# Copyright (C) AB Strakt
# Copyright (C) <PERSON><PERSON><PERSON>
# See LICENSE for details.

"""
pyOpenSSL - A simple wrapper around the OpenSSL library
"""

__all__ = [
    "__author__",
    "__copyright__",
    "__email__",
    "__license__",
    "__summary__",
    "__title__",
    "__uri__",
    "__version__",
]

__version__ = "25.0.0"

__title__ = "pyOpenSSL"
__uri__ = "https://pyopenssl.org/"
__summary__ = "Python wrapper module around the OpenSSL library"
__author__ = "The pyOpenSSL developers"
__email__ = "<EMAIL>"
__license__ = "Apache License, Version 2.0"
__copyright__ = f"Copyright 2001-2025 {__author__}"
