r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Marketplace
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, Optional
from twilio.base import values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class ReferralConversionInstance(InstanceResource):
    """
    :ivar converted_account_sid:
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.converted_account_sid: Optional[str] = payload.get("converted_account_sid")

        self._context: Optional[ReferralConversionContext] = None

    @property
    def _proxy(self) -> "ReferralConversionContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ReferralConversionContext for this ReferralConversionInstance
        """
        if self._context is None:
            self._context = ReferralConversionContext(
                self._version,
            )
        return self._context

    def create(
        self, create_referral_conversion_request: CreateReferralConversionRequest
    ) -> "ReferralConversionInstance":
        """
        Create the ReferralConversionInstance

        :param create_referral_conversion_request:

        :returns: The created ReferralConversionInstance
        """
        return self._proxy.create(
            create_referral_conversion_request,
        )

    async def create_async(
        self, create_referral_conversion_request: CreateReferralConversionRequest
    ) -> "ReferralConversionInstance":
        """
        Asynchronous coroutine to create the ReferralConversionInstance

        :param create_referral_conversion_request:

        :returns: The created ReferralConversionInstance
        """
        return await self._proxy.create_async(
            create_referral_conversion_request,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Marketplace.V1.ReferralConversionInstance>"


class ReferralConversionContext(InstanceContext):

    def __init__(self, version: Version):
        """
        Initialize the ReferralConversionContext

        :param version: Version that contains the resource
        """
        super().__init__(version)

        self._uri = "/ReferralConversion"

    def create(
        self, create_referral_conversion_request: CreateReferralConversionRequest
    ) -> ReferralConversionInstance:
        """
        Create the ReferralConversionInstance

        :param create_referral_conversion_request:

        :returns: The created ReferralConversionInstance
        """
        data = values.of(
            {
                "CreateReferralConversionRequest": create_referral_conversion_request,
            }
        )

        payload = self._version.create(method="POST", uri=self._uri, data=data)

        return ReferralConversionInstance(self._version, payload)

    async def create_async(
        self, create_referral_conversion_request: CreateReferralConversionRequest
    ) -> ReferralConversionInstance:
        """
        Asynchronous coroutine to create the ReferralConversionInstance

        :param create_referral_conversion_request:

        :returns: The created ReferralConversionInstance
        """
        data = values.of(
            {
                "CreateReferralConversionRequest": create_referral_conversion_request,
            }
        )

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data
        )

        return ReferralConversionInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Marketplace.V1.ReferralConversionContext>"


class ReferralConversionList(ListResource):

    class CreateReferralConversionRequest(object):
        """
        :ivar referral_account_sid:
        """

        def __init__(self, payload: Dict[str, Any]):

            self.referral_account_sid: Optional[str] = payload.get(
                "referral_account_sid"
            )

        def to_dict(self):
            return {
                "referral_account_sid": self.referral_account_sid,
            }

    def __init__(self, version: Version):
        """
        Initialize the ReferralConversionList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self) -> ReferralConversionContext:
        """
        Constructs a ReferralConversionContext

        """
        return ReferralConversionContext(self._version)

    def __call__(self) -> ReferralConversionContext:
        """
        Constructs a ReferralConversionContext

        """
        return ReferralConversionContext(self._version)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Marketplace.V1.ReferralConversionList>"
