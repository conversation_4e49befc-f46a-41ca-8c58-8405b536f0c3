r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Intelligence
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, Optional
from twilio.base import values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class OperatorAttachmentInstance(InstanceResource):
    """
    :ivar service_sid: The unique SID identifier of the Service.
    :ivar operator_sid: The unique SID identifier of the Operator.
    :ivar url: The URL of this resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        service_sid: Optional[str] = None,
        operator_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.service_sid: Optional[str] = payload.get("service_sid")
        self.operator_sid: Optional[str] = payload.get("operator_sid")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "service_sid": service_sid or self.service_sid,
            "operator_sid": operator_sid or self.operator_sid,
        }
        self._context: Optional[OperatorAttachmentContext] = None

    @property
    def _proxy(self) -> "OperatorAttachmentContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: OperatorAttachmentContext for this OperatorAttachmentInstance
        """
        if self._context is None:
            self._context = OperatorAttachmentContext(
                self._version,
                service_sid=self._solution["service_sid"],
                operator_sid=self._solution["operator_sid"],
            )
        return self._context

    def create(self) -> "OperatorAttachmentInstance":
        """
        Create the OperatorAttachmentInstance


        :returns: The created OperatorAttachmentInstance
        """
        return self._proxy.create()

    async def create_async(self) -> "OperatorAttachmentInstance":
        """
        Asynchronous coroutine to create the OperatorAttachmentInstance


        :returns: The created OperatorAttachmentInstance
        """
        return await self._proxy.create_async()

    def delete(self) -> bool:
        """
        Deletes the OperatorAttachmentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the OperatorAttachmentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Intelligence.V2.OperatorAttachmentInstance {}>".format(context)


class OperatorAttachmentContext(InstanceContext):

    def __init__(self, version: Version, service_sid: str, operator_sid: str):
        """
        Initialize the OperatorAttachmentContext

        :param version: Version that contains the resource
        :param service_sid: The unique SID identifier of the Service.
        :param operator_sid: The unique SID identifier of the Operator. Allows both Custom and Pre-built Operators.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "service_sid": service_sid,
            "operator_sid": operator_sid,
        }
        self._uri = "/Services/{service_sid}/Operators/{operator_sid}".format(
            **self._solution
        )

    def create(self) -> OperatorAttachmentInstance:
        """
        Create the OperatorAttachmentInstance


        :returns: The created OperatorAttachmentInstance
        """
        data = values.of({})

        payload = self._version.create(method="POST", uri=self._uri, data=data)

        return OperatorAttachmentInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            operator_sid=self._solution["operator_sid"],
        )

    async def create_async(self) -> OperatorAttachmentInstance:
        """
        Asynchronous coroutine to create the OperatorAttachmentInstance


        :returns: The created OperatorAttachmentInstance
        """
        data = values.of({})

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data
        )

        return OperatorAttachmentInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            operator_sid=self._solution["operator_sid"],
        )

    def delete(self) -> bool:
        """
        Deletes the OperatorAttachmentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._version.delete(
            method="DELETE",
            uri=self._uri,
        )

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the OperatorAttachmentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._version.delete_async(
            method="DELETE",
            uri=self._uri,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Intelligence.V2.OperatorAttachmentContext {}>".format(context)


class OperatorAttachmentList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the OperatorAttachmentList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, service_sid: str, operator_sid: str) -> OperatorAttachmentContext:
        """
        Constructs a OperatorAttachmentContext

        :param service_sid: The unique SID identifier of the Service.
        :param operator_sid: The unique SID identifier of the Operator. Allows both Custom and Pre-built Operators.
        """
        return OperatorAttachmentContext(
            self._version, service_sid=service_sid, operator_sid=operator_sid
        )

    def __call__(
        self, service_sid: str, operator_sid: str
    ) -> OperatorAttachmentContext:
        """
        Constructs a OperatorAttachmentContext

        :param service_sid: The unique SID identifier of the Service.
        :param operator_sid: The unique SID identifier of the Operator. Allows both Custom and Pre-built Operators.
        """
        return OperatorAttachmentContext(
            self._version, service_sid=service_sid, operator_sid=operator_sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Intelligence.V2.OperatorAttachmentList>"
