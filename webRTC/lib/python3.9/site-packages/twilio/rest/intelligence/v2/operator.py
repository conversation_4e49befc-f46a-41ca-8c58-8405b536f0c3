r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Intelligence
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class OperatorInstance(InstanceResource):

    class Availability(object):
        INTERNAL = "internal"
        BETA = "beta"
        PUBLIC = "public"
        RETIRED = "retired"

    """
    :ivar account_sid: The unique SID identifier of the Account the Operator belongs to.
    :ivar sid: A 34 character string that uniquely identifies this Operator.
    :ivar friendly_name: A human-readable name of this resource, up to 64 characters.
    :ivar description: A human-readable description of this resource, longer than the friendly name.
    :ivar author: The creator of the Operator. Either Twilio or the creating Account.
    :ivar operator_type: Operator Type for this Operator. References an existing Operator Type resource.
    :ivar version: Numeric Operator version. Incremented with each update on the resource, used to ensure integrity when updating the Operator.
    :ivar availability: 
    :ivar config: Operator configuration, following the schema defined by the Operator Type. Only available on Custom Operators created by the Account.
    :ivar date_created: The date that this Operator was created, given in ISO 8601 format.
    :ivar date_updated: The date that this Operator was updated, given in ISO 8601 format.
    :ivar url: The URL of this resource.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.sid: Optional[str] = payload.get("sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.description: Optional[str] = payload.get("description")
        self.author: Optional[str] = payload.get("author")
        self.operator_type: Optional[str] = payload.get("operator_type")
        self.version: Optional[int] = deserialize.integer(payload.get("version"))
        self.availability: Optional["OperatorInstance.Availability"] = payload.get(
            "availability"
        )
        self.config: Optional[Dict[str, object]] = payload.get("config")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[OperatorContext] = None

    @property
    def _proxy(self) -> "OperatorContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: OperatorContext for this OperatorInstance
        """
        if self._context is None:
            self._context = OperatorContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "OperatorInstance":
        """
        Fetch the OperatorInstance


        :returns: The fetched OperatorInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "OperatorInstance":
        """
        Asynchronous coroutine to fetch the OperatorInstance


        :returns: The fetched OperatorInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Intelligence.V2.OperatorInstance {}>".format(context)


class OperatorContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the OperatorContext

        :param version: Version that contains the resource
        :param sid: A 34 character string that uniquely identifies this Operator.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Operators/{sid}".format(**self._solution)

    def fetch(self) -> OperatorInstance:
        """
        Fetch the OperatorInstance


        :returns: The fetched OperatorInstance
        """

        payload = self._version.fetch(
            method="GET",
            uri=self._uri,
        )

        return OperatorInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> OperatorInstance:
        """
        Asynchronous coroutine to fetch the OperatorInstance


        :returns: The fetched OperatorInstance
        """

        payload = await self._version.fetch_async(
            method="GET",
            uri=self._uri,
        )

        return OperatorInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Intelligence.V2.OperatorContext {}>".format(context)


class OperatorPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> OperatorInstance:
        """
        Build an instance of OperatorInstance

        :param payload: Payload response from the API
        """
        return OperatorInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Intelligence.V2.OperatorPage>"


class OperatorList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the OperatorList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Operators"

    def stream(
        self,
        availability: Union["OperatorInstance.Availability", object] = values.unset,
        language_code: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[OperatorInstance]:
        """
        Streams OperatorInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;OperatorInstance.Availability&quot; availability: Returns Operators with the provided availability type. Possible values: internal, beta, public, retired.
        :param str language_code: Returns Operators that support the provided language code.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            availability=availability,
            language_code=language_code,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        availability: Union["OperatorInstance.Availability", object] = values.unset,
        language_code: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[OperatorInstance]:
        """
        Asynchronously streams OperatorInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param &quot;OperatorInstance.Availability&quot; availability: Returns Operators with the provided availability type. Possible values: internal, beta, public, retired.
        :param str language_code: Returns Operators that support the provided language code.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            availability=availability,
            language_code=language_code,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        availability: Union["OperatorInstance.Availability", object] = values.unset,
        language_code: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[OperatorInstance]:
        """
        Lists OperatorInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;OperatorInstance.Availability&quot; availability: Returns Operators with the provided availability type. Possible values: internal, beta, public, retired.
        :param str language_code: Returns Operators that support the provided language code.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                availability=availability,
                language_code=language_code,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        availability: Union["OperatorInstance.Availability", object] = values.unset,
        language_code: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[OperatorInstance]:
        """
        Asynchronously lists OperatorInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param &quot;OperatorInstance.Availability&quot; availability: Returns Operators with the provided availability type. Possible values: internal, beta, public, retired.
        :param str language_code: Returns Operators that support the provided language code.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                availability=availability,
                language_code=language_code,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        availability: Union["OperatorInstance.Availability", object] = values.unset,
        language_code: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> OperatorPage:
        """
        Retrieve a single page of OperatorInstance records from the API.
        Request is executed immediately

        :param availability: Returns Operators with the provided availability type. Possible values: internal, beta, public, retired.
        :param language_code: Returns Operators that support the provided language code.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of OperatorInstance
        """
        data = values.of(
            {
                "Availability": availability,
                "LanguageCode": language_code,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = self._version.page(method="GET", uri=self._uri, params=data)
        return OperatorPage(self._version, response)

    async def page_async(
        self,
        availability: Union["OperatorInstance.Availability", object] = values.unset,
        language_code: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> OperatorPage:
        """
        Asynchronously retrieve a single page of OperatorInstance records from the API.
        Request is executed immediately

        :param availability: Returns Operators with the provided availability type. Possible values: internal, beta, public, retired.
        :param language_code: Returns Operators that support the provided language code.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of OperatorInstance
        """
        data = values.of(
            {
                "Availability": availability,
                "LanguageCode": language_code,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data
        )
        return OperatorPage(self._version, response)

    def get_page(self, target_url: str) -> OperatorPage:
        """
        Retrieve a specific page of OperatorInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of OperatorInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return OperatorPage(self._version, response)

    async def get_page_async(self, target_url: str) -> OperatorPage:
        """
        Asynchronously retrieve a specific page of OperatorInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of OperatorInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return OperatorPage(self._version, response)

    def get(self, sid: str) -> OperatorContext:
        """
        Constructs a OperatorContext

        :param sid: A 34 character string that uniquely identifies this Operator.
        """
        return OperatorContext(self._version, sid=sid)

    def __call__(self, sid: str) -> OperatorContext:
        """
        Constructs a OperatorContext

        :param sid: A 34 character string that uniquely identifies this Operator.
        """
        return OperatorContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Intelligence.V2.OperatorList>"
