import { CheckCircle } from "lucide-react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import ConversationSelector from "./ConversationSelector";

interface PricingPlan {
  name: string;
  price: number | string;
  period: string;
  description: string;
  features: string[];
  popular?: boolean;
  comingSoon?: boolean;
  cta: string;
}

interface PricingCardProps {
  plan: PricingPlan;
  isYearly: boolean;
  professionalConversations: string;
  onConversationChange: (value: string) => void;
  onContactSales: () => void;
  onSignup: () => void;
}

const PricingCard = ({ 
  plan, 
  isYearly, 
  professionalConversations, 
  onConversationChange,
  onContactSales,
  onSignup 
}: PricingCardProps) => {
  const getPrice = (basePrice: number | string, planName?: string) => {
    if (basePrice === 0) return "Free";
    if (typeof basePrice === "string") return basePrice;
    
    // Special handling for Professional plan with conversation tiers
    if (planName === "Professional") {
      const conversations = parseInt(professionalConversations);
      
      // Special pricing for 60,000 conversations
      if (conversations === 60000) {
        const totalPrice = 2499; // Fixed price for 60k conversations
        return isYearly ? Math.round(totalPrice * 12 * 0.9) : totalPrice;
      }
      
      const additionalThousands = Math.max(0, (conversations - 2000) / 1000);
      const additionalCost = additionalThousands * 50;
      const totalPrice = basePrice + additionalCost;
      return isYearly ? Math.round(totalPrice * 12 * 0.9) : totalPrice;
    }
    
    return isYearly ? Math.round(basePrice * 12 * 0.9) : basePrice;
  };

  const getMonthlyPrice = (basePrice: number | string, planName?: string) => {
    if (basePrice === 0) return 0;
    if (typeof basePrice === "string") return 0;
    
    // Special handling for Professional plan with conversation tiers
    if (planName === "Professional") {
      const conversations = parseInt(professionalConversations);
      
      // Special pricing for 60,000 conversations
      if (conversations === 60000) {
        return 2499; // Fixed price for 60k conversations
      }
      
      const additionalThousands = Math.max(0, (conversations - 2000) / 1000);
      const additionalCost = additionalThousands * 50;
      return basePrice + additionalCost;
    }
    
    return basePrice;
  };

  const getPeriod = () => {
    return isYearly ? "per year" : "per month";
  };

  const getConversationFeature = (planName: string) => {
    if (planName === "Professional") {
      const conversations = parseInt(professionalConversations);
      const displayConversations = isYearly ? conversations * 12 : conversations;
      const period = isYearly ? "year" : "month";
      return `Up to ${displayConversations.toLocaleString()} conversations/${period}`;
    }
    return null;
  };

  return (
    <Card 
      className={`relative ${
        plan.popular 
          ? 'bg-white/5 border-2 border-purple-400 shadow-lg shadow-purple-500/20' 
          : 'bg-gray-800/50 border-gray-700'
      } ${plan.comingSoon ? 'opacity-75' : ''}`}
    >
      {plan.popular && (
        <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-purple-500 text-white font-semibold px-4 py-1 text-sm border-2 border-white shadow-lg">
          Most Popular
        </Badge>
      )}
      <CardHeader>
        <CardTitle className={`text-xl font-bold ${
          plan.popular ? 'text-white' : 'text-white'
        }`}>{plan.name}</CardTitle>
        
        {/* Conversation Selector for Professional Plan */}
        {plan.name === "Professional" && (
          <ConversationSelector
            professionalConversations={professionalConversations}
            onConversationChange={onConversationChange}
            isYearly={isYearly}
          />
        )}

        <div className="space-y-1">
          <div className={`text-3xl font-bold ${
            plan.popular ? 'text-white' : 'text-white'
          }`}>
            {plan.price === "Custom" || plan.price === 0 ? (
              plan.price === 0 ? "Free" : "Custom"
            ) : (
              <>
                ${getPrice(plan.price, plan.name)}
                {isYearly && plan.price !== "Custom" && plan.price !== 0 && typeof plan.price === "number" && (
                  <div className="text-sm text-gray-400 line-through">
                    ${getMonthlyPrice(plan.price, plan.name) * 12}
                  </div>
                )}
              </>
            )}
          </div>
          <div className={`text-sm font-medium ${
            plan.popular ? 'text-gray-200' : 'text-gray-300'
          }`}>
            {isYearly ? "per year" : "per month"}
          </div>
        </div>
        <CardDescription className={`font-medium ${
          plan.popular ? 'text-gray-100' : 'text-gray-200'
        }`}>{plan.description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <ul className="space-y-3">
          {plan.features.map((feature, index) => {
            // Replace the conversation feature for Professional plan
            const displayFeature = feature.includes("Up to 2,000 conversations/month") && plan.name === "Professional" 
              ? getConversationFeature(plan.name) 
              : feature;
            
            return (
              <li key={index} className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                <span className={`font-medium ${
                  plan.popular ? 'text-gray-100' : 'text-gray-200'
                }`}>{displayFeature}</span>
              </li>
            );
          })}
        </ul>
        <Button 
          className={`w-full font-semibold ${
            plan.popular 
              ? 'bg-purple-500 hover:bg-purple-600 text-white border-2 border-purple-400' 
              : 'bg-gray-700 hover:bg-gray-600 text-white'
          }`}
          disabled={plan.comingSoon}
          onClick={plan.cta === "Contact Sales" ? onContactSales : onSignup}
        >
          {plan.cta}
        </Button>
      </CardContent>
    </Card>
  );
};

export default PricingCard;
