import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { TbDeviceDesktopAnalytics } from "react-icons/tb";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  useEffect(() => {
    const token = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY);
    setIsAuthenticated(!!token);
  }, []);

  // Handle section scrolling when hash changes
  useEffect(() => {
    if (location.hash) {
      const element = document.getElementById(location.hash.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [location.hash]);

  // Helper function to handle section navigation
  const handleSectionClick = (section: string) => {
    if (location.pathname === '/') {
      // If we're on the home page, just scroll to the section
      const element = document.getElementById(section);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      // If we're on another page, navigate to home with hash
      navigate(`/#${section}`);
    }
  };

  return (
    <header className="relative z-50 bg-white/10 backdrop-blur-md border-b border-white/20 h-35">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-3">
              {/* Glasses Logo */}
              <div className="relative flex flex-col items-start">
                <div className="h-12 flex items-center">
                  <img
                    src="/headerlogo.png"
                    alt="QuickTalk Logo"
                    className="h-[100px] object-contain"
                    style={{ display: 'block' }}
                  />
                </div>
                {/* <span className="text-xs text-white  mt-1 ml-[130px]">Powered by Najoomi Technologies</span> */}
              </div>
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link to="/" className="text-white hover:text-purple-200 transition-colors">
              Home
            </Link>
            <Link to="/call-solutions" className="text-white hover:text-purple-200 transition-colors">
              Call Solutions
            </Link>
            <button 
              onClick={() => handleSectionClick('features')} 
              className="text-white hover:text-purple-200 transition-colors"
            >
              Features
            </button>
            <button 
              onClick={() => handleSectionClick('how-it-works')} 
              className="text-white hover:text-purple-200 transition-colors"
            >
              How it Works
            </button>
            <button 
              onClick={() => handleSectionClick('use-cases')} 
              className="text-white hover:text-purple-200 transition-colors"
            >
              Use Cases
            </button>
            <button 
              onClick={() => handleSectionClick('pricing')} 
              className="text-white hover:text-purple-200 transition-colors"
            >
              Pricing
            </button>
            <Link to="/contact" className="text-white hover:text-purple-200 transition-colors">
              Contact
            </Link>
          </nav>

          {/* Desktop CTA Buttons */}
          <div className="hidden md:flex space-x-4">
            {isAuthenticated ? (
               <Link to="/dashboard" onClick={() => setIsMenuOpen(false)}>
                   <Button className="w-full  px-14 bg-white text-purple-600 hover:bg-gray-100">
                    Dashboard
                    </Button>
                  </Link>
            ) : (
              <>
                <Link to="/login">
                  <Button variant="ghost" className="text-white hover:bg-white/10">
                    Sign In
                  </Button>
                </Link>
                <Link to="/signup">
                  <Button className="bg-white text-purple-600 hover:bg-gray-100">
                    Get Started Free
                  </Button>
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden text-white"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white/10 backdrop-blur-md rounded-lg mt-2">
              <Link 
                to="/" 
                className="block px-3 py-2 text-white hover:text-purple-200 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>
              <Link 
                to="/call-solutions" 
                className="block px-3 py-2 text-white hover:text-purple-200 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Call Solutions
              </Link>
              <button 
                onClick={() => {
                  handleSectionClick('features');
                  setIsMenuOpen(false);
                }}
                className="block w-full text-left px-3 py-2 text-white hover:text-purple-200 transition-colors"
              >
                Features
              </button>
              <button 
                onClick={() => {
                  handleSectionClick('how-it-works');
                  setIsMenuOpen(false);
                }}
                className="block w-full text-left px-3 py-2 text-white hover:text-purple-200 transition-colors"
              >
                How it Works
              </button>
              <button 
                onClick={() => {
                  handleSectionClick('use-cases');
                  setIsMenuOpen(false);
                }}
                className="block w-full text-left px-3 py-2 text-white hover:text-purple-200 transition-colors"
              >
                Use Cases
              </button>
              <button 
                onClick={() => {
                  handleSectionClick('pricing');
                  setIsMenuOpen(false);
                }}
                className="block w-full text-left px-3 py-2 text-white hover:text-purple-200 transition-colors"
              >
                Pricing
              </button>
              <Link 
                to="/contact" 
                className="block px-3 py-2 text-white hover:text-purple-200 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Contact
              </Link>
              <div className="px-3 py-2 space-y-2">
                {isAuthenticated ? (
                  <Link to="/dashboard" onClick={() => setIsMenuOpen(false)}>
                   <Button className="w-full bg-white text-purple-600 hover:bg-gray-100">
                    Dashboard
                    </Button>
                  </Link>
                ) : (
                  <>
                    <Link to="/login" onClick={() => setIsMenuOpen(false)}>
                      <Button variant="ghost" className="w-full text-white hover:bg-white/10">
                        Sign In
                      </Button>
                    </Link>
                    <Link to="/signup" onClick={() => setIsMenuOpen(false)}>
                      <Button className="w-full bg-white text-purple-600 hover:bg-gray-100">
                        Get Started Free
                      </Button>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
