import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

const HowItWorksSection = () => {
  return (
    <section id="how-it-works" className="container mx-auto px-4 py-20">
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-white mb-4">How QuickTalk Works</h2>
        <p className="text-xl text-gray-200 max-w-2xl mx-auto">
          Get your AI-powered contact center running in three simple steps. No coding required.
        </p>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        <Card className="bg-gray-800/50 border-gray-700">
          <CardHeader>
            <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg flex items-center justify-center mb-4">
              <span className="text-white font-bold text-lg">1</span>
            </div>
            <CardTitle className="text-white text-xl">Import Your Knowledge</CardTitle>
            <CardDescription className="text-gray-300">
              Upload documents (PDFs, Word docs), connect your APIs, or provide website URLs. Our AI will automatically extract and organize the information.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-gray-300">
              <li>• Document parsing</li>
              <li>• API integration</li>
              <li>• Website scraping</li>
              <li>• Data validation</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/50 border-gray-700">
          <CardHeader>
            <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg flex items-center justify-center mb-4">
              <span className="text-white font-bold text-lg">2</span>
            </div>
            <CardTitle className="text-white text-xl">Configure Your Agent</CardTitle>
            <CardDescription className="text-gray-300">
              Customize your AI agent's branding, colors, and behavior. Set up conversation flows and define how it should handle different scenarios.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-gray-300">
              <li>• Brand customization</li>
              <li>• Color schemes</li>
              <li>• Response templates</li>
              <li>• Channel preferences</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/50 border-gray-700">
          <CardHeader>
            <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg flex items-center justify-center mb-4">
              <span className="text-white font-bold text-lg">3</span>
            </div>
            <CardTitle className="text-white text-xl">Deploy & Monitor</CardTitle>
            <CardDescription className="text-gray-300">
              Launch your AI agent across all channels instantly. Monitor performance, gather insights, and continuously improve based on real interactions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-gray-300">
              <li>• One-click deployment</li>
              <li>• Real-time monitoring</li>
              <li>• Performance analytics</li>
              <li>• Live agent transfer</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default HowItWorksSection;
