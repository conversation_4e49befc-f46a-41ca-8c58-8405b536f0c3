import { Phone, PhoneCall, Calendar, Target, Mic, HeadphonesIcon, Globe } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const VoiceSolutionsSection = () => {
  return (
    <section className="container mx-auto px-4 py-20">
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-white mb-4">Call Solutions</h2>
        <p className="text-xl text-gray-200 max-w-3xl mx-auto">
          Complete call handling and automated campaigns to connect with your customers through voice and messaging
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-12 items-start">
        {/* Inbound Call Handling */}
        <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
          <CardHeader>
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                <Phone className="w-6 h-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-white text-xl">Inbound Call Management</CardTitle>
                <p className="text-gray-300 text-sm">AI-powered customer service calls</p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-gray-200">
                <HeadphonesIcon className="w-5 h-5 text-blue-400" />
                <span>24/7 AI call handling</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-200">
                <Mic className="w-5 h-5 text-blue-400" />
                <span>Natural conversation flow</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-200">
                <Globe className="w-5 h-5 text-blue-400" />
                <span>Multilingual support (50+ languages)</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-200">
                <Target className="w-5 h-5 text-blue-400" />
                <span>Smart escalation to live agents</span>
              </div>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
              <p className="text-gray-300 text-sm italic">
                "Handle customer inquiries, support requests, and information gathering automatically with intelligent voice AI in any language"
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Outbound Campaigns */}
        <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
          <CardHeader>
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
                <PhoneCall className="w-6 h-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-white text-xl">Outbound Campaigns</CardTitle>
                <p className="text-gray-300 text-sm">Voice calls & WhatsApp messaging</p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="text-gray-200">
                <h4 className="font-medium mb-2">Campaign Setup Requires:</h4>
                <ul className="space-y-1 text-sm text-gray-300">
                  <li>• Product/Service Details</li>
                  <li>• Contact Lists & Phone Numbers</li>
                  <li>• Campaign Mission (e.g., "Book a Demo")</li>
                  <li>• Channel: Voice Calls or WhatsApp</li>
                  <li>• Language Preference</li>
                </ul>
              </div>
              <div className="flex items-center space-x-3 text-gray-200">
                <Globe className="w-5 h-5 text-green-400" />
                <span>Multilingual campaign support</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-200">
                <Calendar className="w-5 h-5 text-green-400" />
                <span>Smart callback scheduling</span>
              </div>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
              <p className="text-gray-300 text-sm italic">
                "When customers say 'call me later' or 'message me later', the system automatically schedules follow-ups via their preferred channel"
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Campaign Flow */}
      <div className="mt-16 bg-gray-800/50 rounded-2xl p-8 border border-gray-700 backdrop-blur-lg">
        <h3 className="text-2xl font-bold text-white text-center mb-8">Campaign Workflow</h3>
        <div className="flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0 md:space-x-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-4 mx-auto">
              <span className="text-white font-bold text-lg">1</span>
            </div>
            <h4 className="text-white font-medium mb-2">Setup Campaign</h4>
            <p className="text-gray-300 text-sm">Configure product details, contact lists, channel preference, language, and mission objectives</p>
          </div>
          
          <div className="hidden md:block w-12 h-px bg-gradient-to-r from-purple-400 to-pink-400"></div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-4 mx-auto">
              <span className="text-white font-bold text-lg">2</span>
            </div>
            <h4 className="text-white font-medium mb-2">AI Outreach</h4>
            <p className="text-gray-300 text-sm">Automated calls or WhatsApp messages with natural conversation and mission execution</p>
          </div>
          
          <div className="hidden md:block w-12 h-px bg-gradient-to-r from-purple-400 to-pink-400"></div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-4 mx-auto">
              <span className="text-white font-bold text-lg">3</span>
            </div>
            <h4 className="text-white font-medium mb-2">Smart Scheduling</h4>
            <p className="text-gray-300 text-sm">Automatic callback scheduling and follow-up management across preferred channels</p>
          </div>
        </div>
      </div>

      <div className="text-center mt-12">
        <Link to="/call-solutions">
          <Button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-3 text-lg">
            Learn More About Call Solutions
          </Button>
        </Link>
      </div>
    </section>
  );
};

export default VoiceSolutionsSection;
