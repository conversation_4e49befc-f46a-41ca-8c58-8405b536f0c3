
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { pricingPlans } from "@/constants";
import PricingToggle from "./PricingToggle";
import PricingCard from "./PricingCard";

const PricingSection = () => {
  const navigate = useNavigate();
  const [isYearly, setIsYearly] = useState(false);
  const [professionalConversations, setProfessionalConversations] = useState("2000");

  const handleContactSales = () => {
    navigate('/contact');
  };

  const handleSignup = () => {
    console.log("Navigating to signup page from pricing");
    navigate('/signup');
  };

  return (
    <section id="pricing" className="container mx-auto px-4 py-20">
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-white mb-4">Simple, Transparent Pricing</h2>
        <p className="text-xl text-gray-200 mb-8">Choose the plan that fits your business needs</p>
        
        <PricingToggle 
          isYearly={isYearly} 
          onToggle={() => setIsYearly(!isYearly)} 
        />
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
        {pricingPlans.map((plan) => (
          <PricingCard
            key={plan.name}
            plan={plan}
            isYearly={isYearly}
            professionalConversations={professionalConversations}
            onConversationChange={setProfessionalConversations}
            onContactSales={handleContactSales}
            onSignup={handleSignup}
          />
        ))}
      </div>
    </section>
  );
};

export default PricingSection;
