
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { features } from "@/constants";
import DocumentsView from "@/components/features/DocumentsView";
import CustomizationView from "@/components/features/CustomizationView";
import IntegrationView from "@/components/features/IntegrationView";
import AnalyticsView from "@/components/features/AnalyticsView";
import AgentsView from "@/components/features/AgentsView";
import DefaultChatView from "@/components/features/DefaultChatView";
import MultilingualView from "@/components/features/MultilingualView";
import LeadGenerationView from "@/components/features/LeadGenerationView";

const FeaturesSection = () => {
  const [activeFeature, setActiveFeature] = useState("documents");

  const getFeatureVisualization = () => {
    switch (activeFeature) {
      case "documents":
        return <DocumentsView />;
      case "customization":
        return <CustomizationView />;
      case "integration":
        return <IntegrationView />;
      case "multilingual":
        return <MultilingualView />;
      case "analytics":
        return <AnalyticsView />;
      case "agents":
        return <AgentsView />;
      case "lead-generation":
        return <LeadGenerationView />;
      default:
        return <DefaultChatView />;
    }
  };

  // Override the title for the documents feature to reflect the new comprehensive name
  const getFeatureTitle = (feature: any) => {
    if (feature.id === "documents") {
      return "Knowledge Intelligence";
    }
    return feature.title;
  };

  const getFeatureDescription = (feature: any) => {
    if (feature.id === "documents") {
      return "Process documents, websites, and APIs with advanced AI";
    }
    return feature.description;
  };

  return (
    <section id="features" className="container mx-auto px-4 py-20">
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-white mb-4">Everything You Need</h2>
        <p className="text-xl text-gray-200 max-w-2xl mx-auto">
          From document processing to lead generation and live agent handoff, QuickTalk provides a complete solution for modern customer service.
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-12 items-center">
        <div className="space-y-6">
          {features.map((feature) => (
            <Card 
              key={feature.id}
              className={`cursor-pointer transition-all duration-300 ${
                activeFeature === feature.id 
                  ? 'bg-purple-500/20 border-purple-400' 
                  : 'bg-gray-800/50 border-gray-700 hover:border-purple-400/50'
              }`}
              onClick={() => setActiveFeature(feature.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg flex items-center justify-center">
                    <feature.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white">{getFeatureTitle(feature)}</CardTitle>
                    <CardDescription className="text-gray-200">{getFeatureDescription(feature)}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              {activeFeature === feature.id && (
                <CardContent className="pt-0">
                  <p className="text-gray-200">{feature.details}</p>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
        
        <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl p-8 border border-purple-400/30">
          {getFeatureVisualization()}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
