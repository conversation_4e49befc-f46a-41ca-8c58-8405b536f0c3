
import { Badge } from "@/components/ui/badge";

interface PricingToggleProps {
  isYearly: boolean;
  onToggle: () => void;
}

const PricingToggle = ({ isYearly, onToggle }: PricingToggleProps) => {
  return (
    <div className="flex items-center justify-center mb-8">
      <span className={`text-lg font-semibold mr-3 ${!isYearly ? 'text-white' : 'text-gray-400'}`}>Monthly</span>
      <button
        onClick={onToggle}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          isYearly ? 'bg-purple-500' : 'bg-gray-600'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            isYearly ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
      <span className={`text-lg font-semibold ml-3 ${isYearly ? 'text-white' : 'text-gray-400'}`}>
        Yearly 
        <Badge className="ml-2 bg-green-500 text-white text-xs">Save 10%</Badge>
      </span>
    </div>
  );
};

export default PricingToggle;
