import { Card, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Heart, Building, Phone, ShoppingBag, Truck, Briefcase } from "lucide-react";

const useCases = [
  {
    id: "healthcare",
    icon: Heart,
    title: "Healthcare & Hospitals",
    description: "Revolutionize patient care with AI-powered appointment booking and 24/7 support",
    image: "/lovable-uploads/e6804bcd-8be7-4018-a277-2da12acb1140.png",
    features: [
      "Patient Queries & Appointments: Handle bookings, prescription refills, test reports, and general inquiries without staff intervention",
      "After-Hours Support: Let QuickTalk provide 24/7 patient assistance—even during off-hours"
    ],
    benefits: "Reduce administrative burden, improve patient satisfaction, and ensure round-the-clock healthcare support"
  },
  {
    id: "hospitality",
    icon: Building,
    title: "Hotels & Hospitality",
    description: "Enhance guest experience with intelligent reservation management and concierge services",
    image: "/lovable-uploads/d5609cee-b9b6-4445-bf7c-3f26f650142b.png",
    features: [
      "Reservation & Room Service: Guests can book, inquire, or request services via WhatsApp or webchat",
      "Multi-language Concierge: Built-in multilingual support offers local and international guests a truly personalized experience"
    ],
    benefits: "Streamline operations, boost guest satisfaction, and provide seamless multilingual support"
  },
  {
    id: "ecommerce",
    icon: ShoppingBag,
    title: "E-commerce & Retail",
    description: "Transform customer support with intelligent order tracking and product assistance",
    image: "/lovable-uploads/2f863830-eae7-4772-9513-3c1959487e36.png",
    features: [
      "Order Management: Automated order tracking, complaint registration, and delivery status updates",
      "Product Support: Instant product information, technical support, and purchase guidance"
    ],
    benefits: "Increase conversion rates, reduce support costs, and enhance customer loyalty"
  },
  {
    id: "telecommunications",
    icon: Phone,
    title: "Telecommunications",
    description: "Provide instant technical support and service information to customers",
    image: "/Telecom.jpg",
    features: [
      "Technical Support: Automated troubleshooting, service plan information, and connectivity assistance",
      "Billing & Account Management: Handle billing inquiries, plan upgrades, and account modifications"
    ],
    benefits: "Reduce call center load, improve response times, and enhance customer experience"
  },
  {
    id: "logistics",
    icon: Truck,
    title: "Logistics & Shipping",
    description: "Optimize supply chain operations with real-time tracking and automated customer support",
    image: "/Logisitc.jpg",
    features: [
      "Shipment Tracking: Real-time package tracking, delivery updates, and route optimization information",
      "Customer Support: Handle delivery scheduling, address changes, and shipment inquiries automatically"
    ],
    benefits: "Improve delivery efficiency, reduce customer service costs, and enhance supply chain visibility"
  },
  {
    id: "finance",
    icon: Briefcase,
    title: "Financial Services",
    description: "Secure customer support for banking, loans, and financial product inquiries",
    image: "/Financial.jpg",
    features: [
      "Account Services: Balance inquiries, transaction history, and secure account management with MFA",
      "Product Information: Loan applications, investment options, and personalized financial planning guidance"
    ],
    benefits: "Enhance security, improve customer satisfaction, reduce operational costs, and ensure regulatory compliance"
  }
];

const UseCasesSection = () => {
  return (
    <section id="use-cases" className="container mx-auto px-4 py-20">
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold text-white mb-4">
          Transforming Industries with AI-Powered Customer Support
        </h2>
        <p className="text-xl text-gray-200 max-w-3xl mx-auto">
          Discover how businesses across various sectors are revolutionizing their customer experience with QuickTalk's intelligent AI agents. From healthcare to hospitality, see real-world applications that drive results.
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {useCases.map((useCase) => (
          <Card 
            key={useCase.id}
            className="bg-gray-800/50 border-gray-700 hover:border-purple-400/50 transition-all duration-300 group h-full flex flex-col"
          >
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <useCase.icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-white text-lg">{useCase.title}</CardTitle>
                </div>
              </div>
              <CardDescription className="text-gray-200 text-sm">
                {useCase.description}
              </CardDescription>
            </CardHeader>
            
            {useCase.image && (
              <div className="px-6 mb-4 flex-shrink-0">
                <div className="relative w-full h-64 overflow-hidden rounded-lg border border-gray-600">
                  <img 
                    src={useCase.image} 
                    alt={`${useCase.title} interface`}
                    className="w-full h-full object-cover object-center"
                  />
                </div>
              </div>
            )}
            
            <CardContent className="pt-0 flex-1 flex flex-col">
              <div className="space-y-3 mb-4 flex-1">
                {useCase.features.map((feature, index) => (
                  <div key={index} className="text-sm">
                    <div className="text-purple-400 font-medium mb-1">
                      {feature.split(':')[0]}:
                    </div>
                    <div className="text-gray-300 text-xs leading-relaxed pl-2">
                      {feature.split(':')[1]}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="border-t border-gray-600 pt-3 mt-auto">
                <div className="text-xs text-gray-400 mb-2 font-medium">Benefits:</div>
                <div className="text-xs text-gray-300 leading-relaxed">
                  {useCase.benefits}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center mt-12">
        <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl p-8 border border-purple-400/30">
          <h3 className="text-2xl font-bold text-white mb-4">
            Ready to Transform Your Industry?
          </h3>
          <p className="text-gray-200 mb-6 max-w-2xl mx-auto">
            Join hundreds of businesses already using QuickTalk to deliver exceptional customer experiences. Start your free trial and see the difference AI can make.
          </p>
          <Button size="lg" className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-8 py-4 text-lg font-semibold">
            Start Your Free Trial
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default UseCasesSection;
