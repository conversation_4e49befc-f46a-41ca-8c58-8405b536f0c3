
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import Player from "lottie-react";
import aiHeroLottie from "@/assets/ai-hero-lottie.json";

const HeroSection = () => {
  const navigate = useNavigate();

  const handleSignupClick = () => {
    navigate('/signup');
  };

  const handleDemoClick = () => {
    navigate('/contact');
  };

  return (
    <section className="container mx-auto px-4 py-20 text-center">
      <div className="max-w-4xl mx-auto">
        <Badge className="mb-6 bg-purple-500/20 text-purple-200 border-purple-400 font-medium">
          🚀 Autonomous Omnichannel Contact Center
        </Badge>
        <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
          Build Intelligent AI Agents
          <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> in Minutes  
            <span className="inline-block align-middle">
            <Player
              autoplay
              loop
              animationData={aiHeroLottie}
              style={{ height: '7rem', width: '7rem' }}
            />
          </span></span>
        </h1>
        <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto font-medium">
          Upload documents, connect APIs, and integrate websites. Deliver 24/7 omnichannel customer support in 50+ languages while automatically capturing leads through intelligent conversations.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            size="lg"
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-8 py-4 text-lg font-semibold"
            onClick={handleSignupClick}
          >
            Start Building Your AI Agent
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="border-white/50 text-white bg-white/10 hover:bg-white hover:text-gray-900 px-8 py-4 text-lg font-semibold backdrop-blur-sm"
            onClick={handleDemoClick}
          >
            Schedule Demo
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
