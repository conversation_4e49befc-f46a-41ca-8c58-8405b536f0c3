import { MessageSquare, Smartphone, Globe, Send, Phone, Linkedin } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { integrationPlatforms } from "@/constants";

const platformIcons = {
  'WhatsApp': Phone, // Using phone icon for WhatsApp
  'LinkedIn': Linkedin, // Using LinkedIn icon
  'Website': Globe,
  'Mobile App': Smartphone,
  'Messenger': Send,
  'Telegram': Send
};

const IntegrationsSection = () => {
  return (
    <section className="container mx-auto px-4 py-20">
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold text-white mb-4">Works Everywhere</h2>
        <p className="text-xl text-gray-200">Integrate with all your favorite platforms</p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
        {integrationPlatforms.map((platform) => {
          const IconComponent = platformIcons[platform as keyof typeof platformIcons] || MessageSquare;
          return (
            <Card key={platform} className="bg-white/10 border-gray-600 hover:border-purple-400 transition-colors">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg mx-auto mb-3 flex items-center justify-center">
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <div className="text-white font-semibold">{platform}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </section>
  );
};

export default IntegrationsSection;
