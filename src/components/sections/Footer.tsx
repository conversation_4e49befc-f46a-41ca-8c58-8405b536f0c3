import { MessageSquare, Mail, Phone, MapPin } from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
const Footer = () => {
  return (
    <footer className="bg-black/20 backdrop-blur-sm border-t border-white/10">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex flex-col space-y-1">
              <div className="flex items-center space-x-2">
                {/* Glasses Logo - same as header */}
                <div className="relative">
                  <Link to="/" className="flex items-center space-x-3">
                    {/* Glasses Logo */}
                    <div className="relative flex flex-col items-start">
                      <div className="h-12 flex items-center">
                        <img
                          src="/headerlogo.png"
                          alt="QuickTalk Logo"
                          className="h-[100px] object-contain"
                          style={{ display: 'block' }}
                        />
                      </div>
                      {/* <span className="text-xs text-white  mt-1 ml-[130px]">Powered by Najoomi Technologies</span> */}
                    </div>
                  </Link>
                </div>
              </div>
            </div>
            <p className="text-white/80 text-sm leading-relaxed">
              The humanless contact center of the future. Transform your customer service with AI-powered solutions.
            </p>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-4">Product</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-white/70 hover:text-white transition-colors text-sm">Features</a></li>
              <li><a href="#" className="text-white/70 hover:text-white transition-colors text-sm">Integrations</a></li>
              <li><a href="#" className="text-white/70 hover:text-white transition-colors text-sm">Live Agents</a></li>
              <li><a href="#" className="text-white/70 hover:text-white transition-colors text-sm">Analytics</a></li>
            </ul>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-4">Company</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-white/70 hover:text-white transition-colors text-sm">About</a></li>
              <li><a href="#" className="text-white/70 hover:text-white transition-colors text-sm">Careers</a></li>
              <li><Link to="/blogs" className="text-white/70 hover:text-white transition-colors text-sm">Blog</Link></li>
              <li><a href="#" className="text-white/70 hover:text-white transition-colors text-sm">Privacy</a></li>
            </ul>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-4">Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-white/70" />
                <span className="text-white/70 text-sm"><EMAIL></span>
              </li>
              <li className="flex items-center space-x-2">
                <Phone className="w-4 h-4 text-white/70" />
                <span className="text-white/70 text-sm">+92-334-9589089</span>
              </li>
              <li className="flex items-center space-x-2">
                <MapPin className="w-4 h-4 text-white/70" />
                <span className="text-white/70 text-sm">Islamabad, PK</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-white/10 mt-8 pt-8 text-center">
          <p className="text-white/60 text-sm">
            © 2024 QuickTalk. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
