
import { Select, <PERSON>Content, <PERSON>Item, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ConversationSelectorProps {
  professionalConversations: string;
  onConversationChange: (value: string) => void;
  isYearly: boolean;
}

const ConversationSelector = ({ 
  professionalConversations, 
  onConversationChange, 
  isYearly 
}: ConversationSelectorProps) => {
  const conversationOptions = [
    { value: "2000", label: `${isYearly ? (2000 * 12).toLocaleString() : "2,000"} conversations` },
    { value: "3000", label: `${isYearly ? (3000 * 12).toLocaleString() : "3,000"} conversations` },
    { value: "4000", label: `${isYearly ? (4000 * 12).toLocaleString() : "4,000"} conversations` },
    { value: "5000", label: `${isYearly ? (5000 * 12).toLocaleString() : "5,000"} conversations` },
    { value: "10000", label: `${isYearly ? (10000 * 12).toLocaleString() : "10,000"} conversations` },
    { value: "15000", label: `${isYearly ? (15000 * 12).toLocaleString() : "15,000"} conversations` },
    { value: "20000", label: `${isYearly ? (20000 * 12).toLocaleString() : "20,000"} conversations` },
    { value: "60000", label: `${isYearly ? (60000 * 12).toLocaleString() : "60,000"} conversations` },
  ];

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-200 mb-2">
        Select conversation limit:
      </label>
      <Select value={professionalConversations} onValueChange={onConversationChange}>
        <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="bg-gray-700 border-gray-600">
          {conversationOptions.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value}
              className="text-white hover:bg-gray-600"
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default ConversationSelector;
