
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const CTASection = () => {
  const navigate = useNavigate();

  const handleTrialClick = () => {
    console.log("Navigating to signup page");
    navigate('/signup');
  };

  const handleDemoClick = () => {
    navigate('/contact');
  };

  return (
    <section className="py-20 bg-gradient-to-r from-purple-600 to-blue-600">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
          Ready to Transform Your Industry?
        </h2>
        <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
          Join hundreds of businesses already using QuickTalk to deliver exceptional customer experiences. Start your free trial and see the difference AI can make.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            size="lg" 
            className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white px-8 py-4 text-lg font-semibold"
            onClick={handleTrialClick}
          >
            Get Started Free
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
          <Button 
            size="lg" 
            variant="outline" 
            className="border-white/50 text-white bg-white/10 hover:bg-white hover:text-gray-900 px-8 py-4 text-lg font-semibold backdrop-blur-sm"
            onClick={handleDemoClick}
          >
            Schedule Demo
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
