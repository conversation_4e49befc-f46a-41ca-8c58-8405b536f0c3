"use client"

import { useState, useEffect } from "react"
import {
  Button,
  Input,
  Textarea,
  Checkbox,
  Card,
  CardHeader,
  CardBody,
  Typography,
  Select,
  Option,
} from "@material-tailwind/react"
import userPool from "@/lib/userPoolConfig"
import { <PERSON><PERSON><PERSON>oader } from "react-spinners"
import toast from "react-hot-toast"
import { IoMdAdd, IoMdRemove, IoMdSave, IoMdCreate } from "react-icons/io"

export default function SettingsPage({ isTicketOnly }) {
  // Step 1: Function Definition - Collects basic information about the function
  const [functionDefinitionSettings, setFunctionDefinitionSettings] = useState({
    organisation_id: "",
    function_name: isTicketOnly ? "generate_ticket" : "",
    fdescription: "",
    api_link: "",
    api_method: "",
  })

  // Step 2: Function Parameters/Attributes - Collects details about each parameter
  const [functionParams, setFunctionParams] = useState([
    {
      organisation_id: "",
      function_id: "",
      name_of_attribute: "",
      type: "",
      pdescription: "",
      required: false,
      isSaved: false,
    },
  ])

  const [org_id, setOrg_id] = useState()
  const [loading, setLoading] = useState(false)
  const [functionStep, setFunctionStep] = useState(1)
  const [isDefinitionValid, setIsDefinitionValid] = useState(false)
  const [isParameterValid, setIsParameterValid] = useState(false)
  const [selectedParamIndex, setSelectedParamIndex] = useState(0) // Default to first parameter
  const [showInitialForm, setShowInitialForm] = useState(true) // Flag to control initial form visibility
  const [isEditing, setIsEditing] = useState(false) // New state to track if we're editing a saved attribute

  useEffect(() => {
    const user = userPool.getCurrentUser()

    if (!user) {
      console.log("No current user")
      return
    }

    const username = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY)

    user.getSession((err, session) => {
      if (err || !session.isValid()) {
        console.error("Session error or invalid session:", err)
        return
      }

      user.getUserAttributes((err, attributes) => {
        if (err) {
          console.error("Error fetching attributes:", err)
          return
        }
        if (!attributes) {
          console.error("User attributes are undefined")
          return
        }

        const customAttributes = attributes.filter((attr) => attr.Name.startsWith("custom:"))
        console.log("Custom attributes:", customAttributes)

        const organizationId = customAttributes.find((attr) => attr.Name === "custom:organizationId")?.Value
        setOrg_id(organizationId)
        console.log("organizationId:", organizationId)

        if (organizationId) {
          setOrg_id(organizationId)
          setFunctionDefinitionSettings((prevSettings) => ({
            ...prevSettings,
            organisation_id: organizationId,
          }))
          setFunctionParams((prevParams) =>
            prevParams.map((param) => ({
              ...param,
              organisation_id: organizationId,
            })),
          )
        } else {
          console.error("Required custom attribute missing: organizationId")
        }
      })
    })
  }, [])

  // Validation functions
  const validateFunctionDefinition = (settings) => {
    return Object.values(settings).every((value) => (typeof value === "string" ? value.trim() !== "" : true))
  }

  const validateFunctionParameter = (param) => {
    const fieldsToValidate = ["name_of_attribute", "type", "pdescription"]
    return fieldsToValidate.every((key) => typeof param[key] === "string" && param[key].trim() !== "")
  }

  // Form handling functions
  const handleFunctionDefinitionChange = (e) => {
    const { id, value } = e.target

    // If it's the function name field and isTicketOnly is true, keep it as "Ticket Generation"
    if (id === "function_name" && isTicketOnly) {
      return // Don't allow changes to function name when isTicketOnly is true
    }

    const updatedSettings = { ...functionDefinitionSettings, [id]: value }
    setFunctionDefinitionSettings(updatedSettings)
    setIsDefinitionValid(validateFunctionDefinition(updatedSettings))
  }

  const handleFunctionParamChange = (index, e) => {
    const { id, value, type } = e.target
    const parsedValue = type === "checkbox" ? e.target.checked : value

    const updatedParams = [...functionParams]
    updatedParams[index] = {
      ...updatedParams[index],
      [id]: parsedValue,
    }

    setFunctionParams(updatedParams)

    // Only validate the current parameter
    const isCurrentValid = validateFunctionParameter(updatedParams[index])
    // Update overall parameter validity - all saved params must be valid
    const allSavedValid = updatedParams.filter((param) => param.isSaved).every(validateFunctionParameter)

    setIsParameterValid(allSavedValid)
  }

  // Parameter management functions
  const handleAddParam = () => {
    const newParam = {
      organisation_id: org_id || "", // Use org_id directly if functionParams is empty
      function_id: functionParams.length > 0 ? functionParams[0].function_id : "",
      name_of_attribute: "",
      type: "",
      pdescription: "",
      required: false,
      isSaved: false,
    }

    setFunctionParams([...functionParams, newParam])
    setSelectedParamIndex(functionParams.length) // Select the new parameter
    setShowInitialForm(false) // Close the form after the first attribute
    setIsEditing(false) // Ensure we're not in edit mode for the new attribute
    toast.success("New attribute added!")
  }

  const handleRemoveParam = (index) => {
    // Remove this check to allow removing all attributes
    // if (functionParams.length <= 1) {
    //   toast.error("Cannot remove the only parameter")
    //   return
    // }

    const updatedParams = [...functionParams]
    updatedParams.splice(index, 1)
    setFunctionParams(updatedParams)

    // Adjust selected index if needed
    if (index === selectedParamIndex) {
      setSelectedParamIndex(null)
    } else if (index < selectedParamIndex) {
      setSelectedParamIndex(selectedParamIndex - 1)
    }

    // If we're back to one parameter, show the initial form again
    if (updatedParams.length === 1) {
      setShowInitialForm(true)
      setSelectedParamIndex(0)
    }

    toast.success("Attribute removed!")
  }

  // Edit saved attribute
  const handleEditAttribute = (index) => {
    setSelectedParamIndex(index)
    setIsEditing(true)

    // We need to temporarily mark it as not saved to enable editing
    const updatedParams = [...functionParams]
    updatedParams[index] = {
      ...updatedParams[index],
      isSaved: false,
    }
    setFunctionParams(updatedParams)
  }

  // Save individual attribute - locally only, not to API
  const saveAttribute = (index) => {
    const param = functionParams[index]

    if (!validateFunctionParameter(param)) {
      toast.error("Please fill in all required fields for this attribute.")
      return
    }

    // Mark this parameter as saved locally (not sending to API yet)
    const updatedParams = [...functionParams]
    updatedParams[index] = {
      ...updatedParams[index],
      isSaved: true,
    }
    setFunctionParams(updatedParams)

    toast.success(`Attribute "${param.name_of_attribute}" saved!`)

    // Check if all visible attributes are now saved
    const allSaved = updatedParams.every((param) => param.isSaved)
    setIsParameterValid(allSaved)

    // For the first attribute, we need to change the showInitialForm flag to hide it
    if (index === 0 && showInitialForm) {
      setShowInitialForm(false)
    }

    // Close the form by deselecting it
    setSelectedParamIndex(null)
    setIsEditing(false)
  }

  // Reset form after completion
  const resetFunctionForm = () => {
    setFunctionDefinitionSettings({
      organisation_id: functionDefinitionSettings.organisation_id,
      function_name: "",
      fdescription: "",
      api_link: "",
      api_method: "",
    })
    setFunctionParams([
      {
        organisation_id: functionDefinitionSettings.organisation_id,
        function_id: "",
        name_of_attribute: "",
        type: "",
        pdescription: "",
        required: false,
        isSaved: false,
      },
    ])
    setIsDefinitionValid(false)
    setIsParameterValid(false)
    setFunctionStep(1)
    setSelectedParamIndex(0)
    setShowInitialForm(true)
    setIsEditing(false)
  }

  // API calls for each step
  const saveFunctionDef = async () => {
    if (!isDefinitionValid) {
      toast.error("Please fill in all fields before saving.")
      return
    }
    setLoading(true)
    try {
      // Step 1: Save the function definition
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/store_user_finput`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(functionDefinitionSettings),
      })

      if (response.ok) {
        const data = await response.json()
        console.log("Function ID:", data.function_id)
        toast.success("Function Definition saved successfully!")
        setFunctionParams((prevParams) =>
          prevParams.map((param) => ({
            ...param,
            function_id: data.function_id,
          })),
        )
        setFunctionStep(2)
      } else {
        toast.error("Failed to save Function Definition. Please try again.")
      }
    } catch (error) {
      toast.error("Error saving Function Definition.")
    } finally {
      setLoading(false)
    }
  }

  const saveFunctionParams = async () => {
    // Check if all parameters have been individually saved locally
    const unsavedParams = functionParams.filter((param) => !param.isSaved)

    if (unsavedParams.length > 0) {
      toast.error("Please save all attributes individually before completing.")
      return
    }

    setLoading(true)
    try {
      // Now send all saved parameters to the API
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/store_user_pinput`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ attributes: functionParams }),
      })

      if (response.ok) {
        toast.success("All attributes saved to server successfully!")
        resetFunctionForm()
      } else {
        toast.error("Failed to save attributes to server. Please try again.")
      }
    } catch (error) {
      toast.error("Error saving attributes to server.")
    } finally {
      setLoading(false)
    }
  }

  // Render the parameter form - either inline for first parameter or in dropdown for others
  const renderParameterForm = (index, param) => {
    const isFirstAttribute = index === 0 && showInitialForm
    const isValid = validateFunctionParameter(param)
    const isSaved = param.isSaved && !isEditing

    return (
      <div
        className={
          isFirstAttribute
            ? "flex flex-col gap-4 font-manrope"
            : " font-manrope flex flex-col gap-4 mt-2 p-4 border border-gray-200 rounded-lg bg-white"
        }
      >
        <Input
          type="text"
          label="Attribute Title"
          id="name_of_attribute"
          value={param.name_of_attribute}
          onChange={(e) => handleFunctionParamChange(index, e)}
          required
          className="mb-4"
          disabled={isSaved}
        />
        <Input
          type="text"
          label="Type"
          id="type"
          value={param.type}
          onChange={(e) => handleFunctionParamChange(index, e)}
          required
          className="mb-4"
          disabled={isSaved}
        />
        <Textarea
          label="Description"
          id="pdescription"
          value={param.pdescription}
          onChange={(e) => handleFunctionParamChange(index, e)}
          required
          className="mb-4"
          disabled={isSaved}
        />
        <Checkbox
          label="Required"
          id="required"
          checked={param.required}
          onChange={(e) => handleFunctionParamChange(index, e)}
          className=""
          disabled={isSaved}
        />

        <p className="text-sm text-gray-600 pl-4 mb-4">
          All saved tasks appear in Task Manager.
        </p>

        <div className="mt-4 flex gap-4">
          {/* Save Attribute Button (locally only) - Disabled until required checkbox is checked */}
          <Button
            onClick={() => saveAttribute(index)}
            className="font-manrope bg-[#CD0ADD] font-semibold hover:bg-purple-700 text-white py-2 px-8 rounded transition-all"
            disabled={!isValid || isSaved}
          >
            {isSaved ? "Saved" : "Save"}
          </Button>

          {/* Remove Attribute Button - Always visible and enabled */}
          <Button
            onClick={() => handleRemoveParam(index)}
            className="font-manrope font-semibold border-2 bg-transparent border-[#CD0ADD] text-[#CD0ADD] hover:bg-[#CD0ADD] hover:text-white py-2 px-6 rounded transition-all"
          >
            Remove
          </Button>
        </div>
      </div>
    )
  }

  // Render a saved attribute in the list view
  const renderSavedAttribute = (param, index) => {
    return (
      <div className="font-manrope border border-gray-200 rounded-lg p-3 mb-2 bg-white" key={index}>
        <div className="flex justify-between items-center">
          <div className="flex-grow">
            <span className="font-medium">{param.name_of_attribute}</span>
            <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Saved</span>
          </div>
          <Button
            onClick={() => handleEditAttribute(index)}
            className="bg-blue-500 hover:bg-blue-700 text-white p-2"
            size="sm"
          >
            <IoMdCreate className="w-4 h-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <Card className="w-full font-manrope">
      <CardBody>
        <div className="w-full">
          <div className="relative flex flex-col">
            {/* Vertical line between Step 1 and Step 2 */}
            <div
              className="absolute left-3.5 top-5 w-1 bg-[#CD0ADD] transition-all duration-300"
              style={{ height: functionStep === 1 ? "510px" : "100px" }}
            ></div>

            {/* Step 1 - Task */}
            <div className="z-10 flex mb-4">
              <div className="flex flex-col items-center mr-4 relative">
                {functionStep === 1 && (
                  <div className="absolute w-10 h-10 flex items-center justify-center z-20 border-4 border-gray-400 rounded-full -mt-1"></div>
                )}
                <div className="w-8 h-8 rounded-full flex items-center justify-center bg-[#CD0ADD] text-white z-30"></div>
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-bold text-[#CD0ADD]">Task</span>
                <span className="text-lg font-semibold mb-2">{functionDefinitionSettings.function_name || ""}</span>
              </div>
            </div>

            {/* Task Form Fields - Below Step 1 */}
            {functionStep === 1 && (
              <div className="ml-14 mb-8 space-y-4 font-manrope">
                <h2 className="text-2xl font-bold mb-4 ">Add tasks</h2>
                <Input
                  type="text"
                  label="Task Title"
                  id="function_name"
                  value={functionDefinitionSettings.function_name}
                  onChange={handleFunctionDefinitionChange}
                  required
                  disabled={isTicketOnly}
                  className={`${isTicketOnly ? "opacity-50 cursor-not-allowed" : ""}`}
                />
                <Textarea
                  label="Task Description"
                  id="fdescription"
                  value={functionDefinitionSettings.fdescription}
                  onChange={handleFunctionDefinitionChange}
                  required
                />
                <Input
                  type="text"
                  label="API Link"
                  id="api_link"
                  value={functionDefinitionSettings.api_link}
                  onChange={handleFunctionDefinitionChange}
                  required
                />
                <Select
                  id="api_method"
                  type="text"
                  value={functionDefinitionSettings.api_method}
                  onChange={(value) => handleFunctionDefinitionChange({ target: { id: "api_method", value } })}
                  className="list-none"
                  variant="outlined"
                  label="Select API Method"
                >
                  <Option value="GET">GET</Option>
                  <Option value="POST">POST</Option>
                  <Option value="PUT">PUT</Option>
                  <Option value="PATCH">PATCH</Option>
                  <Option value="DELETE">DELETE</Option>
                </Select>

                {/* Save Button moved to the end of task definition form */}
                <div className="flex justify-end pt-4">
                  <Button
                    onClick={saveFunctionDef}
                    disabled={loading || !isDefinitionValid}
                    className="bg-[#CD0ADD] hover:bg-purple-700 text-white py-2 px-8 rounded transition-all"
                  >
                    {loading ? <ClipLoader size={20} color={"#ffffff"} /> : "Save"}
                  </Button>
                </div>
              </div>
            )}

            {/* Step 2 - Attributes */}
            <div className="z-10 flex mt-8">
              <div className="flex flex-col items-center mr-4 relative">
                {functionStep === 2 && (
                  <div className="absolute w-10 h-10 flex items-center justify-center z-40 border-4 border-gray-400 rounded-full -mt-1"></div>
                )}
                <div className="w-8 h-8 rounded-full flex items-center justify-center bg-[#CD0ADD] text-white z-30"></div>
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-bold text-[#CD0ADD]">Attributes</span>
              </div>
            </div>

            {/* Attributes List and Form - Below Step 2 */}
            {functionStep === 2 && (
              <div className="ml-14 mb-8 space-y-4">
                <h2 className="text-2xl font-bold mb-4">{functionParams.function_name}</h2>

                {/* List of Attributes */}
                <div className="space-y-2 mb-4">
                  {functionParams.length === 0 ? (
                    <div className="p-4 border border-dashed border-gray-300 rounded-lg text-center">
                      <p className="text-gray-500">No attributes added. Click &quot;Add attributes&quot; below to add one.</p>
                    </div>
                  ) : (
                    functionParams.map((param, index) => {
                      // If the parameter is saved and not being edited, and not the initial form
                      if (param.isSaved && (!isEditing || selectedParamIndex !== index) && !(index === 0 && showInitialForm)) {
                        return renderSavedAttribute(param, index)
                      }

                      // For parameters being edited or the initial form
                      return (
                        <div key={index} className="mb-4">
                          {/* For first parameter with showInitialForm true, show form directly */}
                          {index === 0 && showInitialForm ? (
                            <div>
                              <div className="mb-2 font-semibold flex items-center">
                                {`Attribute ${index + 1}`}
                              </div>
                              {renderParameterForm(index, param)}
                            </div>
                          ) : (
                            <>
                              {/* Only show as dropdown if not saved or being edited */}
                              {(!param.isSaved || (isEditing && selectedParamIndex === index)) && (
                                <>
                                  {/* Attribute Name Button for unsaved or being edited items */}
                                  <div
                                    onClick={() => setSelectedParamIndex(selectedParamIndex === index ? null : index)}
                                    className={`flex justify-between items-center p-3 rounded-lg cursor-pointer border ${selectedParamIndex === index ? "border-[#CD0ADD] bg-purple-50" : "border-gray-200 hover:bg-gray-50"
                                      }`}
                                  >
                                    <span className="font-medium text-lg">
                                      {param.name_of_attribute || `Attribute ${index + 1}`}
                                      {isEditing && selectedParamIndex === index && (
                                        <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Editing</span>
                                      )}
                                    </span>
                                    <span className="text-gray-500 text-sm">
                                      {selectedParamIndex === index ? "▲" : "▼"}
                                    </span>
                                  </div>

                                  {/* Attribute Edit Form - Visible only when selected */}
                                  {selectedParamIndex === index && renderParameterForm(index, param)}
                                </>
                              )}
                            </>
                          )}
                        </div>
                      )
                    })
                  )}
                </div>

                {/* Add Attribute Button */}
                <div className="mt-8">
                  <button
                    onClick={handleAddParam}
                    className="flex items-center text-[#CD0ADD] hover:text-purple-700 font-medium"
                  >
                    <IoMdAdd className="w-5 h-5 mr-1" /> Add attributes
                  </button>
                </div>

                {/* Complete Button at the end of attributes form */}
                <div className="flex justify-end pt-4">
                  <Button
                    onClick={saveFunctionParams}
                    disabled={loading || functionParams.some((param) => !param.isSaved) || functionParams.length === 0}
                    className="font-manrope bg-[#CD0ADD] hover:bg-purple-700 text-white py-2 px-8 rounded transition-all"
                  >
                    {loading ? <ClipLoader size={20} color={"#ffffff"} /> : "Complete"}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  )
}
