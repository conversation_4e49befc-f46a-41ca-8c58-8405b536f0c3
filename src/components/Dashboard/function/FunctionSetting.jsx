import React, { useState, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';
import FunctionAttributeCards from '../../oldui/FunctionAttributeCards';
import EditModal from '../../oldui/EditModal';
import Squareload from '../../oldui/Squareload';
const FunctionSettingsCheck = ({ org_id, onComplete, isTicketOnly }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedData, setSelectedData] = useState(null);
  const [data, setData] = useState({
    function_data: null,
    function_attribute: null
  });

  useEffect(() => {
    fetchFunctionData();
  }, []);

  const fetchFunctionData = async () => {
    if (!org_id) {
      toast.error('Organisation id not found.');
      setIsLoading(false);
      return;
    }

    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/taskmanager/function_setting/get`, {
        organisationId: org_id
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 200) {
        const data = response.data;
        setData({
          function_data: data.functions,
          function_attribute: data.attributes
        });
        toast.success('Function Data Fetched!');
      } else {
        toast.error('Failed to fetch function data.');
      }
    } catch (error) {
      console.error(error);
      toast.error('No Function Data for this organisation.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (functionId, attributeId) => {
    const functionToEdit = data.function_data.find((f) => f.functionid === functionId);
    if (!functionToEdit) return;

    let attributeToEdit = undefined;
    if (attributeId) {
      attributeToEdit = data.function_attribute.find((a) => a.attributeid === attributeId);
    }
    setSelectedData({ function: functionToEdit, attribute: attributeToEdit });
    setIsModalOpen(true);
  };

  const handleSave = async (updatedData) => {
    const updatedPayload = {
      function_data: {},
      function_attribute: {},
    };

    if (updatedData.function) {
      const updatedFunctionData = data.function_data.find((func) => func.functionid === updatedData.function.functionid);
      if (updatedFunctionData) {
        const updatedFunction = { ...updatedFunctionData };
        let hasChanges = false;

        if (updatedFunction.functionName !== updatedData.function.functionName) {
          updatedFunction.functionName = updatedData.function.functionName;
          hasChanges = true;
        }

        if (updatedFunction.functionDefinition !== updatedData.function.functionDefinition) {
          updatedFunction.functionDefinition = updatedData.function.functionDefinition;
          hasChanges = true;
        }

        if (updatedFunction.functionapilink !== updatedData.function.functionapilink) {
          updatedFunction.functionapilink = updatedData.function.functionapilink;
          hasChanges = true;
        }

        if (hasChanges) {
          updatedPayload.function_data = {
            function_id: updatedFunction.functionid,
            functionName: updatedFunction.functionName,
            functionDefinition: updatedFunction.functionDefinition,
            api_link: updatedFunction.functionapilink,
          };

          setData((prevState) => ({
            ...prevState,
            function_data: prevState.function_data.map((func) =>
              func.functionid === updatedFunction.functionid ? updatedFunction : func
            ),
          }));
        }
      }
    }

    if (updatedData.attribute) {
      const updatedAttributeData = data.function_attribute.find(
        (attr) => attr.attributeid === updatedData.attribute.attributeid
      );
      if (updatedAttributeData) {
        const updatedAttribute = { ...updatedAttributeData };
        let hasChanges = false;

        if (updatedAttribute.attributeName !== updatedData.attribute.attributeName) {
          updatedAttribute.attributeName = updatedData.attribute.attributeName;
          hasChanges = true;
        }

        if (updatedAttribute.attributeDescription !== updatedData.attribute.attributeDescription) {
          updatedAttribute.attributeDescription = updatedData.attribute.attributeDescription;
          hasChanges = true;
        }

        if (updatedAttribute.required !== updatedData.attribute.required) {
          updatedAttribute.required = updatedData.attribute.required;
          hasChanges = true;
        }

        if (hasChanges) {
          updatedPayload.function_attribute = {
            parameter_id: updatedAttribute.attributeid,
            attributeName: updatedAttribute.attributeName,
            attributeDescription: updatedAttribute.attributeDescription,
            required: updatedAttribute.required,
          };

          setData((prevState) => ({
            ...prevState,
            function_attribute: prevState.function_attribute.map((attr) =>
              attr.attributeid === updatedAttribute.attributeid ? updatedAttribute : attr
            ),
          }));
        }
      }
    }

    if (Object.keys(updatedPayload.function_data).length > 0 ||
      Object.keys(updatedPayload.function_attribute).length > 0) {
      try {
        const response = await axios.post(
          `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/update_parameters`,
          updatedPayload,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        toast.success("Data updated successfully!");
      } catch (error) {
        console.error("Error updating data:", error);
        toast.error("Error updating data. Please try again.");
      }
    } else {
      toast.info("No changes detected");
    }
  };

  const handleDelete = async (functionId, attributeIds) => {
    try {
      const response = await axios.delete(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/delete_function`, {
        headers: {
          "Content-Type": "application/json",
        },
        data: {
          functionId,
          org_id,
          attributeIds,
        },
      });

      setData((prevData) => {
        const updatedFunctionData = prevData.function_data.filter((func) => func.functionid !== functionId);
        const updatedAttributeData = prevData.function_attribute.filter((attr) => !attributeIds.includes(attr.attributeid));

        return {
          function_data: updatedFunctionData,
          function_attribute: updatedAttributeData,
        };
      });

      toast.success("Function and its attributes deleted successfully");
      updateSchemaAndGeneratePrompt();
    } catch (error) {
      toast.error("Error deleting function and its attributes");
    }
  };

  const handleDeleteAttribute = async (functionId, attributeIds) => {
    try {
      const response = await axios.delete(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/delete_attribute`, {
        headers: {
          "Content-Type": "application/json",
        },
        data: {
          functionId,
          org_id,
          attributeIds,
        },
      });

      setData((prevData) => ({
        ...prevData,
        function_attribute: prevData.function_attribute.filter((attr) => !attributeIds.includes(attr.attributeid)),
      }));

      toast.success("Attribute deleted successfully");
      updateSchemaAndGeneratePrompt();
    } catch (error) {
      toast.error("Error deleting attribute");
    }
  };

  const handleAddAttribute = async (functionId, attributeData) => {
    try {
      const randomAttributeId = `${Math.floor(Math.random() * 1000)}`;

      const payload = {
        ...attributeData,
        parameter_id: randomAttributeId,
        org_id: org_id,
        functionId: functionId
      };

      const response = await axios.post(
        `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/add_attribute`,
        payload,
        { headers: { "Content-Type": "application/json" } }
      );

      // First update the local state with the new attribute
      setData((prevData) => ({
        ...prevData,
        function_attribute: [...prevData.function_attribute, response.data],
      }));

      toast.success("New attribute added successfully");
      updateSchemaAndGeneratePrompt();

      fetchFunctionData();
    } catch (error) {
      console.error("Error adding new attribute:", error);
      toast.error("Error adding new attribute");
    }
  };

  const generateSystemPrompt = async () => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_FUNCTTIONCALLING_API}/generate_system_prompt`, {
        organisation_id: org_id
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.status === 200) {
        toast.success('System prompt generated successfully');
      } else {
        toast.error('Failed to generate system prompt');
      }
    } catch (error) {
      console.error('Error generating system prompt:', error);
      toast.error('Error generating system prompt');
    }
  };


  const updateFunctionSchema = async () => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/save_function_schema`, {
        organisation_id: org_id
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.status === 200) {
        toast.success('Function schema updated successfully');
        return true;
      } else {
        toast.error('Failed to update function schema');
        return false;
      }
    } catch (error) {
      console.error('Error updating function schema:', error);
      toast.error('Error updating function schema');
      return false;
    }
  };

  const updateSchemaAndGeneratePrompt = async () => {
    // Get the current state directly from the server instead of relying on local state
    try {
      // First update the schema
      const schemaUpdated = await updateFunctionSchema()

      // Then generate the prompt only if schema was updated successfully
      if (schemaUpdated) {
        await generateSystemPrompt()
      }
    } catch (error) {
      console.error("Error updating schema and generating prompt:", error)
      toast.error("Error updating schema and generating prompt")
    }
  }

  return (
    <div className="space-y-8 font-manrope">
      {isLoading ? (
        <div className="">
          <Squareload />
        </div>
      ) : (
        <>
          {data.function_data ? (
            <FunctionAttributeCards
              functions={data.function_data}
              attributes={data.function_attribute}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onDeleteAttribute={handleDeleteAttribute}
              onAddAttribute={handleAddAttribute}
              onFetch={fetchFunctionData}
              isTicketOnly={isTicketOnly} // Pass the prop here
            />
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <p className="text-gray-600">
                {isTicketOnly 
                  ? "No ticket generation function available."
                  : "No function data available for this organisation."}
              </p>
            </div>
          )}
        </>
      )}

      <EditModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        data={selectedData}
        onSave={handleSave}
        isTicketOnly={isTicketOnly} // Add this prop
      />
    </div>
  );
};

export default FunctionSettingsCheck;