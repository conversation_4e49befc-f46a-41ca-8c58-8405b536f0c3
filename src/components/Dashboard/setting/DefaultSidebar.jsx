import { Fa<PERSON>og, <PERSON>aPalette, FaFont } from "react-icons/fa";
import { TbMessages } from "react-icons/tb";
import { Card, List, ListItem, ListItemPrefix } from "@material-tailwind/react";
import { SiDictionarydotcom } from "react-icons/si";

const DefaultSidebar = ({ selectedSetting, setSelectedSetting }) => {
  return (
    <Card className="h-[560px] w-full max-w-[20rem] py-4 shadow-xl shadow-blue-gray-900/5 bg-gradient-to-b from-[#1a3b2a] via-[#0f2a24] to-[#0b1f1b]">
      <List className="space-y-4">
        {[
          { key: "logo", label: "Logo Setting", icon: <FaCog className="h-5 w-5" /> },
          { key: "background", label: "Background Color Setting", icon: <FaPalette className="h-5 w-5" /> },
          { key: "fontColor", label: "Font Color Setting", icon: <FaFont className="h-5 w-5" /> },
          { key: "Reply setting", label: "Reply Setting", icon: <TbMessages className="h-5 w-5" /> },
          { key: "Word setting", label: "Word Setting", icon: <SiDictionarydotcom className="h-5 w-5" /> },
        ].map(({ key, label, icon }) => (
          <ListItem
            key={key}
            onClick={() => setSelectedSetting(key)}
            className={`group flex items-center rounded-lg transition-colors duration-200 p-2 cursor-pointer ${
              selectedSetting === key ? "bg-gradient-to-r from-[#134e2b] to-[#5c3d69]" : "hover:bg-gradient-to-r from-[#134e2b] to-[#5c3d69]"
            }`}
          >
            <ListItemPrefix>
              <span className={`text-[#CD0ADD] ${selectedSetting === key ? "font-bold" : ""}`}>{icon}</span>
            </ListItemPrefix>
            <span className={`text-base text-[#CD0ADD] transition-colors duration-200 ${selectedSetting === key ? "font-bold" : ""}`}>
              {label}
            </span>
          </ListItem>
        ))}
      </List>
    </Card>
  );
};

export default DefaultSidebar;
