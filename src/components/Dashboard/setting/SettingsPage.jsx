"use client"

import { useState } from "react"
import { Card } from "@mui/material"
import { toast } from "react-hot-toast"
import Squareload from "../../oldui/Squareload"
import axios from "axios"
import { useEffect } from "react"
import { Play } from "lucide-react"
const Stepper = ({ org_id }) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [logo, setLogo] = useState("")
  const [background, setBackground] = useState("")
  const [textcolor, setTextcolor] = useState("")
  const [fontcolor, setFontcolor] = useState("")
  const [settings, setSettings] = useState({
    logo: null,
    backgroundColor: "#FFFFFF",
    backgroundType: "gradient",
    fontColor: "white",
    wordDictionary: {},
    labelColors: {
      user: "#D3D3D3",
      assistant: "#E040FB",
    },
  })
  const [loading, setLoading] = useState(false)
  const [selectedOption, setSelectedOption] = useState("text")
  const [supportedServices, setSupportedServices] = useState({
    current_option: "text", // Default value
    available_options: ["text"]
  })
  const [hexInputs, setHexInputs] = useState({
    fontColor: "white",
    backgroundStart: "#2A5A4B",
    backgroundEnd: "#A771BD",
    userLabel: "#D3D3D3",
    assistantLabel: "#E040FB",
  })

  const handleLogoUpload = (e) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setSettings((prev) => ({ ...prev, logo: reader.result }))
      }
      reader.readAsDataURL(file)
    }
  }

  const handleColorChange = (e) => {
    const { id, value } = e.target
    setSettings((prev) => ({ ...prev, [id]: value }))
  }

  const handleHexInputChange = (e) => {
    const { name, value } = e.target
    let hexValue = value

    if (value.length > 0 && !value.startsWith('#')) {
      hexValue = '#' + value
    }

    // Update the hex input state
    setHexInputs(prev => ({ ...prev, [name]: hexValue }))

    // Only update the actual color if it's a valid hex code
    const hexRegex = /^#[0-9A-Fa-f]{6}$/
    if (hexRegex.test(hexValue)) {
      switch (name) {
        case 'fontColor':
          setSettings(prev => ({ ...prev, fontColor: hexValue }))
          break
        case 'backgroundStart':
          if (settings.backgroundType === 'solid') {
            setSettings(prev => ({ ...prev, backgroundColor: hexValue }))
          } else {
            const endColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD"
            setSettings(prev => ({
              ...prev,
              backgroundColor: `linear-gradient(135deg, ${hexValue} 0%, ${endColor} 100%)`
            }))
          }
          break
        case 'backgroundEnd':
          const startColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
          setSettings(prev => ({
            ...prev,
            backgroundColor: `linear-gradient(135deg, ${startColor} 0%, ${hexValue} 100%)`
          }))
          break
        case 'userLabel':
          setSettings(prev => ({
            ...prev,
            labelColors: { ...prev.labelColors, user: hexValue }
          }))
          break
        case 'assistantLabel':
          setSettings(prev => ({
            ...prev,
            labelColors: { ...prev.labelColors, assistant: hexValue }
          }))
          break
      }
    }
  }

  const handleHexKeyPress = (e) => {
    if (e.key === 'Enter') {
      const hexRegex = /^#[0-9A-Fa-f]{6}$/
      if (hexRegex.test(e.target.value)) {
        handleHexInputChange(e)
      }
    }
  }

  const goToStep = (step) => {
    if (step >= 1 && step <= 5) {
      setCurrentStep(step)

      // Call the API when navigating to step 5
      if (step === 5 && org_id) {
        checkSupportedServices()
      }
    }
  }

  useEffect(() => {
    fetchSetting()
  }, [])

  const fetchSetting = async () => {
    const savedUsername = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY)
    if (!savedUsername) {
      console.error("No username found in localStorage")
      return
    }
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get_user_settings`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username: savedUsername }),
      })
      if (!response.ok) {
        throw new Error("Failed to fetch settings")
      }
      const data = await response.json()

      // Store the fetched values in the state variables for reference
      setLogo(data.logo_base64)
      setBackground(data.background_color)
      setFontcolor(data.font_color)

      // Parse textfield JSON if it exists
      let parsedTextfield = settings.labelColors
      try {
        if (data.textfield) {
          parsedTextfield = JSON.parse(data.textfield)
          setTextcolor(data.textfield)
        }
      } catch (e) {
        console.error("Error parsing textfield JSON:", e)
      }

      // Update the settings state with fetched values
      let backgroundType = "gradient" // Default
      if (data.background_color) {
        // Check if the background color is a gradient or solid
        backgroundType = data.background_color.includes("linear-gradient") ? "gradient" : "solid"
      }

      setSettings((prevSettings) => ({
        ...prevSettings,
        logo: data.logo_base64 || prevSettings.logo,
        backgroundColor: data.background_color || prevSettings.backgroundColor,
        backgroundType: backgroundType,
        fontColor: data.font_color || prevSettings.fontColor,
        labelColors: parsedTextfield,
      }))

    } catch (error) {
      console.error("Error fetching settings:", error)
    }
  }

  const checkSupportedServices = async () => {
    if (!org_id) {
      toast.error("Organization ID is required")
      return
    }

    try {
      setLoading(true)
      const response = await axios({
        method: "POST",
        url: `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/check_supported_services`,
        data: {
          organisation_id: org_id,
          service: "frontend_voice_setting",
        },
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (response.status === 200) {
        if (typeof response.data.supported_services === 'string') {
          // If it's a string, use it as current_option
          setSupportedServices({
            current_option: response.data.supported_services,
            available_options: ["text", "voice", "both"] // Default options
          })
          setSelectedOption(response.data.supported_services)
        } else if (response.data.supported_services && typeof response.data.supported_services === 'object') {
          // If it's an object, use it directly
          setSupportedServices(response.data.supported_services)

          // If there's a current_option property, use it for selectedOption
          if (response.data.supported_services.current_option) {
            setSelectedOption(response.data.supported_services.current_option)
          }
        }

        toast.success("Retrieved supported services successfully")
      }
    } catch (error) {
      console.error("API Error:", error)
      toast.error(error.response?.data?.message || "Failed to fetch supported services")
    } finally {
      setLoading(false)
    }
  }
  // Add this after the state declarations

  const saveSettings = async () => {
    setLoading(true)
    const savedUsername = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY)

    if (!savedUsername) {
      toast.error("User session not found. Please login again.")
      return
    }

    try {
      let specificData = {}
      switch (currentStep) {
        case 1:
          specificData = { logo: settings.logo }
          break
        case 2:
          specificData = { background_color: settings.backgroundColor, backgroundType: settings.backgroundType }
          break
        case 3:
          specificData = { font_color: settings.fontColor }
          break
        case 4:
          try {
            const response = await axios({
              method: "POST",
              url: `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/settings/set`,
              data: {
                organisation_id: org_id,
                label_colors: settings.labelColors,
              },
              headers: {
                "Content-Type": "application/json",
              },
            })

            if (response.status === 200) {
              toast.success("Label colors updated successfully")
              setLoading(false)
              return
            }
          } catch (error) {
            console.error("API Error:", error)
            throw new Error(error.response?.data?.message || "Failed to update label colors")
          }
          break
        // In case 5 of the saveSettings switch statement
        case 5:
          if (!org_id) {
            throw new Error("Organization ID is required")
          }

          try {
            const response = await axios({
              method: "POST",
              url: `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/settings/reply_option/update`,
              data: {
                organisationId: org_id,
                service: "frontend_voice_setting",
                options: selectedOption,
              },
              headers: {
                "Content-Type": "application/json",
              },
            })

            if (response.status === 200) {
              // Update the supportedServices state to reflect the new selection
              setSupportedServices((prev) => ({
                ...prev,
                current_option: selectedOption,
              }))
              toast.success("Reply mode updated successfully")
              setLoading(false)
              return
            }
          } catch (error) {
            console.error("API Error:", error)
            throw new Error(error.response?.data?.message || "Failed to update reply mode")
          }
          break;
      }

      if (Object.keys(specificData).length > 0) {
        const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/settings/set`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ organisation_id: org_id, ...specificData }),
        })

        if (!response.ok) throw new Error("Failed to save settings")
      }

      toast.success("Settings saved successfully!")
    } catch (error) {
      console.error("Error in saveSettings:", error)
      toast.error(error.message || "Error saving settings.")
    } finally {
      setLoading(false)
    }
  }

  const ChatPreview = () => {
    const renderReplyContent = () => {
      switch (selectedOption) {
        case "voice":
          return (
            <div className="flex flex-col gap-2 max-w-xs font-manrope">
              <div className="flex items-center gap-2 bg-purple-500 text-white p-2 rounded-full w-60">
                <Play className="w-5 h-5 cursor-pointer" />
                <div className="w-full h-1 bg-white rounded-full relative">
                  <div className="h-1 bg-gray-300 w-1/3 rounded-full"></div>
                </div>
                <span className="text-xs bg-purple-700 px-2 py-1 rounded-full">Yo</span>
              </div>
            </div>
          )
        case "both":
          return (
            <div className="flex flex-col gap-2 max-w-xs">
              <div className="flex items-center gap-2 bg-purple-500 text-white p-2 rounded-full w-60">
                <Play className="w-5 h-5 cursor-pointer" />
                <div className="w-full h-1 bg-white rounded-full relative">
                  <div className="h-1 bg-gray-300 w-1/3 rounded-full"></div>
                </div>
                <span className="text-xs bg-purple-700 px-2 py-1 rounded-full">Yo</span>
              </div>
              <div className="bg-purple-500 p-2 rounded-xl w-fit" style={{ color: settings.fontColor }}>
                Hi, how may I assist you?
              </div>
            </div>
          )
        case "text":
        default:
          return <p>Hello! I&apos;m your AI assistant. How can I help you today?</p>
      }
    }

    return (
      <div
        className="w-[420px] h-[440px] rounded-xl overflow-hidden font-manrope border-2 border-gray-300"
        style={{
          background: settings.backgroundColor,
        }}
      >
        <div className="p-4 flex items-center gap-3">
          {settings.logo ? (
            <img
              src={settings.logo || "/placeholder.svg"}
              alt="Company Logo"
              className="w-10 h-10 rounded-full object-contain border-2 border-black bg-white"
            />
          ) : (
            <div className="w-8 h-8 bg-[#E429F2] rounded-full border-2 border-black"></div>
          )}
          <div
            className="max-w-[80%] rounded-2xl rounded-tl-sm px-4 py-2"
            style={{
              backgroundColor: settings.labelColors.assistant,
              color: settings.fontColor,
            }}
          >
            {renderReplyContent()}
          </div>
        </div>

        <div className="p-4 space-y-4">
          <div className="flex justify-end items-center gap-2">
            <div
              className="max-w-[80%] rounded-lg rounded-tr-sm px-4 py-2"
              style={{
                backgroundColor: settings.labelColors.user,
                color: settings.fontColor,
              }}
            >
              <p>I have a question about my order</p>
            </div>
            <div className="w-8 h-8 bg-white rounded-full"></div>
          </div>
        </div>
      </div>
    )
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Upload Your Company Logo</h2>
            <div className="flex flex-col md:flex-row gap-8">
              <div className="space-y-4 min-w-[300px] ">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="border border-gray-300 p-2 rounded-lg w-80"
                />
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Choose Your Background</h2>
            <div className="flex flex-col md:flex-row gap-8">
              <div className="space-y-4 min-w-[300px]">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Background Type</label>
                  <div className="flex gap-4">
                    <label className="flex items-center space-x-3 p-3 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                      <input
                        type="radio"
                        name="backgroundType"
                        value="solid"
                        checked={settings.backgroundType === "solid"}
                        onChange={() => {
                          const solidColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
                          setSettings((prev) => ({
                            ...prev,
                            backgroundType: "solid",
                            backgroundColor: solidColor,
                          }))
                        }}
                        className="w-4 h-4 text-purple-600"
                      />
                      <span className="text-sm font-medium text-gray-900">Solid Color</span>
                    </label>

                    <label className="flex items-center space-x-3 p-3 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                      <input
                        type="radio"
                        name="backgroundType"
                        value="gradient"
                        checked={settings.backgroundType === "gradient"}
                        onChange={() => {
                          const startColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
                          const endColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD"
                          setSettings((prev) => ({
                            ...prev,
                            backgroundType: "gradient",
                            backgroundColor: `linear-gradient(135deg, ${startColor} 0%, ${endColor} 100%)`,
                          }))
                        }}
                        className="w-4 h-4 text-purple-600"
                      />
                      <span className="text-sm font-medium text-gray-900">Gradient</span>
                    </label>
                  </div>
                </div>

                {settings.backgroundType === "solid" ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Color</label>
                    <div className="flex items-center gap-3">
                      <input
                        type="color"
                        value={
                          settings.backgroundColor.startsWith("#")
                            ? settings.backgroundColor
                            : settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
                        }
                        onChange={(e) => {
                          setSettings((prev) => ({
                            ...prev,
                            backgroundColor: e.target.value,
                          }))
                          setHexInputs(prev => ({
                            ...prev,
                            backgroundStart: e.target.value
                          }))
                        }}
                        className="w-32 h-32"
                      />
                      <div className="flex flex-col gap-2">
                        <label className="block text-sm font-medium text-gray-700">Hex Code</label>
                        <input
                          type="text"
                          name="backgroundStart"
                          value={hexInputs.backgroundStart}
                          onChange={handleHexInputChange}
                          onKeyPress={handleHexKeyPress}
                          className="border border-gray-300 p-2 rounded-lg w-full"
                          placeholder="#2A5A4B"
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Start Color</label>
                      <div className="flex items-center gap-3">
                        <input
                          type="color"
                          value={settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"}
                          onChange={(e) => {
                            const endColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD"
                            setSettings((prev) => ({
                              ...prev,
                              backgroundColor: `linear-gradient(135deg, ${e.target.value} 0%, ${endColor} 100%)`,
                            }))
                          }}
                          className="w-32 h-32"
                        />
                        <div className="flex flex-col gap-2">
                          <label className="block text-sm font-medium text-gray-700">Hex Code</label>
                          <input
                            type="text"
                            name="backgroundStart"
                            value={hexInputs.backgroundStart}
                            onChange={handleHexInputChange}
                            onKeyPress={handleHexKeyPress}
                            className="border border-gray-300 p-2 rounded-lg w-full"
                            placeholder="#2A5A4B"
                          />
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">End Color</label>
                      <div className="flex items-center gap-3">
                        <input
                          type="color"
                          value={settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD"}
                          onChange={(e) => {
                            const startColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
                            setSettings((prev) => ({
                              ...prev,
                              backgroundColor: `linear-gradient(135deg, ${startColor} 0%, ${e.target.value} 100%)`,
                            }))
                          }}
                          className="w-32 h-32"
                        />
                        <div className="flex flex-col gap-2">
                          <label className="block text-sm font-medium text-gray-700">Hex Code</label>
                          <input
                            type="text"
                            name="backgroundEnd"
                            value={hexInputs.backgroundEnd}
                            onChange={handleHexInputChange}
                            onKeyPress={handleHexKeyPress}
                            className="border border-gray-300 p-2 rounded-lg w-full"
                            placeholder="#A771BD"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Choose Your Font Color</h2>
            <div className="flex flex-col md:flex-row gap-8">
              <div className="space-y-4 min-w-[300px]">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Select Font Color</label>
                  <div className="flex items-center gap-4">
                    <input
                      type="color"
                      id="fontColor"
                      value={settings.fontColor}
                      onChange={handleColorChange}
                      className="w-20 h-20 cursor-pointer"
                    />
                    <input
                      type="text"
                      name="fontColor"
                      value={hexInputs.fontColor}
                      onChange={handleHexInputChange}
                      onKeyPress={handleHexKeyPress}
                      className="border border-gray-300 p-2 rounded-lg w-32"
                      placeholder="Hex Code"
                    />
                    <span className="text-sm font-medium" style={{ color: settings.fontColor }}>
                    Hex Code
                    </span>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Colors</h3>
                  <div className="grid grid-cols-5 gap-2">
                    {["#FFFFFF", "#000000", "#E429F2", "#4A90E2", "#F5A623"].map((color) => (
                      <div
                        key={color}
                        className="w-10 h-10 rounded-full cursor-pointer border border-gray-300 hover:scale-110 transition-transform"
                        style={{ backgroundColor: color }}
                        onClick={() => {
                          setSettings((prev) => ({ ...prev, fontColor: color }))
                        }}
                      ></div>
                    ))}
                  </div>
                </div>

                <div className="mt-6">
                  <div className="p-4 rounded-lg border border-gray-200 bg-gray-50">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Font Color Tips</h3>
                    <ul className="text-xs text-gray-600 space-y-1">
                      <li>• Choose high contrast colors for better readability</li>
                      <li>• White or light colors work best on dark backgrounds</li>
                      <li>• Dark colors work best on light backgrounds</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Customize Label Colors</h2>
            <div className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Response Font Color</h3>
                <div className="flex items-center gap-4">
                  <input
                    type="color"
                    value={settings.labelColors.assistant}
                    onChange={(e) => {
                      setSettings((prev) => ({
                        ...prev,
                        labelColors: {
                          ...prev.labelColors,
                          assistant: e.target.value,
                        },
                      }))
                    }}
                    className="w-20 h-20 cursor-pointer"
                  />
                  <input
                    type="text"
                    name="assistantLabel"
                    value={hexInputs.assistantLabel}
                    onChange={handleHexInputChange}
                    onKeyPress={handleHexKeyPress}
                    className="border border-gray-300 p-2 rounded-lg w-32"
                    placeholder="Hex Code"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Question Font Color</h3>
                <div className="flex items-center gap-4">
                  <input
                    type="color"
                    value={settings.labelColors.user}
                    onChange={(e) => {
                      setSettings((prev) => ({
                        ...prev,
                        labelColors: {
                          ...prev.labelColors,
                          user: e.target.value,
                        },
                      }))
                    }}
                    className="w-20 h-20 cursor-pointer"
                  />
                  <input
                    type="text"
                    name="userLabel"
                    value={hexInputs.userLabel}
                    onChange={handleHexInputChange}
                    onKeyPress={handleHexKeyPress}
                    className="border border-gray-300 p-2 rounded-lg w-32"
                    placeholder="Hex Code"
                  />
                </div>
              </div>
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Configure Reply Settings</h2>
            {loading ? (
              <div className="flex justify-center items-center h-40">
                 <Squareload/>
              </div>
            ) : (
              <div className="">
                <h3 className="text-lg font-medium mb-4">Select preferred reply mode for your bot</h3>

                <div className="mb-4 p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <p className="text-sm text-[#CD0ADD]">
                    <span className="font-medium">Current option:</span> {
                      supportedServices
                        ? (typeof supportedServices === 'string'
                          ? supportedServices
                          : supportedServices.current_option || "text")
                        : "Loading..."
                    }
                  </p>
                </div>

                <div className="space-y-4">
                  <label className="flex items-center space-x-3 p-4 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="replyMode"
                      value="text"
                      onChange={() => setSelectedOption("text")}
                      checked={selectedOption === "text"}
                      className="w-4 h-4 text-purple-600"
                    />
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-900">Text</span>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 p-4 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="replyMode"
                      value="voice"
                      onChange={() => setSelectedOption("voice")}
                      checked={selectedOption === "voice"}
                      className="w-4 h-4 text-purple-600"
                    />
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-900">Voice</span>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 p-4 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="replyMode"
                      value="both"
                      onChange={() => setSelectedOption("both")}
                      checked={selectedOption === "both"}
                      className="w-4 h-4 text-purple-600"
                    />
                    <div className="flex items-center space-x-3">
                      <div className="flex gap-1"></div>
                      <span className="text-sm font-medium text-gray-900">Both</span>
                    </div>
                  </label>
                </div>
              </div>
            )}
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className="font-manrope">
      {/* Clickable stepper navigation */}
      <div className="max-w-5xl mx-auto mt-8 px-4">
        <div className="relative flex justify-between items-center">
          {/* Horizontal line */}
          <div className="absolute top-[11px] left-[10px] w-[940px] h-0.5 bg-gray-200 transform -translate-y-1/2"></div>

          {[1, 2, 3, 4, 5].map((step) => (
            <button
              key={step}
              onClick={() => goToStep(step)}
              className="relative z-10 flex flex-col items-center focus:outline-none"
            >
              <div
                className={`w-6 h-6 rounded-full flex items-center justify-center transition-colors ${step === currentStep
                  ? "bg-[#E429F2] text-white ring-2 ring-offset-2 ring-[#E429F2]"
                  : "bg-gray-200 text-gray-500"
                  }`}
              >
                {step}
              </div>
              <span className={`text-xs pt-3 text-[#CD0ADD] mt-1 ${step === currentStep ? "font-bold" : ""}`}>
                {step === 1
                  ? "Logo"
                  : step === 2
                    ? "Background Color"
                    : step === 3
                      ? "Font Color"
                      : step === 4
                        ? "Text Field Color"
                        : "Reply Settings"}
              </span>
            </button>
          ))}
        </div>
      </div>

      <Card className="max-w-5xl mx-auto p-6 mt-8">
        <div className="flex flex-col h-full">
          <div className="flex flex-1 justify-between">
            <ChatPreview />
            <div className="flex-grow max-w-sm">{renderStep()}</div>
          </div>

          <div className="flex justify-end gap-4 mt-8 pt-6">
            <button
              onClick={saveSettings}
              disabled={loading}
              className="px-6 py-2 bg-[#E429F2] text-white rounded-md hover:bg-[#D11ADF] flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Processing...</span>
                </>
              ) : (
                "Save"
              )}
            </button>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default Stepper

