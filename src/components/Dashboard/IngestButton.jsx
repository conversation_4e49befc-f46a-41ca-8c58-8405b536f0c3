import React, { useState, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';
import { <PERSON>unceLoader } from 'react-spinners';
import { Upload } from 'lucide-react';
import { useLoader } from '../../context/LoaderContext';
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogHeader,
  DialogBody,
  DialogFooter,
  Checkbox,
  Typography,
} from '@material-tailwind/react';

const IngestButton = ({ onIngestComplete }) => {
  const [open, setOpen] = useState(false);
  const [ingesting, setIngesting] = useState(false);
  const [error, setError] = useState(null);
  const [options, setOptions] = useState([]);
  const [selectedOption, setSelectedOption] = useState('');
  const [files, setFiles] = useState([]);
  const { setLoading, setMessage } = useLoader();

  useEffect(() => {
    const fetchOptions = async () => {
      const username = localStorage.getItem(
        import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY
      );
      if (!username) {
        setError('Username not found. Please login again.');
        return;
      }

      try {
        const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get_pinecone_instances`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username }),
        });

        if (response.ok) {
          const data = await response.json();
          setOptions(data.instances || []);
          if (data.instances && data.instances.length > 0) {
            setSelectedOption(data.instances[0].instance_id);
          }
        } else {
          const errorMessage = await response.text();
          setError(`Failed to fetch options: ${errorMessage}`);
          toast.error(`Failed to fetch options: ${errorMessage}`);
        }
      } catch (error) {
        console.error('Error fetching options:', error);
        setError('Error fetching options. Please try again later.');
        toast.error('Error fetching options. Please try again later.');
      }
    };

    fetchOptions();
  }, []);

  const handleFileChange = (e) => {
    setFiles(Array.from(e.target.files));
    setError(null);
  };

  const handleIngest = async () => {
    const username = localStorage.getItem(
      import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY
    );
    console.log("ingestusername",username)

    if (!username) {
      toast.error('Username not found. Please login again.');
      return;
    }

    if (!selectedOption) {
      toast.error('Please select an index before ingesting.');
      return;
    }

    if (files.length === 0) {
      toast.error('Please upload at least one file before ingesting.');
      return;
    }

    setOpen(false);
    setIngesting(true);
    setMessage('Ingestion is currently in progress...');
    setLoading(true);
    setError(null);

    const formData = new FormData();
    files.forEach(file => formData.append('files', file));
    formData.append('username', username);
    formData.append('selectedOption', selectedOption);

    try {
      await axios.post(`${import.meta.env.VITE_PUBLIC_BACKEND_API}/api/ingest`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const toastId = toast.loading('Processing documents...');

      // Wait for ingestion to complete
      await new Promise(resolve => setTimeout(resolve, 20000));

      // Call onIngestComplete to trigger data refresh
      await onIngestComplete();

      toast.dismiss(toastId);
      toast.success('Documents ingested successfully!');
    } catch (err) {
      console.error('Error during ingestion:', err);
      toast.error('An error occurred during ingestion. Same filename can be ingested.');
    } finally {
      setIngesting(false);
      setLoading(false);
      setFiles([]);
    }
  };

  const handleOpen = () => setOpen(!open);

  const handleOptionChange = (instanceId) => {
    setSelectedOption(instanceId === selectedOption ? '' : instanceId);
  };

  return (
    <>
      <Button
        onClick={handleOpen}
        className="flex items-center gap-2 bg-[#CD0ADD] ml-auto font-manrope"
      >
        <Upload className="h-4 w-4" />
        Upload Document
      </Button>

      <Dialog
        open={open}
        handler={handleOpen}
        size="md"
        className="bg-white shadow-xl font-manrope"
      >
        <DialogBody className="overflow-y-auto font-manrope">
          <div className="space-y-4">
            <div className="mt-6">
              <Typography variant="h5" color="blue-gray" className="mb-2 font-manrope">
                Upload Documents
              </Typography>
              <Typography variant="small" color="gray" className="mb-4 font-manrope">
                Select one or more PDF files to upload
              </Typography>
              <input
                type="file"
                onChange={handleFileChange}
                className="w-full p-2 border border-gray-300 rounded-lg"
                accept=".pdf"
                multiple
              />
            </div>

            {error && (
              <Typography color="red" className="mt-2">
                {error}
              </Typography>
            )}
          </div>
        </DialogBody>
        <Typography className='p-4 font-manrope' color="gray" variant="h7">
          Select your index
        </Typography>
        <div className="flex flex-wrap px-4 gap-4">
          {options.length > 0 ? (
            options.map((instance) => (
              <div 
                key={instance.instance_id} 
                className="flex items-center gap-2"
              >
                <Checkbox
                  color="gray"
                  checked={selectedOption === instance.instance_id}
                  onChange={() => handleOptionChange(instance.instance_id)}
                  className={`font-manrope h-5 w-5 rounded border-2 border-gray-300 transition-all 
                    checked:border-[#CD0ADD] checked:bg-[#CD0ADD] 
                    hover:border-[#CD0ADD] hover:bg-[#CD0ADD]/20`}
                  containerProps={{
                    className: "p-0"
                  }}
                />
                <Typography className="" color="gray" >
                  {instance.display_name}
                </Typography>
              </div>
            ))
          ) : (
            <Typography color="gray">No instances available.</Typography>
          )}
        </div>
        <DialogFooter className="space-x-2 font-manrope">
          <Button 
            className='font-manrope border-2 border-[#CD0ADD] text-[#CD0ADD]' 
            variant="text" 
            onClick={handleOpen}
          >
            <h1 className='normal-case'>Cancel</h1>
          </Button>
          <Button
            color="blue"
            onClick={handleIngest}
            disabled={ingesting || !selectedOption || files.length === 0}
            className="font-manrope flex items-center gap-2 bg-[#CD0ADD]"
          >
            {ingesting ? (
              <>
                <BounceLoader color="#ffffff" size={20} />
                <span>Processing...</span>
              </>
            ) : (
              <h1 className='normal-case'>Upload</h1>
            )}
          </Button>
        </DialogFooter>
      </Dialog>
    </>
  );
};

export default IngestButton;
