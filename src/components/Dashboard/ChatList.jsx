import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import LogoutDialog from "../oldui/LogoutDialog"
import userPool from "../../lib/userPoolConfig"
import toast from 'react-hot-toast';

// Get API base URL from environment variable
const API_BASE = import.meta.env.VITE_PUBLIC_AGENT_PUBSUB_API;
console.log("Api",API_BASE)

function ChatList({ org_id }) {
    // State to hold chat messages
    const [messages, setMessages] = useState([]);
    // State for the new message being typed by the user
    const [newMessage, setNewMessage] = useState('');
    // State for the current active agent's identifier (the sender's name)
    const [myAgentId, setMyAgentId] = useState(''); // Will be set from userPool
    // State for the current organization ID
    const [organizationId, setOrganizationId] = useState(); // Default organization ID
    const [isLogoutDialogOpen, setLogoutDialogOpen] = useState(false)

    // Initialize selectedChannel from localStorage if present, else empty
    const getInitialSelectedChannel = () => {
        const stored = localStorage.getItem('selectedChannel');
        return stored ? stored : '';
    };
    const [selectedChannel, setSelectedChannel] = useState(getInitialSelectedChannel());

    // State to hold the list of available chat channels (agent IDs from Redis)
    const [availableChannels, setAvailableChannels] = useState([]);
    // State to store the status of the currently selected chat
    const [currentChatStatus, setCurrentChatStatus] = useState(null);
    // State to track if assignment is in progress
    const [assignmentInProgress, setAssignmentInProgress] = useState(false);

    // Ref to store the SINGLE WebSocket instance
    const wsRef = useRef(null);
    // Ref to store the messages container DOM element for auto-scrolling
    const messagesContainerRef = useRef(null);

    // ADD THESE REFS TO STORE CURRENT VALUES FOR WEBSOCKET HANDLERS
    const selectedChannelRef = useRef(selectedChannel);
    const myAgentIdRef = useRef(myAgentId);
    const organizationIdRef = useRef(organizationId);

    // UPDATE REFS WHEN STATE CHANGES
    useEffect(() => {
        selectedChannelRef.current = selectedChannel;
    }, [selectedChannel]);

    useEffect(() => {
        myAgentIdRef.current = myAgentId;
    }, [myAgentId]);

    useEffect(() => {
        organizationIdRef.current = organizationId;
    }, [organizationId]);

    const logout = async () => {
        //   setLoading(true)
        const user = userPool.getCurrentUser()

        if (user) {
            console.log("Signing out user:", user.getUsername())
            localStorage.removeItem("username")
            localStorage.removeItem("email")
            user.signOut()
            // setLoading(false)
            window.location.href = "/"
        } else {
            console.log("No user to sign out")
            // setLoading(false)
        }
    }
    // Custom setter for selectedChannel to also manage localStorage (only allow valid channel ids)
    // This function ONLY selects a channel for viewing - it does NOT assign the channel
    const setAndStoreSelectedChannel = (channelId) => {
        console.log(`[setAndStoreSelectedChannel] Called with channelId: ${channelId}`);
        console.log(`[setAndStoreSelectedChannel] Current availableChannels:`, availableChannels);
        
        // Only allow setting to a valid channel or empty
        if (channelId && !availableChannels.find(c => c.id === channelId)) {
            setSelectedChannel('');
            localStorage.removeItem('selectedChannel');
            return;
        }
        
        console.log(`[setAndStoreSelectedChannel] Setting selectedChannel to: ${channelId} (VIEWING ONLY - NOT ASSIGNING)`);
        setSelectedChannel(channelId);
        
        if (channelId) {
            localStorage.setItem('selectedChannel', channelId);
            console.log(`Stored selectedChannel: ${channelId}`);
            
            // DO NOT send SET_ACTIVE_CHANNEL when just selecting for viewing
            // SET_ACTIVE_CHANNEL should only be sent when explicitly assigning
            console.log(`[setAndStoreSelectedChannel] NOT sending SET_ACTIVE_CHANNEL - this is just for viewing`);
        } else {
            localStorage.removeItem('selectedChannel');
        }
    };

    // Effect to set organizationId from org_id prop
    useEffect(() => {
        setOrganizationId(org_id);
        if (org_id) {
            console.log(`[Prop] Loaded organizationId: ${org_id}`);
        }
    }, [org_id]); // Run on mount and whenever org_id changes

    // Effect to load initial organizationId and myAgentId from localStorage and userPool
    useEffect(() => {
        // Try to get username from Cognito userPool
        let username = '';
        try {
            const user = userPool.getCurrentUser();
            if (user) {
                username = user.getUsername();
            }
        } catch (e) {
            console.error('Error getting username from userPool:', e);
        }
        // Fallback to localStorage if userPool fails
        if (!username) {
            username = localStorage.getItem('CognitoIdentityServiceProvider.24ira6svu3j9t2qbls9s4cgr6t.LastAuthUser') || '';
        }
        if (username) {
            setMyAgentId(username);
            console.log(`[UserPool] Loaded myAgentId: ${username}`);
        }
    }, []); // Run once on mount

    // Effect to persist organizationId and myAgentId whenever they change
    useEffect(() => {
        localStorage.setItem('lastOrgId', organizationId);
        localStorage.setItem('lastAgentId', myAgentId);
    }, [organizationId, myAgentId]);

    // Function to fetch available channels from the backend (now callable on demand)
    const fetchChannels = async () => {
        console.log('[fetchChannels] Starting fetchChannels...');
        console.log(`[fetchChannels] Current organizationId: '${organizationId}'`);
        console.log(`[fetchChannels] Current selectedChannel (before fetch): '${selectedChannel}'`);
        console.log(`[fetchChannels] Current currentChatStatus (before fetch):`, currentChatStatus);

        if (!organizationId) {
            console.log('[fetchChannels] No organizationId, clearing channels and chat status.');
            setAvailableChannels([]);
            setAndStoreSelectedChannel(''); // Use custom setter
            setCurrentChatStatus(null);
            setMessages([]);
            return;
        }
        try {
            // Always fetch channels for the agent view, so include agentId
            let url = `${API_BASE}/channels?organizationId=${organizationId}`;
            // if (myAgentId) {
            //   url += `&agentId=${myAgentId}`; // Only fetch channels assigned to this agent
            // }
            console.log(`[fetchChannels] Fetching from URL: ${url}`);
            const response = await axios.get(url);
            const fetchedChannels = response.data.channels;
            setAvailableChannels(fetchedChannels);
            // NEW LOGS FOR DEBUGGING
            console.log(`[fetchChannels] Backend returned fetchedChannels:`, fetchedChannels);
            console.log(`[fetchChannels] Previously selected channel ID was: '${selectedChannel}'`);
            // END NEW LOGS
            console.log('Fetched available channels for organization:', organizationId, fetchedChannels);

            const previouslySelectedChannelId = selectedChannel; // Capture before potential changes
            const updatedSelectedChat = fetchedChannels.find(c => c.id === previouslySelectedChannelId);
            console.log(`[fetchChannels] Found updatedSelectedChat for '${previouslySelectedChannelId}':`, updatedSelectedChat);

            // --- IMPROVED LOGIC TO PRESERVE SELECTED CHANNEL DURING ASSIGNMENT ---
            if (previouslySelectedChannelId) // If there was a channel selected
                if (!updatedSelectedChat) {
                    // If the previously selected channel is NOT found in the newly fetched list,
                    // it might be temporarily unavailable due to assignment. Don't clear immediately.
                    console.log(`[fetchChannels] Previously selected channel '${previouslySelectedChannelId}' NOT found in fetched channels.`);
                    console.log(`[fetchChannels] Assignment in progress: ${assignmentInProgress}`);
                    
                    // If assignment is in progress, keep the current selection
                    if (assignmentInProgress) {
                        console.log(`[fetchChannels] Assignment in progress, keeping current selection for channel: ${previouslySelectedChannelId}`);
                        // Don't clear anything - keep the current state
                        return; // Exit early to preserve the current state
                    }
                    
                    // Only clear if we're certain the channel is gone (e.g., after a delay)
                    // For now, keep the current selection and status
                    if (currentChatStatus && currentChatStatus.id === previouslySelectedChannelId) {
                        console.log(`[fetchChannels] Keeping current selection and status for channel: ${previouslySelectedChannelId}`);
                        // Don't clear anything - keep the current state
                    } else {
                        console.log(`[fetchChannels] No currentChatStatus for selected channel, clearing selection.`);
                        setAndStoreSelectedChannel(''); // Use custom setter
                        setCurrentChatStatus(null);
                        setMessages([]);
                    }
                } else {
                    // If the previously selected channel IS found, update its status.
                    setCurrentChatStatus(updatedSelectedChat);
                    console.log(`[fetchChannels] Selected channel '${previouslySelectedChannelId}' still exists. Updating currentChatStatus.`);
                }
            

            // This block handles the case where there are no channels AT ALL for the current view.
            // This should always clear the selected channel if the list is truly empty.
            if (fetchedChannels.length === 0) {
                console.log('[fetchChannels] No channels available after fetch. Ensuring selectedChannel, status, and messages are cleared.');
                setAndStoreSelectedChannel(''); // Use custom setter
                setCurrentChatStatus(null);
                setMessages([]);
            }
        } catch (error) {
            console.error('[fetchChannels] Error fetching channels:', error);
            // On error, it's safer to clear the current chat state to prevent stale data
            setAndStoreSelectedChannel(''); // Use custom setter
            setCurrentChatStatus(null);
            setMessages([]);
        }
        console.log('[fetchChannels] Finished fetchChannels.');
    };

    // Effect for the single WebSocket connection
    useEffect(() => {
        console.log('[useEffect - WS Connection] Initiating WS connection effect.');
        console.log(`[useEffect - WS Connection] Current organizationId: '${organizationId}', myAgentId: '${myAgentId}'`);

        // Only connect if organizationId and myAgentId are available
        if (!organizationId || !myAgentId) {
            if (wsRef.current) {
                console.log('[useEffect - WS Connection] Closing existing WS due to missing orgId/agentId.');
                wsRef.current.close();
                wsRef.current = null;
            }
            return;
        }

        // Close any existing WebSocket connection before creating a new one
        if (wsRef.current) {
            console.log('[useEffect - WS Connection] Closing previous WS connection.');
            wsRef.current.close();
        }

        // Establish a single WebSocket connection including organizationId and myAgentId
        const ws = new WebSocket(`wss://devagent.najoomi.ai/ws?organizationId=${organizationId}&agentId=${myAgentId}`);
        // const ws = new WebSocket(`ws://localhost:3300/ws?organizationId=${organizationId}&agentId=${myAgentId}`);

        // --- KEEPALIVE: Send ping every 30 seconds to keep connection alive ---
        let pingInterval = setInterval(() => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'ping' }));
                console.log('[WebSocket Keepalive] Sent ping');
            }
        }, 30000); // 30 seconds

        ws.onopen = () => {
            console.log('Single WebSocket connected for organization:', organizationId, 'agent:', myAgentId);
            // Immediately fetch channels on connection to get the initial state
            fetchChannels();
            // Send initial active channel if one is already selected (for real-time messaging only)
            if (selectedChannel) {
                const currentChat = availableChannels.find(c => c.id === selectedChannel);
                if (currentChat) {
                    console.log(`[WS onopen] NOT sending SET_ACTIVE_CHANNEL - channel ${selectedChannel} is just for viewing`);
                } else {
                    console.log(`[WS onopen] Not sending SET_ACTIVE_CHANNEL - channel ${selectedChannel} not found in availableChannels.`);
                }
            }
        };

        // Attach onmessage handler and log that it is attached
        console.log('[WebSocket] onmessage handler attached');
        ws.onmessage = (event) => {
            console.log('[WebSocket Raw Data]', event.data);
            let receivedData;
            try {
                receivedData = JSON.parse(event.data);
            } catch (e) {
                console.error('[WebSocket] Failed to parse message:', event.data, e);
                return;
            }
            console.log('--- Frontend WebSocket Received Data ---');
            console.log('Raw received data:', receivedData); // Log the entire received object

            // USE REFS TO GET CURRENT VALUES INSTEAD OF STALE CLOSURE VALUES
            const currentSelectedChannel = selectedChannelRef.current;
            const currentMyAgentId = myAgentIdRef.current;
            const currentOrganizationId = organizationIdRef.current;

            // Differentiate between chat messages and channel list updates
            if (receivedData.type === 'CHANNEL_LIST_UPDATE' && receivedData.organizationId === currentOrganizationId) {
                console.log('Received CHANNEL_LIST_UPDATE. Re-fetching channels...');
                fetchChannels(); // Re-fetch channels when a global update is received
            } else if (receivedData.type === 'chat_message' && receivedData.organizationId === currentOrganizationId) {
                console.log('Received chat_message for this organization.');
                console.log(`  - receivedData.userId (message target): '${receivedData.userId}'`);
                console.log(`  - currentSelectedChannel (current view): '${currentSelectedChannel}'`);
                console.log(`  - userId match condition: ${receivedData.userId === currentSelectedChannel}`);
                console.log(`  - message from: '${receivedData.from}'`); // Check 'from' key in received message
                console.log(`  - currentMyAgentId (current agent): '${currentMyAgentId}'`);

                // Only process if the message is for the currently selected channel
                if (receivedData.userId === currentSelectedChannel) {
                    setMessages(prevMessages => {
                        const msgText = receivedData.message || receivedData.text || '';
                        if (!msgText.trim()) {
                            console.log('[WS onmessage] Empty message text, skipping.');
                            return [...prevMessages]; // Return new array to prevent issues
                        }
                        
                        console.log(`[WS onmessage] Processing message: "${msgText}" from: "${receivedData.from}"`);
                        console.log(`[WS onmessage] Current agent ID: "${currentMyAgentId}"`);
                        console.log(`[WS onmessage] Previous messages count: ${prevMessages.length}`);
                        
                        // Use 'from' key to determine message sender for current conversation
                        const isFromAgent = receivedData.from === 'agent';
                        
                        if (isFromAgent) {
                            console.log('[WS onmessage] Message is from current agent, checking for duplicates...');
                            
                            // Check if this is a confirmation of an optimistic message
                            const optimisticMessageIndex = prevMessages.findIndex(msg => 
                                msg.isOptimistic && 
                                msg.text === msgText && 
                                msg.from === 'agent'
                            );
                            
                            if (optimisticMessageIndex !== -1) {
                                console.log('[WS onmessage] Found matching optimistic message, replacing with confirmed message.');
                                const confirmedMsg = {
                                    ...receivedData,
                                    from: 'agent',
                                    text: msgText,
                                    time: new Date(receivedData.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                                    timestamp: new Date(receivedData.timestamp).getTime(),
                                    isOptimistic: false, // Mark as confirmed
                                    isHistorical: false // Mark as current conversation
                                };
                                
                                const newMessages = [...prevMessages];
                                newMessages[optimisticMessageIndex] = confirmedMsg;
                                return newMessages;
                            }
                            
                            // Improved duplicate detection: check for messages with same text and recent timestamp
                            const receivedTimestamp = new Date(receivedData.timestamp).getTime();
                            const timeThreshold = 5000; // 5 seconds threshold for duplicate detection
                            
                            const isDuplicate = prevMessages.some(msg => 
                                msg.from === 'agent' &&
                                msg.text === msgText &&
                                Math.abs(msg.timestamp - receivedTimestamp) < timeThreshold
                            );
                            
                            console.log(`[WS onmessage] Duplicate check - isDuplicate: ${isDuplicate}, receivedTimestamp: ${receivedTimestamp}, timeThreshold: ${timeThreshold}`);
                            
                            if (isDuplicate) {
                                console.log('[WS onmessage] Duplicate message detected, skipping WebSocket message.');
                                return [...prevMessages]; // Return new array to prevent issues
                            }
                            
                            console.log('[WS onmessage] Adding new agent message from WebSocket.');
                            const newMsg = {
                                ...receivedData,
                                from: 'agent',
                                text: msgText,
                                time: new Date(receivedData.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                                timestamp: receivedTimestamp,
                                isHistorical: false // Mark as current conversation
                            };
                            return [...prevMessages, newMsg];
                        } else {
                            console.log('[WS onmessage] Message is from user, adding to messages.');
                            const newMsg = {
                                ...receivedData,
                                from: 'user',
                                text: msgText,
                                time: new Date(receivedData.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                                timestamp: new Date(receivedData.timestamp).getTime(),
                                isHistorical: false // Mark as current conversation
                            };
                            return [...prevMessages, newMsg];
                        }
                    });
                } else {
                    // This is a chat message for a channel *not* currently selected.
                    console.log(`Chat message for unselected channel '${receivedData.userId}'. Not adding to current chat view.`);
                }
            } else {
                console.log('Unhandled WebSocket message type or organizationId mismatch:', receivedData);
            }
        };

        // Add a second event listener for debugging
        ws.addEventListener('message', (event) => {
            console.log('[WebSocket addEventListener] Received message:', event.data);
        });

        ws.onclose = () => {
            console.log('Single WebSocket disconnected.');
            clearInterval(pingInterval); // Cleanup keepalive
        };

        ws.onerror = (error) => {
            console.error('Single WebSocket error:', error);
            clearInterval(pingInterval); // Cleanup keepalive
        };

        wsRef.current = ws;

        // Cleanup function: close the WebSocket connection when the component unmounts
        // or when organizationId or myAgentId changes (triggering a re-run of this effect)
        return () => {
            if (wsRef.current) {
                console.log('[useEffect - WS Connection Cleanup] Closing WebSocket on unmount/dependency change.');
                wsRef.current.close();
            }
            clearInterval(pingInterval); // Cleanup keepalive
        };
    }, [organizationId, myAgentId]); // Dependencies for single WebSocket

    // Effect to send SET_ACTIVE_CHANNEL message when selectedChannel changes
    // This tells the backend which channel to send real-time messages to (NOT for assignment)
    useEffect(() => {
        console.log('[useEffect - SET_ACTIVE_CHANNEL] selectedChannel changed for real-time messaging:', selectedChannel);
        // DO NOT send SET_ACTIVE_CHANNEL when just selecting for viewing
        // SET_ACTIVE_CHANNEL should only be sent when explicitly assigning
        console.log('[useEffect - SET_ACTIVE_CHANNEL] NOT sending SET_ACTIVE_CHANNEL - this is just for viewing');
    }, [selectedChannel, availableChannels]); // Include availableChannels as dependency

    // Effect to fetch initial messages when selectedChannel or organizationId changes
    useEffect(() => {
        if (selectedChannel && organizationId) {
            const getMessages = async () => {
                try {
                    console.log(`[getMessages] Fetching messages for channel: ${selectedChannel}`);
                    const response = await axios.get(`${API_BASE}/messages/${selectedChannel}?organizationId=${organizationId}`);
                    // Skip the first message (index 0) as it's a summary message
                    const actualMessages = response.data.messages.slice(1);
                    
                    console.log(`[getMessages] Received ${actualMessages.length} actual messages (excluding summary)`);
                    
                    // Only process if there are actual messages after skipping the summary
                    if (actualMessages && actualMessages.length > 0) {
                        // Separate historical messages (using 'sender' key) from current conversation (using 'from' key)
                        const historicalMessages = [];
                        const currentMessages = [];
                        
                        actualMessages.forEach(msg => {
                            if (msg.sender && !msg.from) {
                                // Historical message using 'sender' key
                                historicalMessages.push({
                                    ...msg,
                                    from: msg.sender === myAgentId ? 'agent' : 'user',
                                    time: new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                                    isHistorical: true // Mark as historical
                                });
                            } else if (msg.from) {
                                // Current live conversation using 'from' key
                                currentMessages.push({
                                    ...msg,
                                    time: new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                                    isHistorical: false // Mark as current
                                });
                            }
                        });
                        
                        // Combine messages with a separator if both historical and current messages exist
                        let combinedMessages = [];
                        if (historicalMessages.length > 0) {
                            combinedMessages.push(...historicalMessages);
                            // Always add separator after historical messages
                            combinedMessages.push({
                                id: 'separator',
                                type: 'separator',
                                text: '--- Chatbot Conversation ended here ---',
                                time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                                isSeparator: true
                            });
                        }
                        if (currentMessages.length > 0) {
                            combinedMessages.push(...currentMessages);
                        }
                        
                        console.log(`[getMessages] Setting ${combinedMessages.length} messages (${historicalMessages.length} historical, ${currentMessages.length} current)`);
                        setMessages(combinedMessages);
                    } else {
                        // No actual messages, but don't clear existing messages to avoid closing chat
                        console.log('[getMessages] No actual messages found (only summary exists), keeping current messages.');
                        // Don't call setMessages here to avoid clearing existing messages
                    }
                } catch (error) {
                    console.error('[getMessages] Error fetching messages:', error);
                    // Don't clear messages on error to avoid closing chat
                    // Don't call setMessages here to avoid clearing existing messages
                }
            };
            getMessages();
        } else {
            console.log('[getMessages] No selectedChannel or organizationId, clearing messages');
            setMessages([]);
        }
    }, [selectedChannel, organizationId, myAgentId]);

    // Separate effect to update currentChatStatus when selectedChannel or availableChannels changes
    useEffect(() => {
        const currentChat = availableChannels.find(c => c.id === selectedChannel);
        
        // Only update currentChatStatus if we found the channel or if selectedChannel is empty
        // This prevents the chat from closing when availableChannels is temporarily empty during updates
        if (currentChat) {
            setCurrentChatStatus(currentChat);
            console.log('[useEffect - Update currentChatStatus] currentChatStatus set to:', currentChat);
        } else if (!selectedChannel) {
            // Only clear currentChatStatus if there's no selectedChannel
            setCurrentChatStatus(null);
            console.log('[useEffect - Update currentChatStatus] currentChatStatus cleared (no selectedChannel)');
        } else {
            // If we have a selectedChannel but it's not in availableChannels yet, keep the current status
            console.log('[useEffect - Update currentChatStatus] Keeping current status - selectedChannel exists but not in availableChannels yet');
        }
    }, [selectedChannel, availableChannels]);

    // Effect for auto-scrolling to the bottom of the messages container
    useEffect(() => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
    }, [messages]); // Scroll whenever messages state updates

    // Function to send a new message
    const sendMessage = async () => {
        console.log('[sendMessage] Attempting to send message...');
        if (!newMessage.trim()) {
            // Prevent sending empty or whitespace-only messages
            console.log('[sendMessage] Empty message, not sending.');
            return;
        }
        if (newMessage.trim() && selectedChannel && myAgentId.trim() && organizationId) {
            const messageText = newMessage.trim(); // Store the message text before clearing
            const clientId = Date.now() + '-' + Math.random();
            const payload = {
                organizationId: organizationId,
                userId: selectedChannel,
                message: messageText,
                type: 'sent', // Assuming agent sends messages of type 'sent'
                sender: myAgentId,
                clientId, // Attach clientId to payload
            };

            console.log('[sendMessage] Creating optimistic message...');
            // Optimistically add the message to the UI
            const optimisticMsg = {
                ...payload,
                from: 'agent',
                text: messageText,
                time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                timestamp: Date.now(),
                isOptimistic: true, // Mark as optimistic for potential cleanup
                isHistorical: false // Mark as current conversation
            };
            console.log('[sendMessage] Adding optimistic message to UI:', optimisticMsg);
            setMessages(prev => [...prev, optimisticMsg]);

            setNewMessage('');
            console.log('[sendMessage] Cleared newMessage input.');

            try {
                console.log('--- Frontend Sending Message ---');
                console.log('Outgoing message payload (to backend):', payload);
                // Changed API endpoint to match new backend structure
                await axios.post(`${API_BASE}/send-message`, payload);
                console.log('[sendMessage] Message sent successfully to backend (HTTP POST acknowledged).');
                console.log('[sendMessage] Waiting for WebSocket confirmation...');
                
                // Set a timeout to clean up optimistic message if WebSocket confirmation doesn't come
                setTimeout(() => {
                    setMessages(prev => {
                        const optimisticMessage = prev.find(msg => 
                            msg.isOptimistic && 
                            msg.text === messageText && 
                            msg.from === 'agent'
                        );
                        if (optimisticMessage) {
                            console.log('[sendMessage] Timeout reached, keeping optimistic message as fallback.');
                            // Remove the optimistic flag since we're keeping it
                            return prev.map(msg => 
                                msg === optimisticMessage 
                                    ? { ...msg, isOptimistic: false }
                                    : msg
                            );
                        }
                        return prev;
                    });
                }, 3000); // 3 second timeout
                
            } catch (error) {
                console.error('[sendMessage] Error sending message via HTTP POST:', error.response?.data || error.message);
                // Optionally remove the optimistic message on error
                console.log('[sendMessage] Removing optimistic message due to error.');
                setMessages(prev => prev.slice(0, -1));
                setNewMessage(messageText); // Revert message on error using stored messageText
            }
        } else {
            console.warn('[sendMessage] Cannot send message: Missing message text, selected channel, agent ID, or organization ID.');
            console.warn(`[sendMessage] Debug info - newMessage: "${newMessage}", selectedChannel: "${selectedChannel}", myAgentId: "${myAgentId}", organizationId: "${organizationId}"`);
        }
    };

    // ORIGINAL FUNCTION: Close a chat (keeps its original behavior but is no longer called by the button)
    const closeChat = async () => {
        console.log('[closeChat] Initiating close chat process.');
        if (!selectedChannel || !organizationId || !myAgentId) {
            toast.error('Error: Please select a chat and ensure Organization ID and Agent ID are set.');
            console.error('[closeChat] Missing selectedChannel, organizationId, or myAgentId.');
            return;
        }

        const reason = prompt('Please enter a reason for closing this chat:');
        if (reason === null) { // User clicked cancel on the prompt
            console.log('[closeChat] Close chat action cancelled by user.');
            return;
        }
        // Trim reason and provide a default if empty
        const finalReason = reason.trim() === '' ? 'No reason provided' : reason.trim();
        console.log(`[closeChat] User provided reason: "${finalReason}"`);

        try {
            console.log(`[closeChat] Sending POST request to /close-chat for channel: ${selectedChannel}`);
            const response = await axios.post(`${API_BASE}/close-chat`, {
                organizationId,
                channelId: selectedChannel,
                closingAgentId: myAgentId,
                closeReason: finalReason
            });
            console.log('[closeChat] Backend response for close chat:', response.data);
            toast.error(`Chat "${selectedChannel}" closed successfully!`);

            // Clear frontend state to reflect closure
            setAndStoreSelectedChannel(''); // Use custom setter to clear from state and localStorage
            setMessages([]); // Clear messages
            setCurrentChatStatus(null);
            console.log('[closeChat] Frontend state cleared: selectedChannel, messages, currentChatStatus.');

            // The WebSocket update will trigger fetchChannels() to update the list,
            // ensuring the closed chat's status is reflected.
            console.log('[closeChat] Awaiting CHANNEL_LIST_UPDATE from backend to re-fetch channels.');

        } catch (error) {
            console.error('[closeChat] Error closing chat:', error);
            const errorMessage = error.response?.data?.error || error.message || 'Unknown error';
            toast.error(`Failed to close chat: ${errorMessage}`);
            console.error('[closeChat] Detailed error:', error.response?.data);
        }
    };

    // NEW FUNCTION: Release a chat channel
    const releaseChat = async (channelId) => {
        console.log('[releaseChat] Initiating release chat process.');
        if (!channelId || !organizationId || !myAgentId) {
            toast.error('Error: Please ensure Organization ID and Agent ID are set.');
            console.error('[releaseChat] Missing channelId, organizationId, or myAgentId.');
            return;
        }

        try {
            console.log(`[releaseChat] Sending POST request to /release-channel for channel: ${channelId}`);
            const response = await axios.post(`${API_BASE}/release-channel`, {
                organizationId,
                channelId: channelId,
                releasingAgentId: myAgentId,
            });
            console.log('[releaseChat] Backend response for release chat:', response.data);
            toast.success(`Chat "${channelId}" released successfully!`);

            // Clear frontend state to reflect release
            if (selectedChannel === channelId) {
                setAndStoreSelectedChannel(''); // Use custom setter to clear from state and localStorage
                setMessages([]); // Clear messages
                setCurrentChatStatus(null);
            }
            console.log('[releaseChat] Frontend state cleared: selectedChannel, messages, currentChatStatus.');

            // The WebSocket update will trigger fetchChannels() to update the list,
            // ensuring the released chat's status is reflected as 'open' or available.
            console.log('[releaseChat] Awaiting CHANNEL_LIST_UPDATE from backend to re-fetch channels.');

        } catch (error) {
            console.error('[releaseChat] Error releasing chat:', error);
            const errorMessage = error.response?.data?.error || error.message || 'Unknown error';
            toast.error(`Failed to release chat: ${errorMessage}`);
            console.error('[releaseChat] Detailed error:', error.response?.data);
        }
    };

    const endChat = async () => {
        if (!selectedChannel || !organizationId || !myAgentId) return;
        if (!window.confirm('Are you sure you want to end this chat? This will delete the channel and all its messages.')) return;
        try {
            await axios.delete(`${API_BASE}/channel`, {
                data: {
                    organizationId,
                    channelId: selectedChannel,
                    agentId: myAgentId,
                },
            });
            setAndStoreSelectedChannel('');
            setMessages([]);
            setCurrentChatStatus(null);
            toast.success('Chat ended and channel deleted successfully.');
        } catch (err) {
            toast.error('Failed to end chat: ' + (err.response?.data?.error || err.message));
        }
    }

    // NEW FUNCTION: Assign a chat channel to yourself
    const assignChat = async (channelId) => {
        console.log('[assignChat] Initiating assign chat process.');
        if (!channelId || !organizationId || !myAgentId) {
            toast.error('Error: Please ensure Organization ID and Agent ID are set.');
            console.error('[assignChat] Missing channelId, organizationId, or myAgentId.');
            return;
        }

        // Set assignment in progress flag to prevent chat window from closing
        setAssignmentInProgress(true);
        console.log('[assignChat] Set assignmentInProgress = true');

        try {
            // Immediately update local state to reflect assignment
            // This ensures the chat window stays open during the assignment process
            setAndStoreSelectedChannel(channelId);
            setCurrentChatStatus(prev => ({
                ...prev,
                id: channelId,
                status: 'assigned',
                assignedTo: myAgentId
            }));

            console.log(`[assignChat] Sending POST request to /assign-channel for channel: ${channelId}`);
            const response = await axios.post(`${API_BASE}/assign-channel`, {
                organizationId,
                channelId: channelId,
                targetAgentId: myAgentId,
            });
            console.log('[assignChat] Backend response for assign chat:', response.data);
            toast.success(`Chat "${channelId}" assigned to you successfully!`);

            // Update available channels list to reflect assignment
            setAvailableChannels(prevChannels => 
                prevChannels.map(channel => 
                    channel.id === channelId 
                        ? { ...channel, status: 'assigned', assignedTo: myAgentId }
                        : channel
                )
            );

            // Fetch messages immediately for the assigned channel
            try {
                const messagesResponse = await axios.get(`${API_BASE}/messages/${channelId}?organizationId=${organizationId}`);
                // Skip the first message (index 0) as it's a summary message
                const actualMessages = messagesResponse.data.messages.slice(1);
                
                // Only process if there are actual messages after skipping the summary
                if (actualMessages && actualMessages.length > 0) {
                    // Separate historical messages (using 'sender' key) from current conversation (using 'from' key)
                    const historicalMessages = [];
                    const currentMessages = [];
                    
                    actualMessages.forEach(msg => {
                        if (msg.sender && !msg.from) {
                            // Historical message using 'sender' key
                            historicalMessages.push({
                                ...msg,
                                from: msg.sender === myAgentId ? 'agent' : 'user',
                                time: new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                                isHistorical: true // Mark as historical
                            });
                        } else if (msg.from) {
                            // Current live conversation using 'from' key
                            currentMessages.push({
                                ...msg,
                                time: new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                                isHistorical: false // Mark as current
                            });
                        }
                    });
                    
                    // Combine messages with a separator if both historical and current messages exist
                    let combinedMessages = [];
                    if (historicalMessages.length > 0) {
                        combinedMessages.push(...historicalMessages);
                        // Always add separator after historical messages
                        combinedMessages.push({
                            id: 'separator',
                            type: 'separator',
                            text: '--- Chatbot Conversation ended here---',
                            time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                            isSeparator: true
                        });
                    }
                    if (currentMessages.length > 0) {
                        combinedMessages.push(...currentMessages);
                    }
                    
                    setMessages(combinedMessages);
                    console.log('[assignChat] Messages fetched and displayed immediately after assignment.');
                }
            } catch (error) {
                console.error('[assignChat] Error fetching messages after assignment:', error);
                // Don't clear messages on error to avoid closing chat
            }

            // Send SET_ACTIVE_CHANNEL for real-time messaging (ONLY when explicitly assigning)
            if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
                console.log(`[assignChat] Sending SET_ACTIVE_CHANNEL for real-time messaging: ${channelId}`);
                wsRef.current.send(JSON.stringify({ type: 'SET_ACTIVE_CHANNEL', channelId: channelId }));
            }

            // Clear assignment in progress flag after a short delay
            setTimeout(() => {
                setAssignmentInProgress(false);
                console.log('[assignChat] Set assignmentInProgress = false');
            }, 2000); // 2 second delay to allow for WebSocket updates

        } catch (error) {
            console.error('[assignChat] Error assigning chat:', error);
            const errorMessage = error.response?.data?.error || error.message || 'Unknown error';
            toast.error(`Failed to assign chat: ${errorMessage}`);
            console.error('[assignChat] Detailed error:', error.response?.data);
            
            // Revert local state changes on error
            setCurrentChatStatus(prev => ({
                ...prev,
                status: 'open',
                assignedTo: null
            }));
            
            // Clear assignment in progress flag on error
            setAssignmentInProgress(false);
            console.log('[assignChat] Set assignmentInProgress = false (error)');
        }
    };

    // NEW STATE TO TRACK MINIMIZED CHATS
    const [minimizedChats, setMinimizedChats] = useState({});

    // CHECK IF A CHAT IS MINIMIZED
    const isChatMinimized = (channelId) => {
        return minimizedChats[channelId] === true;
    };

    // TOGGLE MINIMIZE STATE OF A CHAT
    const toggleMinimize = (channelId) => {
        setMinimizedChats(prev => ({
            ...prev,
            [channelId]: !prev[channelId],
        }));
    };

    // Handle Enter key press to send message
    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            sendMessage();
        }
    };

    const handleKeyPressForChannel = (e, channelId) => {
        if (e.key === 'Enter') {
            sendMessageForChannel(channelId);
        }
    };

    const getChatStatusDisplay = (chat) => {
        if (chat && chat.status === 'assigned') {
            return `Assigned to ${chat.assignedTo}`;
        }
        if (chat && chat.status === 'closed') {
            return `Closed by ${chat.closedBy || 'N/A'} (Reason: ${chat.closeReason || 'N/A'})`;
        }
        return chat ? chat.status : 'N/A';
    };

    // NEW FUNCTION TO HANDLE SENDING MESSAGE FOR A SPECIFIC CHANNEL
    const sendMessageForChannel = async (channelId) => {
        console.log('[sendMessageForChannel] Attempting to send message for channel:', channelId);
        const messageText = chatStates[channelId]?.newMessage?.trim();
        if (messageText && channelId && myAgentId.trim() && organizationId.trim()) {
            const payload = {
                organizationId: organizationId,
                userId: channelId,
                message: messageText,
                type: 'sent',
                sender: myAgentId,
            };

            // OPTIMISTIC UPDATE: Immediately add the new message to the chat state
            setChatStates(prev => {
                const channelState = prev[channelId] || { messages: [], newMessage: '' };
                const newMessageEntry = {
                    ...payload,
                    from: 'agent',
                    time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                };
                return {
                    ...prev,
                    [channelId]: {
                        ...channelState,
                        messages: [...channelState.messages, newMessageEntry],
                    }
                };
            });

            try {
                console.log('--- Frontend Sending Message for Channel ---');
                console.log('Outgoing message payload (to backend):', payload);
                await axios.post(`${API_BASE}/send-message`, payload);
                console.log('[sendMessageForChannel] Message sent successfully to backend (HTTP POST acknowledged).');
            } catch (error) {
                console.error('[sendMessageForChannel] Error sending message via HTTP POST:', error.response?.data || error.message);
                // ROLLBACK OPTIMISTIC UPDATE ON ERROR: Remove the last message from the state
                setChatStates(prev => {
                    const channelState = prev[channelId] || { messages: [], newMessage: '' };
                    return {
                        ...prev,
                        [channelId]: {
                            ...channelState,
                            messages: channelState.messages.slice(0, -1), // Remove last message
                        }
                    };
                });
            }

            // CLEAR THE INPUT FIELD AFTER SENDING
            setChatStates(prev => ({
                ...prev,
                [channelId]: {
                    ...prev[channelId],
                    newMessage: '',
                }
            }));
        } else {
            console.warn('[sendMessageForChannel] Cannot send message: Missing message text, selected channel, agent ID, or organization ID.');
        }
    };

    // NEW STATE TO TRACK CHAT STATES (INCLUDING MESSAGES AND INPUT)
    const [chatStates, setChatStates] = useState({});

    // INITIALIZE CHAT STATE FOR A CHANNEL
    const initializeChatState = (channelId) => {
        setChatStates(prev => ({
            ...prev,
            [channelId]: prev[channelId] || { messages: [], newMessage: '' },
        }));
    };

    // HANDLE INPUT CHANGE FOR A SPECIFIC CHANNEL
    const handleInputChange = (channelId, value) => {
        setChatStates(prev => ({
            ...prev,
            [channelId]: {
                ...prev[channelId],
                newMessage: value,
            }
        }));
    };

    return (
        <div className="min-h-screen w-full bg-gray-100 font-manrope">
            {/* Header */}
            <div className="bg-gradient-to-r from-[#1a3b2a] via-[#0f2a24] to-[#0b1f1b] text-white px-6 py-4 flex items-center justify-between">
                <button
                    onClick={() => { window.location.href = '/'; }}
                    className="px-3 py-1 flex items-center gap-1 text-[#CD0ADD] bg-white/10 hover:bg-[#CD0ADD]/90 hover:text-white rounded-full transition-all duration-150 text-xs font-semibold shadow-sm backdrop-blur"
                >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" /></svg>
                    Go Back
                </button>
                
                <img src="/QuickTalkLogo.png" alt="QuickTalk" className="h-8 absolute left-1/2 transform -translate-x-1/2" />
                
                <button
                    onClick={() => setLogoutDialogOpen(true)}
                    className="px-3 py-1 flex items-center gap-2 text-[#CD0ADD] bg-white/10 hover:bg-[#CD0ADD]/90 hover:text-white rounded-full transition-all duration-150 text-xs font-semibold shadow-sm backdrop-blur"
                >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a2 2 0 01-2 2H7a2 2 0 01-2-2V7a2 2 0 012-2h4a2 2 0 012 2v1" /></svg>
                    Log Out
                </button>
            </div>

            {/* Main Content */}
            <div className="p-6">
                {/* Table Header */}
                <div className="mb-6">
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">Active Chats</h2>
                    <p className="text-gray-600">Manage and respond to customer conversations</p>
                </div>

                {/* Two Column Layout */}
                <div className="flex gap-6 h-[calc(100vh-200px)]">
                    {/* Left Column - Chat Table */}
                    <div className="w-2/5">
                        <div className="bg-white rounded-lg shadow-lg overflow-hidden h-full border border-gray-200">
                            {availableChannels.length === 0 ? (
                                <div className="text-center py-12">
                                    <div className="text-gray-400 text-lg mb-2">
                                        {organizationId ? 'No active chats available.' : 'Enter an Organization ID to see chats.'}
                                    </div>
                                    <div className="text-gray-500 text-sm">
                                        New customer conversations will appear here automatically.
                                    </div>
                                </div>
                            ) : (
                                <>
                                    {/* Table Header */}
                                    <div className="bg-gray-50 border-b border-gray-200 px-4 py-3">
                                        <div className="flex items-center text-sm font-semibold text-gray-700">
                                            <div className="w-2/5 pr-2">Chat ID</div>
                                            <div className="w-1/5 pr-2">Status</div>
                                            <div className="w-1/4 pr-2">Assigned To</div>
                                            {/* <div className="w-1/6">Actions</div> */}
                                            <div className="w-1/4">Topic</div>
                                        </div>
                                    </div>
                                    
                                    {/* Table Body */}
                                    <div className="overflow-y-auto h-[calc(100%-65px)]">
                                        {availableChannels.map((channel, index) => (
                                            <div
                                                key={channel.id}
                                                className={`px-4 py-3 border-b border-gray-100 hover:bg-blue-50 transition-colors cursor-pointer ${
                                                    selectedChannel === channel.id ? 'bg-blue-100 border-blue-300' : 
                                                    index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                                                }`}
                                                onClick={() => setAndStoreSelectedChannel(channel.id)}
                                            >
                                                <div className="flex items-center text-sm">
                                                    {/* Chat ID */}
                                                    <div className="w-2/5 pr-2">
                                                        <div className="flex items-center gap-2">
                                                            <div className="font-medium text-gray-900 text-xs leading-tight break-all">
                                                                {channel.id}
                                                            </div>
                                                            {channel.firstMessage && (
                                                                <div className="relative group">
                                                                    <svg 
                                                                        className="w-4 h-4 text-gray-400 hover:text-blue-500 cursor-help" 
                                                                        fill="none" 
                                                                        stroke="currentColor" 
                                                                        strokeWidth="2" 
                                                                        viewBox="0 0 24 24"
                                                                    >
                                                                        <path strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                    </svg>
                                                                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 min-w-[200px] max-w-[300px]">
                                                                        <div className="font-semibold text-[#CD0ADD] mb-1 break-words">Topic: {channel.firstMessage.topic_summary}</div>
                                                                        <div className="text-gray-300 break-words">{channel.firstMessage.chat_summary}</div>
                                                                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                    
                                                    {/* Status */}
                                                    <div className="w-1/5 pr-2">
                                                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-semibold whitespace-nowrap ${
                                                            channel.status === 'open' ? 'bg-green-100 text-green-800 border border-green-200' :
                                                            channel.status === 'assigned' ? 'bg-blue-100 text-blue-800 border border-blue-200' :
                                                            channel.status === 'closed' ? 'bg-red-100 text-red-800 border border-red-200' :
                                                            'bg-gray-100 text-gray-800 border border-gray-200'
                                                        }`}>
                                                            {channel.status}
                                                        </span>
                                                    </div>
                                                    
                                                    {/* Assigned To */}
                                                    <div className="w-1/4 pr-2 text-gray-700 text-xs">
                                                        {channel.status === 'assigned' && channel.assignedTo ? 
                                                            channel.assignedTo === myAgentId ? 
                                                                <span className="font-semibold text-blue-600 truncate block">You</span> : 
                                                                <span className="font-medium truncate block">{channel.assignedTo}</span>
                                                            : <span className="text-gray-500 italic truncate block">Unassigned</span>
                                                        }
                                                    </div>
                                                    
                                                    {/* Actions */}
                                                    {/* <div className="w-1/6 flex items-center gap-1">
                                                        {channel.status === 'assigned' && channel.assignedTo === myAgentId && (
                                                            <div className="flex items-center gap-1">
                                                                <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse shadow-sm"></div>
                                                                <span className="text-xs text-blue-600 font-semibold">A</span>
                                                            </div>
                                                        )}
                                                        {channel.status === 'open' && (
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    assignChat(channel.id);
                                                                }}
                                                                className="px-2 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-xs font-semibold shadow"
                                                                title="Assign to me"
                                                            >
                                                                Assign
                                                            </button>
                                                        )}
                                                    </div> */}
                                                    {/* Chat topic */}
                                                    <div className="w-1/4 flex items-center gap-1">
                                                        {channel.firstMessage.topic_summary ? channel.firstMessage.topic_summary : ''}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </>
                            )}
                        </div>
                    </div>

                    {/* Right Column - Chat Window */}
                    <div className="w-3/5">
                        {selectedChannel && organizationId ? (
                            <div className="bg-white rounded-lg shadow-lg overflow-hidden h-full flex flex-col">
                                {/* Chat Header */}
                                <div className="bg-gradient-to-r from-[#1a3b2a] via-[#0f2a24] to-[#0b1f1b] text-white px-6 py-4 flex items-center justify-between">
                                    <div className="flex items-center gap-4 w-full">
                                        <div className="font-bold text-lg truncate flex-1">{selectedChannel}</div>
                                        
                                        {/* Status Indicator */}
                                        {currentChatStatus && currentChatStatus.status === 'assigned' && currentChatStatus.assignedTo === myAgentId && (
                                            <div className="flex items-center gap-2">
                                                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                                <span className="text-green-400 text-sm font-semibold">Assigned to You</span>
                                            </div>
                                        )}
                                        {currentChatStatus && currentChatStatus.status === 'assigned' && currentChatStatus.assignedTo !== myAgentId && (
                                            <div className="flex items-center gap-2">
                                                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                                <span className="text-blue-400 text-sm font-semibold">Assigned to {currentChatStatus.assignedTo}</span>
                                            </div>
                                        )}
                                        {currentChatStatus && currentChatStatus.status === 'open' && (
                                            <div className="flex items-center gap-2">
                                                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                                <span className="text-yellow-400 text-sm font-semibold">Open - View Only</span>
                                            </div>
                                        )}
                                        
                                        {/* Action Buttons */}
                                        {currentChatStatus && currentChatStatus.status === 'assigned' && currentChatStatus.assignedTo === myAgentId && (
                                            <>
                                                <button
                                                    onClick={() => releaseChat(selectedChannel)}
                                                    className="px-3 py-1 bg-[#CD0ADD] hover:bg-purple-700 text-white rounded-lg text-xs font-semibold shadow ml-2"
                                                >
                                                    Release
                                                </button>
                                                <button
                                                    onClick={endChat}
                                                    className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-lg text-xs font-semibold shadow ml-2"
                                                >
                                                    End Chat
                                                </button>
                                            </>
                                        )}
                                        {currentChatStatus && currentChatStatus.status === 'open' && (
                                            <button
                                                onClick={() => assignChat(selectedChannel)}
                                                className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded-lg text-xs font-semibold shadow ml-2"
                                            >
                                                Assign to Me
                                            </button>
                                        )}
                                        {!currentChatStatus && (
                                            <span className="ml-2 px-3 py-1 bg-gray-700 text-white rounded-lg text-xs font-semibold shadow">Loading...</span>
                                        )}
                                    </div>
                                </div>
                                {/* Messages & Input: Show messages for all chats, but only allow input when assigned */}
                                <div className="flex-1 overflow-y-auto px-6 py-6 space-y-3 bg-gray-50" ref={messagesContainerRef}>
                                    {messages.length === 0 ? (
                                        <div className="text-center text-gray-400">No messages in this channel yet.</div>
                                    ) : (
                                        messages.map((message, index) => {
                                            // Handle separator
                                            if (message.isSeparator) {
                                                return (
                                                    <div key={`separator-${index}`} className="flex justify-center my-4">
                                                        <div className="bg-gray-200 text-gray-600 px-4 py-2 rounded-full text-xs font-medium border border-gray-300">
                                                            {message.text}
                                                        </div>
                                                    </div>
                                                );
                                            }
                                            
                                            // Handle regular messages
                                            // For historical messages, use 'sender' key; for current messages, use 'from' key
                                            const isUserMessage = message.isHistorical ? 
                                                message.sender === 'user' : 
                                                message.from === 'user';
                                            const isHistorical = message.isHistorical;
                                            
                                            return (
                                                <div
                                                    key={message.timestamp + index}
                                                    className={`flex ${isUserMessage ? 'justify-start' : 'justify-end'}`}
                                                >
                                                    <div className={`rounded-2xl px-4 py-2 max-w-[70%] text-sm shadow relative ${
                                                        isUserMessage 
                                                            ? `bg-white text-gray-800 border ${isHistorical ? 'border-gray-300 opacity-75' : 'border-[#CD0ADD]'} rounded-bl-md` 
                                                            : `bg-[#CD0ADD] text-white rounded-br-md ${isHistorical ? 'opacity-75' : ''}`
                                                    }`}>
                                                        <div>{message.text}</div>
                                                        <div className="text-xs mt-1 opacity-70 text-right flex items-center justify-end gap-1">
                                                            {message.time}
                                                            {message.isOptimistic && (
                                                                <div className="flex items-center gap-1">
                                                                    <div className="w-2 h-2 bg-white/50 rounded-full animate-pulse"></div>
                                                                    <span className="text-xs opacity-70">sending...</span>
                                                                </div>
                                                            )}
                                                            {isHistorical && (
                                                                <span className="text-xs opacity-50 italic">(history)</span>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })
                                    )}
                                </div>
                                
                                {/* Input area: Only show if agent is assigned */}
                                {currentChatStatus && currentChatStatus.status === 'assigned' && currentChatStatus.assignedTo === myAgentId ? (
                                    <div className="px-6 py-4 bg-white border-t border-[#CD0ADD]">
                                        <div className="flex gap-2 items-end">
                                            <input
                                                type="text"
                                                value={newMessage}
                                                onChange={(e) => setNewMessage(e.target.value)}
                                                onKeyPress={handleKeyPress}
                                                placeholder="Type your message..."
                                                className="flex-1 px-4 py-2 rounded-full border-2 border-[#CD0ADD] focus:border-purple-700 focus:outline-none bg-purple-50 text-black"
                                                disabled={currentChatStatus.status === 'closed'}
                                            />
                                            <button
                                                onClick={sendMessage}
                                                className="px-5 py-2 rounded-full font-semibold text-white bg-[#CD0ADD] hover:bg-purple-700 transition-all duration-200 shadow disabled:opacity-50 disabled:cursor-not-allowed"
                                                disabled={!newMessage.trim() || !selectedChannel || !myAgentId.trim() || !organizationId.trim() || currentChatStatus.status === 'closed'}
                                            >
                                                Send
                                            </button>
                                        </div>
                                    </div>
                                ) : currentChatStatus && currentChatStatus.status === 'open' ? (
                                    <div className="px-6 py-4 bg-gray-100 border-t border-gray-200">
                                        <div className="text-center text-gray-600 text-sm">
                                            💬 Chat messages are visible above. Click "Assign to Me" in the header to take ownership and respond to this chat.
                                        </div>
                                    </div>
                                ) : currentChatStatus && currentChatStatus.status === 'assigned' && currentChatStatus.assignedTo !== myAgentId ? (
                                    <div className="px-6 py-4 bg-gray-100 border-t border-gray-200">
                                        <div className="text-center text-gray-600 text-sm">
                                            💬 Chat messages are visible above. This chat is assigned to {currentChatStatus.assignedTo}.
                                        </div>
                                    </div>
                                ) : !currentChatStatus ? (
                                    <div className="px-6 py-4 bg-gray-100 border-t border-gray-200">
                                        <div className="text-center text-gray-600 text-sm">
                                            💬 Loading chat status...
                                        </div>
                                    </div>
                                ) : null}
                            </div>
                        ) : (
                            <div className="bg-white rounded-lg shadow-lg p-12 text-center h-full flex items-center justify-center">
                                <div className="text-gray-400 text-xl">
                                    Select a chat from the table to start messaging
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Logout Dialog */}
            <LogoutDialog
                isOpen={isLogoutDialogOpen}
                onClose={() => setLogoutDialogOpen(false)}
                onLogout={() => {
                    //   setSelectedOption("logout")
                    logout()
                }}
                loading={false}
            />
        </div>
    )
}

export default ChatList;