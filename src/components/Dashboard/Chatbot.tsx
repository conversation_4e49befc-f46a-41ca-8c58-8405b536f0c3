import React, { useRef, useState, useEffect } from "react"
import InputForm from "../Home/InputForm"
import MessageList from "../Home/MessageList"
import WavEncoder from "wav-encoder"
import userPool from "../../lib/userPoolConfig"
import LogoutDialog from "../oldui/LogoutDialog"
import axios from "axios"
import type { Message } from "../../types/chat"
import type { Document } from "langchain/document"
import toast from "react-hot-toast"
import { v4 as uuidv4 } from "uuid"
import WebRTCInterface from "../oldui/WebRTC"
import { Phone } from "lucide-react"

export default function ChatbotHome() {
  const [recorder, setRecorder] = useState<MediaRecorder | null>(null)
  const [webRtcValue, setWebRtcValue] = useState(null);
  const [recording, setRecording] = useState<boolean>(false)
  const [audioURL, setAudioURL] = useState<string | null>(null)
  const [detectedLanguage, setDetectedLanguage] = useState("")
  const [autoSubmit, setAutoSubmit] = useState(false)
  const [audioURL1, setAudioURL1] = useState<string | null>(null)
  const [query, setQuery] = useState<string>("")
  const [loading, setLoading] = useState<boolean>(false)
  const [isMobile, setIsMobile] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [fileUrl, setFileUrl] = useState("")
  const [configData, setConfigData] = useState<any>(null)
  const [clientid, setClientid] = useState(uuidv4())
  const [username, setUsername] = useState<string | null>(null)
  const [isWhisperCalled, setIsWhisperCalled] = useState(true)
  const [inputType, setInputType] = useState("text")
  const [options, setOptions] = useState<Instance[]>([])
  const [selectedOption, setSelectedOption] = useState("")
  const [logo, setLogo] = useState("") // State to hold the logo URL
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isAdmin, setIsAdmin] = useState(false)
  const [background, setBackground] = useState("")
  const [textcolor, setTextcolor] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [org_id, setOrg_id] = useState<string | null>(null)
  const [fontcolor, setFontcolor] = useState("")
  const [ismicro, setMicro] = useState(false)
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [isOtherResponseHandled, setIsOtherResponseHandled] = useState(false)
  const [useSecondApi, setUseSecondApi] = useState(false)
  const [lead_gen, setUseLead] = useState(false)
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [meta, setMeta] = useState(null)
  const [isAgentConnected, setIsAgentConnected] = useState(false)
  const wsRef = useRef<WebSocket | null>(null);
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [wsConnectionStatus, setWsConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [wsUrl, setWsUrl] = useState<string | null>(null);
  const [linkws, setLinkws] = useState<string>('');
  const [showWebRTC, setShowWebRTC] = useState(false)

  interface Instance {
    instance_id: string
    display_name: string
  }

  interface ChatMessage {
    question: string
    answer: string
  }

  interface SessionData {
    chat_history?: any[]
    history?: any[] // Adjust type if chat_history has a specific structure
    meta_data?: string | null
    useSecondApi?: boolean
    contactInfoProcessed?: boolean
    prompt?: string
  }

  interface Message {
    type: "userMessage" | "apiMessage";
    message: string;
    sourceDocs?: Document[];
    source?: string;
    button_values?: any;
    audioURL?: string;
    timestamp?: number;
    time?: string;
  }

  const toggleChatHistory = () => {
    window.open("/history", "_blank")
  }
  const [messageState, setMessageState] = useState<{
    messages: Message[]
    pending?: string
    history: [string, string][]
    pendingSourceDocs?: Document[]
  }>({
    messages: [
      {
        message: "How can i assist you?",
        type: "apiMessage",
      },
    ],
    history: [],
  })
  const { messages, history } = messageState
  const messageListRef = useRef<HTMLDivElement>(null)
  const textAreaRef = useRef<HTMLTextAreaElement>(null)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setInputType("text");

    if (!query.trim()) {
      toast.error("Please input a question");
      return;
    }

    const question = query.trim();
    console.log("User Question:", question);
    setMessageState(prevState => ({
      ...prevState,
      messages: [
        ...prevState.messages,
        {
          type: "userMessage",
          message: question,
          timestamp: Date.now(),
          time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        }
      ]
    }));
    setLoading(true);
    setQuery("");

    try {
      if (!org_id) {
        toast.error("Organization ID is required");
        setLoading(false);
        return;
      }

      // If already connected to agent portal, send message through WebSocket
      if (isAgentConnected) {
        console.log('=== Attempting to send message via WebSocket ===');
        console.log('WebSocket readyState:', wsRef.current?.readyState);
        console.log('Connection status:', wsConnectionStatus);
        console.log('isWebSocketReady():', isWebSocketReady());

        const messagePayload = {
          organizationId: org_id,
          userId: sessionId,
          message: question,
          type: 'chat_message',
          sender: 'user',
          timestamp: Date.now(),
          audioData:'',
        };

        if (sendWebSocketMessage(messagePayload)) {
          setLoading(false);
          return;
        } else {
          console.log('=== WebSocket send failed, attempting to reconnect ===');

          // If WebSocket send failed, try to reconnect
          handleManualReconnect();

          // Continue with regular chat flow for now
          toast('WebSocket connection not ready. Attempting to reconnect...');
        }
      }

      // Regular chat flow
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_BACKEND_API}/api/handleUserQuery`, {
        question,
        org_id,
        sessionId,
        inputType,
        username,
        lead_gen,
        userEmail,
        button_click: sessionStorage.getItem("button_click") === "true",
      });

      const { message, button_values, source, sourceDocs, sessionId: newSessionId, metadata, function_name, linkws, link } = response.data;
      if (linkws) {
        setLinkws(linkws);
        console.log("WebSocket link received:", linkws);
      }
      // Check if this is an agent connection response
      if (function_name && (function_name === 'need_human_agent' ||
        (Array.isArray(function_name) && function_name.includes('need_human_agent')))) {
        console.log('=== Agent Connection Response ===');
        console.log('Message:', message);
        console.log('Function Name:', function_name);
        console.log('=============================');

        setSessionId(newSessionId);
        setMeta(metadata);
        setIsAgentConnected(true);
        if (linkws) {
          setWsUrl(linkws);
        }

        // Add the response message to chat
        setMessageState((prevState) => ({
          ...prevState,
          messages: [
            ...prevState.messages,
            {
              type: "apiMessage",
              message: message,
              source,
              button_values
            },
          ],
        }));

        // Send the initial message through WebSocket
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          const messagePayload = {
            organizationId: org_id,
            userId: newSessionId,
            message: question,
            type: 'chat_message',
            sender: 'user',
            timestamp: Date.now()
          };
          sendWebSocketMessage(messagePayload);
        }
      } else {
        // Handle regular chat responses
        setSessionId(newSessionId);
        setMeta(metadata);
        setMessageState((prevState) => ({
          ...prevState,
          messages: [
            ...prevState.messages,
            {
              type: "apiMessage",
              message,
              sourceDocs,
              source,
              button_values,
            },
          ],
        }));
      }
    } catch (error) {
      console.error("Error processing the request: ", error);
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          const errorMessage = error.response.data.error || "I can assist you in this thing only";
          const newSessionId = error.response.data.sessionId;

          if (newSessionId) {
            setSessionId(newSessionId);
          }
          setMessageState((prevState) => ({
            ...prevState,
            messages: [
              ...prevState.messages,
              {
                type: "apiMessage",
                message: errorMessage,
                source: "error",
              },
            ],
          }));
        } else if (error.response?.status === 500) {
          const errorMessage = "An error occurred while processing your request";
          toast.error(errorMessage);
          setError(errorMessage);
        }
      } else {
        const errorMessage = "Error processing your request";
        toast.error(errorMessage);
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
      messageListRef.current?.scrollTo(0, messageListRef.current.scrollHeight);
    }
  };

  useEffect(() => {
    if (autoSubmit) {
      // Create a fake Event object and pass it to handleSubmit
      const mockEvent = {
        preventDefault: () => { },
      } as React.FormEvent<HTMLFormElement> // Explicitly typing as HTMLFormElement
      handleSubmit(mockEvent)
      // Reset autoSubmit flag
      setAutoSubmit(false)
    }
  }, [autoSubmit])
  useEffect(() => {
    // Fetch the config.json file
    axios.get("/config.json").then((response) => {
      setConfigData(response.data)
    })
  }, [])

  const fetchWebRTCValue = async () => {
    try {
      const response = await axios.post(
        'https://devquicktalkdbwrapper.najoomi.ai/get/web_rtc_value',
        { organization_id: org_id }
      );
      setWebRtcValue(response.data.web_rtc_value);
    } catch (error) {
      console.error('Error fetching WebRTC value:', error);
      setWebRtcValue(null);
    }
  };

  // Add effect to refetch WebRTC value when organizationId changes
  useEffect(() => {
    if (org_id) {
      fetchWebRTCValue();
    }
  }, [org_id]);


  const fetchOrganizationId = async (savedUsername: string) => {
    if (!savedUsername) {
      setError("Username not found in localStorage")
      return
    }
    if (savedUsername) {
      setUsername(savedUsername)
      fetchOptions(savedUsername)
    } else {
      setIsLoading(true)
    }
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get_organization_info`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username: savedUsername }),
      })
      if (!response.ok) {
        throw new Error(`Failed to fetch: ${response.statusText}`)
      }
      const data = await response.json()
      setOrg_id(data.organizationId)
    } catch (error) {
      setError(`Error fetching organizationId: ${error}`)
    }
  }
  const fetchLogo = async (savedUsername: string) => {
    if (!savedUsername) {
      console.error("No username found in localStorage")
      return
    }
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get_user_settings`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username: savedUsername }),
      })
      if (!response.ok) {
        throw new Error("Failed to fetch settings")
      }
      const data = await response.json()
      // Assuming the logo_base64 is in the 'logo_base64' field of the response
      setLogo(data.logo_base64)
      setBackground(data.background_color)
      setFontcolor(data.font_color)
      setTextcolor(data.textfield)
      console.log(data.logo_base64) // Log the base64 string for debugging
      console.log(data.background_color) // Log the base64 string for debugging
    } catch (error) {
      console.error("Error fetching logo:", error)
    }
  }

  const fetchleadgenration = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/settings/botOptions/get_update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ organisation_id: org_id }),
      });

      const data = await response.json();
      const leadValue = Array.isArray(data.lead_generation) ? data.lead_generation[0] : data.lead_generation
      const admin_email = Array.isArray(data.admin_email) ? data.admin_email[0] : data.admin_email
      console.log("Lead Value:", leadValue);
      setUserEmail(admin_email)
      setUseLead(Boolean(leadValue));
    } catch (err) {
      console.error("Error fetching lead generation:", err);
    }
  };

  const fetchOptions = async (username: string) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get_pinecone_instances`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username }),
      })
      if (response.ok) {
        const data = await response.json()
        console.log("Ins", data)
        setOptions(data.instances || []) // Assuming your API returns an array in 'instances'
        // Auto-select the first instance if available
        if (data.instances && data.instances.length > 0) {
          setSelectedOption(data.instances[0].instance_id) // Auto-select the first instance
        }
      } else {
        console.error("Failed to fetch options:", response.statusText)
      }
    } catch (error) {
      console.error("Error fetching options:", error)
    }
  }
  useEffect(() => {
    const fetchData = async () => {
      const savedUsername = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY as string)
      if (!savedUsername) {
        setError("Username not found in localStorage")
        setIsLoading(false)
        return
      }
      setUsername(savedUsername)
      try {
        await fetchOrganizationId(savedUsername)
        await fetchLogo(savedUsername)
        await fetchOptions(savedUsername)
      } catch (error) {
        console.error("Error in data fetching:", error)
        setError("Failed to fetch data")
      } finally {
        setIsLoading(false)
      }
    }
    fetchData()
  }, [])

  useEffect(() => {
    if (org_id) {
      fetchleadgenration()
    }
  }, [org_id])

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 600) // Adjust the threshold as needed
    }

    handleResize() // Set initial value
    window.addEventListener("resize", handleResize)
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedOption(event.target.value)
  }

  const logout = async () => {
    setLoading(true)
    const user = userPool.getCurrentUser()
    if (user) {
      console.log("Signing out user:", user.getUsername())
      localStorage.removeItem("username")
      localStorage.removeItem("email")
      user.signOut()
      window.location.href = "/"
    } else {
      console.log("No user to sign out")
    }
  }

  const handleButtonClick = (buttonValue: string) => {
    setQuery(buttonValue)
    // Store that this is a button click in sessionStorage
    sessionStorage.setItem("button_click", "true")
    setAutoSubmit(true)
  }
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 600) // Adjust the threshold as needed
    }

    handleResize() // Set initial value
    window.addEventListener("resize", handleResize)
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  // Move this outside the function, at the component level
  const shouldProcessAudio = useRef(true)

  const handleMicClick = async (action: "start" | "stop" | "cancel") => {
    if (action === "start") {
      if (!navigator.mediaDevices?.getUserMedia) {
        toast.error("Microphone access not supported")
        return
      }
      shouldProcessAudio.current = true // Reset when starting
      setMicro(true)
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        const newRecorder = new MediaRecorder(stream)
        const chunks: Blob[] = []

        newRecorder.ondataavailable = (e: BlobEvent) => {
          chunks.push(e.data)
        }

        newRecorder.onstop = async () => {
          stream.getTracks().forEach((track) => track.stop())

          if (!shouldProcessAudio.current) {
            chunks.length = 0 // Clear chunks
            setMicro(false)
            return
          }

          try {
            const blob = new Blob(chunks, { type: "audio/webm" })
            const arrayBuffer = await blob.arrayBuffer()
            const audioContext = new (window.AudioContext || window.webkitAudioContext)()

            if (audioContext.state === "suspended") {
              await audioContext.resume()
            }

            const decodedData = await audioContext.decodeAudioData(arrayBuffer)
            const wavData = {
              sampleRate: decodedData.sampleRate,
              channelData: Array.from({ length: decodedData.numberOfChannels }, (_, i) =>
                decodedData.getChannelData(i),
              ),
            }

            const wavBuffer: ArrayBuffer = await WavEncoder.encode(wavData)
            const wavBlob = new Blob([wavBuffer], { type: "audio/wav" })
            const reader = new FileReader()

            reader.onloadend = async () => {
              const base64data = reader.result?.toString().split(",")[1]

              toast.promise(
                (async () => {
                  const sentAudioURL = URL.createObjectURL(wavBlob)
                  setMessageState((state) => ({
                    ...state,
                    messages: [
                      ...state.messages,
                      {
                        type: "userMessage",
                        message: "",
                        audioURL: sentAudioURL,
                      },
                    ],
                  }))

                  // Check if connected to agent portal and WebSocket is ready
                  if (isAgentConnected && isWebSocketReady()) {
                    console.log('=== Sending voice message via WebSocket ===');

                    const voiceMessagePayload = {
                      organizationId: org_id,
                      userId: sessionId,
                      message: "", // Empty text message for voice
                      audioData: base64data, // Send audio data
                      type: 'voice_message',
                      sender: 'user',
                      timestamp: Date.now(),
                      audioFormat: 'wav',
                      sampleRate: decodedData.sampleRate,
                      channels: decodedData.numberOfChannels
                    };

                    if (sendWebSocketMessage(voiceMessagePayload)) {
                      console.log('Voice message sent via WebSocket');
                      setSessionId(sessionId); // Keep current session
                      return; // Exit early, don't process through regular API
                    } else {
                      console.log('WebSocket voice send failed, falling back to API');
                      // Continue with regular API flow below
                    }
                  }

                  // Regular API flow for voice processing
                  const response = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_API}/api/voice`, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ audio: base64data, session_id: sessionId, org_id, type: "voice", username }),
                  })

                  if (!response.ok) {
                    throw new Error("Failed to fetch voice data.")
                  }

                  const data = await response.json()
                  setSessionId(data.session_id)
                  const responseAudio = data.voice_message_base64
                  const textMessages = data.text_reply
                  const buttonValues = data.button_values

                  // --- AGENT CONNECTION LOGIC FOR VOICE ---
                  if (data.linkws) {
                    setLinkws(data.linkws);
                    console.log("WebSocket link received (voice):", data.linkws);
                  }
                  if (data.function_name && (data.function_name === 'need_human_agent' || (Array.isArray(data.function_name) && data.function_name.includes('need_human_agent')))) {
                    console.log('=== Agent Connection Response (voice) ===');
                    console.log('Message:', textMessages);
                    console.log('Function Name:', data.function_name);
                    console.log('=============================');

                    setSessionId(data.session_id);
                    setMeta(data.metadata);
                    setIsAgentConnected(true);
                    if (data.linkws) {
                      setWsUrl(data.linkws);
                    }
                    setMessageState((prevState) => ({
                      ...prevState,
                      messages: [
                        ...prevState.messages,
                        {
                          type: "apiMessage",
                          message: textMessages,
                          source: data.source,
                          button_values: buttonValues
                        },
                      ],
                    }));
                    // Send the initial message through WebSocket if open
                    if (wsRef.current?.readyState === WebSocket.OPEN) {
                      const messagePayload = {
                        organizationId: org_id,
                        userId: data.session_id,
                        message: '', // No text, this was a voice message
                        type: 'chat_message',
                        sender: 'user',
                        timestamp: Date.now()
                      };
                      sendWebSocketMessage(messagePayload);
                    }
                    return; // Do not continue with regular message handling
                  }
                  // --- END AGENT CONNECTION LOGIC FOR VOICE ---

                  if (responseAudio && textMessages) {
                    const audioBlob = new Blob(
                      [
                        new Uint8Array(
                          atob(responseAudio)
                            .split("")
                            .map((c) => c.charCodeAt(0)),
                        ),
                      ],
                      { type: "audio/webm" },
                    )
                    const audioUrl = URL.createObjectURL(audioBlob)
                    setAudioURL(audioUrl)
                    setMessageState((state) => ({
                      ...state,
                      messages: [
                        ...state.messages,
                        {
                          type: "apiMessage",
                          message: textMessages,
                          audioURL: audioUrl,
                          sourceDocs: data.sourceDocuments,
                          button_values: buttonValues,
                        },
                      ],
                    }))
                  } else if (responseAudio) {
                    const audioBlob = new Blob(
                      [
                        new Uint8Array(
                          atob(responseAudio)
                            .split("")
                            .map((c) => c.charCodeAt(0)),
                        ),
                      ],
                      { type: "audio/webm" },
                    )
                    const audioUrl = URL.createObjectURL(audioBlob)
                    setAudioURL(audioUrl)
                    setMessageState((state) => ({
                      ...state,
                      messages: [
                        ...state.messages,
                        {
                          type: "apiMessage",
                          message: "",
                          audioURL: audioUrl,
                          sourceDocs: data.sourceDocuments,
                          button_values: buttonValues,
                        },
                      ],
                    }))
                  } else if (textMessages) {
                    setMessageState((state) => ({
                      ...state,
                      messages: [
                        ...state.messages,
                        {
                          type: "apiMessage",
                          message: textMessages,
                          audioURL: "",
                          sourceDocs: data.sourceDocuments,
                          button_values: buttonValues,
                        },
                      ],
                    }))
                  }

                  if (data.error) {
                    setError(data.error)
                    throw new Error(data.error)
                  }
                })(),
                {
                  loading: "Processing audio...",
                  success: "Audio processed successfully!",
                  error: "Failed to process audio. Please try again.",
                },
              )
            }

            reader.readAsDataURL(wavBlob)
          } catch (error) {
            console.error("Error processing audio data:", error)
            toast.error("Failed to process audio data.")
          }
        }

        newRecorder.start()
        setRecorder(newRecorder)
        setRecording(true)
      } catch (error) {
        console.error("Error accessing microphone:", error)
        toast.error("Failed to access microphone.")
        setMicro(false)
      }
    } else if (action === "stop" || action === "cancel") {
      shouldProcessAudio.current = action === "stop"

      if (recorder && recorder.state === "recording") {
        recorder.stop()
      }
      setRecorder(null)
      setRecording(false)
      setMicro(false)

      if (action === "stop") {
        setInputType("voice")
      }
    }
  }

  const handleEnter = (e: any) => {
    if (e.key === "Enter" && query) {
      handleSubmit(e)
    } else if (e.key == "Enter") {
      e.preventDefault()
    }
  }

  const refreshContext = async () => {
    setClientid(uuidv4())
    setQuery("")
    setAudioURL1("")
    setUseSecondApi(false)
    setSessionId(null)
    setIsOtherResponseHandled(false)
    setIsWhisperCalled(false)
    setMessageState({
      messages: [],
      history: [],
    })
    try {
      // Clear session data from Redis
      const redisClearResponse = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_API}/api/redis-clear`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionId,
        }),
      })
      const redisClearData = await redisClearResponse.json()

      // Check if the error is about session expiration
      if (redisClearData.error && redisClearData.error.includes("Session is expired")) {
        setMessageState({
          messages: [
            {
              type: "apiMessage",
              message: "Session expired. Please refresh the page.",
            },
          ],
          history: [],
        });
        return;
      }

      if (redisClearData.error) {
        throw new Error(redisClearData.error)
      }

      // Proceed with refreshing chat API
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_API}/api/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          question: "", // Optional initial question
          history: [],
          clientid: clientid,
        }),
      })
      const data = await response.json()
      console.log("Chat API response:", data)
      if (data.error) {
        setError(data.error)
        toast.error("Error: " + data.error)
      } else {
        toast.success("Refreshed context successfully!")
        setMessageState((prevState) => ({
          messages: [
            {
              type: "apiMessage",
              message: "How can I assist you?",
              sourceDocs: data.sourceDocuments,
            },
          ],
          history: [], // Reset history
        }))
      }
      setLoading(false)
      // Scroll to bottom
      messageListRef.current?.scrollTo(0, messageListRef.current.scrollHeight)
    } catch (error) {
      setLoading(false)
      setError("An error occurred while refreshing the context. Please try again.")
      toast.error("Error refreshing context: " + (error instanceof Error ? error.message : "Unknown error"))
      console.error("Error:", error)
    }
  }

  // Add WebSocket setup effect
  useEffect(() => {
    if (!isAgentConnected) return;

    // Close any existing WebSocket connection before creating a new one
    if (wsRef.current) {
      wsRef.current.close();
    }

    if (!org_id || !sessionId) {
      console.log('Skipping WebSocket connection: org_id or sessionId is missing.');
      return;
    }

    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5; // Increased from 3 to 5
    const reconnectDelay = 2000; // 2 seconds
    let heartbeatInterval: NodeJS.Timeout | null = null;
    let connectionTimeout: NodeJS.Timeout | null = null;
    let isConnecting = false;

    const connectWebSocket = () => {
      if (isConnecting) {
        console.log('Already attempting to connect, skipping...');
        return;
      }

      if (!wsUrl) {
        console.log('No WebSocket URL available');
        return;
      }

      isConnecting = true;
      setWsConnectionStatus('connecting');

      try {
        // Create a new WebSocket connection using the received URL
        const ws = new WebSocket(linkws);
        // const ws = new WebSocket(`wss://87402b1c4894.ngrok-free.app/quicktalk/ws?organizationId=${org_id}&userId=${sessionId}`);

        // Set connection timeout
        connectionTimeout = setTimeout(() => {
          if (ws.readyState === WebSocket.CONNECTING) {
            console.log('WebSocket connection timeout');
            ws.close();
            isConnecting = false;
            setWsConnectionStatus('error');
          }
        }, 10000); // 10 second timeout

        ws.onopen = () => {
          console.log(`Connected to WebSocket server for Organisation: ${org_id}, Session: ${sessionId}`);
          reconnectAttempts = 0; // Reset reconnect attempts on successful connection
          isConnecting = false;
          setWsConnectionStatus('connected');

          // Clear connection timeout
          if (connectionTimeout) {
            clearTimeout(connectionTimeout);
            connectionTimeout = null;
          }

          // Send initial connection message
          ws.send(JSON.stringify({
            type: 'connection',
            organizationId: org_id,
            // message:messages,
            userId: sessionId,
            timestamp: Date.now()
          }));

          // Start heartbeat
          heartbeatInterval = setInterval(() => {
            if (ws.readyState === WebSocket.OPEN) {
              console.log('Sending heartbeat...');
              ws.send(JSON.stringify({
                type: 'heartbeat',
                organizationId: org_id,
                userId: sessionId,
                timestamp: Date.now()
              }));
            }
          }, 30000); // Send heartbeat every 30 seconds
        };

        ws.onmessage = (event) => {
          console.log('=== WebSocket Message Received ===');
          console.log('Raw message:', event.data);
          try {
            const receivedMessage = JSON.parse(event.data);
            console.log('Parsed message:', receivedMessage);

            // Handle heartbeat response
            if (receivedMessage.type === 'heartbeat') {
              console.log('Heartbeat response received');
              return;
            }

            // Handle end chat message
            if (
              (receivedMessage.text && receivedMessage.text.trim().toLowerCase() === 'end chat') ||
              (receivedMessage.message && receivedMessage.message.trim().toLowerCase() === 'end chat')
            ) {
              console.log('Received end chat command from WebSocket. Closing connection and switching to old chatbot flow.');
              if (wsRef.current) {
                wsRef.current.close(1000, 'End chat received');
              }
              setIsAgentConnected(false);
              setWsConnectionStatus('disconnected');
              // Set the sessionId to the one received in the message
              if (receivedMessage.sessionId || receivedMessage.session_id) {
                setSessionId(receivedMessage.sessionId || receivedMessage.session_id);
              }
              // Optionally, clear chat messages if you want:
              setMessageState({
                messages: [
                  {
                    message: "How can I assist you?",
                    type: "apiMessage",
                  },
                ],
                history: [],
              });
              return;
            }

            // Skip adding the message to chat if it originated from the current user
            if (receivedMessage.sender === 'user') {
              console.log('Skipping echoed user message:', receivedMessage);
              return;
            }

            // Add message to chat
            setMessageState(prevState => {
              const isDuplicate = prevState.messages.some(
                (msg) => msg.timestamp === receivedMessage.timestamp &&
                  msg.message === receivedMessage.text &&
                  msg.type === "apiMessage"
              );

              if (!isDuplicate) {
                return {
                  ...prevState,
                  messages: [...prevState.messages, {
                    type: "apiMessage",
                    message: receivedMessage.text || receivedMessage.message,
                    timestamp: receivedMessage.timestamp,
                    time: new Date(receivedMessage.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                  }]
                };
              }
              return prevState;
            });
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        ws.onclose = (event) => {
          console.log(`WebSocket closed for Organisation: ${org_id}, Session: ${sessionId}`, event);
          console.log('Close code:', event.code, 'Reason:', event.reason);

          // Clear intervals and timeouts
          if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
            heartbeatInterval = null;
          }
          if (connectionTimeout) {
            clearTimeout(connectionTimeout);
            connectionTimeout = null;
          }

          isConnecting = false;

          // Attempt to reconnect if not closed cleanly and haven't exceeded max attempts
          if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
            console.log(`Attempting to reconnect (${reconnectAttempts + 1}/${maxReconnectAttempts})...`);
            reconnectAttempts++;
            setWsConnectionStatus('connecting');

            // Exponential backoff
            const backoffDelay = reconnectDelay * Math.pow(2, reconnectAttempts - 1);
            setTimeout(connectWebSocket, backoffDelay);
          } else {
            console.log('WebSocket connection closed permanently');
            setIsAgentConnected(false);
            setWsConnectionStatus('disconnected');
            toast.success('WebSocket connection closed permanently');
          }
        };

        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          isConnecting = false;
          setWsConnectionStatus('error');

          // Clear connection timeout on error
          if (connectionTimeout) {
            clearTimeout(connectionTimeout);
            connectionTimeout = null;
          }
        };

        // Store the WebSocket instance in the ref
        wsRef.current = ws;
      } catch (error) {
        console.error('Error creating WebSocket connection:', error);
        isConnecting = false;
        setWsConnectionStatus('error');
        setIsAgentConnected(false);
        toast.error('Failed to connect to agent portal. Please try again.');
      }
    };

    connectWebSocket();

    // Cleanup function
    return () => {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
      }
      if (connectionTimeout) {
        clearTimeout(connectionTimeout);
      }
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmounting');
      }
    };
  }, [isAgentConnected, org_id, sessionId]);

  // Manual reconnect function
  const handleManualReconnect = () => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    setIsAgentConnected(true); // This will trigger the useEffect to reconnect
    toast.success('Attempting to reconnect...');
  };

  // Function to check if WebSocket is ready to send messages
  const isWebSocketReady = () => {
    return wsRef.current &&
      wsRef.current.readyState === WebSocket.OPEN &&
      wsConnectionStatus === 'connected';
  };

  // Function to send message via WebSocket with retry logic
  const sendWebSocketMessage = (messagePayload: any) => {
    if (!isWebSocketReady()) {
      console.log('WebSocket not ready, cannot send message');
      return false;
    }

    try {
      wsRef.current!.send(JSON.stringify(messagePayload));
      console.log('Message sent via WebSocket:', messagePayload);
      return true;
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      return false;
    }
  };

  const handleAgentConnect = () => {
    setIsAgentConnected(true);
    setAutoSubmit(true);
    setQuery("connect_to_agent");
  };

  return (
    <div className="h-screen font-manrope">
      {/* Header Section */}
      {/* <Header
 
        isMobile={isMobile}
 
        toggleChatHistory={toggleChatHistory}
 
        setIsModalOpen={setIsModalOpen}
 
      /> */}

      {/* Logout Dialog */}
      <LogoutDialog isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} onLogout={logout} loading={loading} />

      {/* Main Content Section */}

      <main className="sm:px-6 w-full pt-5 lg:max-w-3xl mx-auto px-4 px-8">
        {/* Show Admin Button only for Admin and non-Mobile view */}

        {/* {isAdmin && !isMobile && (
 
          <AdminButton />
 
        )} */}

        {/* WebSocket Connection Status */}
        {isAgentConnected && (
          <div className="mb-4 p-3 rounded-lg border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${wsConnectionStatus === 'connected' ? 'bg-green-500' :
                      wsConnectionStatus === 'connecting' ? 'bg-yellow-500' :
                        wsConnectionStatus === 'error' ? 'bg-red-500' : 'bg-gray-500'
                    }`}
                ></div>
                <span className="text-sm font-medium">
                  Agent Portal: {
                    wsConnectionStatus === 'connected' ? 'Connected' :
                      wsConnectionStatus === 'connecting' ? 'Connecting...' :
                        wsConnectionStatus === 'error' ? 'Connection Error' : 'Disconnected'
                  }
                </span>
              </div>
              {wsConnectionStatus === 'error' && (
                <button
                  onClick={handleManualReconnect}
                  className="text-sm px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  Reconnect
                </button>
              )}
            </div>
          </div>
        )}

        {/* Topic Selection (only visible for non-mobile) */}
        {/* {!isMobile && (
          <TopicSelection
            options={options}
            selectedOption={selectedOption}
            handleChange={handleChange}
          />
        )} */}

        {/* Message List */}

        <MessageList
          messages={messages}
          messageListRef={messageListRef}
          background={background}
          fontcolor={fontcolor}
          isMobile={isMobile}
          logo={logo}
          onButtonClick={handleButtonClick} // Add the button click handler
          textfield={textcolor}
        />

        {/* Input Form Section */}
        <InputForm
          loading={loading}
          handleSubmit={handleSubmit}
          handleEnter={handleEnter}
          query={query}
          setQuery={setQuery}
          refreshContext={refreshContext}
          handleMicClick={handleMicClick}
          ismicro={ismicro}
          background={background}
          textfield={textcolor} // Pass background color
          onCallClick={() => setShowWebRTC(true)}
          showCallButton={!!webRtcValue} // Only show call button if webRtcValue is true
        />
          {showWebRTC && (
            <div
              className="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
              style={{ backdropFilter: 'blur(2px)' }}
              onKeyDown={e => { if (e.key === 'Escape') setShowWebRTC(false); }}
              tabIndex={-1}
            >
              <div
                className="relative bg-white rounded-2xl shadow-2xl p-4 w-full max-w-md max-h-[90vh] flex flex-col"
                style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.25)' }}
                onClick={e => e.stopPropagation()}
              >
                <button
                  onClick={() => setShowWebRTC(false)}
                  className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-2xl font-bold focus:outline-none"
                  aria-label="Close"
                  tabIndex={0}
                >
                  ×
                </button>
                <div className="overflow-y-auto flex-1">
                  <WebRTCInterface org_id={org_id || ""} />
                </div>
              </div>
            </div>
          )}
      </main>

      {/* Footer */}
      <p className="mt-4 text-center text-xs sm:text-sm text-gray-500">Note: Answers may not be exact.</p>
      <p className="mt-2 text-center text-xs sm:text-sm text-gray-500 font-light">
        Quicktalk -<span className="font-semibold text-gray-700"> Powered by Najoomi Technologies</span>
      </p>
      {/* <Footer /> */}
    </div>
  )
}

