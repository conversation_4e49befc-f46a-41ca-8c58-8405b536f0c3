import React, { useEffect, useRef, useState } from 'react';
import TableauEmbed from '../oldui/tableau';
import { FaSync } from 'react-icons/fa';
import userPool from '@/lib/userPoolConfig';
import axios from 'axios';


const AnalyticsDashboard = () => {
  const tableauRef = useRef();
  const user = userPool.getCurrentUser();
  const user_group = localStorage.getItem('user_group');
  const idToken = localStorage.getItem(`CognitoIdentityServiceProvider.24ira6svu3j9t2qbls9s4cgr6t.${user.username}.idToken`);
  const [token, setToken] = useState('');
  const data=localStorage.getItem('userEmail')
  const [timeoutId, setTimeoutId] = useState(null);
  let jwt;
  const handleRefresh = () => {
    if (tableauRef.current) {
      tableauRef.current.refreshViz();
    }
  };

  const fetchToken = async () => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_SSO_TOKEN}/generate-token`, {
        email: data,
      }, {
        headers: {
          Authorization: `Bearer ${idToken}`,
          'Content-Type': 'application/json',
        }
      });

      setToken(response.data.token);
      console.log(response.data.token)

      localStorage.setItem('lastTokenRefreshTime', Date.now().toString());
      scheduleTokenRefresh();
    } catch (error) {
      console.error('Error fetching token:', error);
    }
  };

  const scheduleTokenRefresh = () => {
    const lastTokenRefreshTime = localStorage.getItem('lastTokenRefreshTime');
    const timeSinceLastRefresh = Date.now() - (lastTokenRefreshTime ? parseInt(lastTokenRefreshTime) : 0);
    const timeUntilNextRefresh = Math.max(50 * 60 * 1000 - timeSinceLastRefresh, 0);

    const id = setTimeout(() => {
      fetchToken();
    }, timeUntilNextRefresh);

    setTimeoutId(id);
  };


  useEffect(() => {
    fetchToken();

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [user?.storage?.email]);

  const url = import.meta.env.VITE_PUBLIC_TABLEAU_SITE_URL;
  const options = {
    toolbar: 'top',
    width: '77vw',
    height: '700px',
  };

  return (
    <div className="dashboard">
      <div className='d-flex gap-3'>
        <button onClick={handleRefresh} className="refresh">
          <FaSync size={20} />
        </button>
      </div>

      <TableauEmbed 
        ref={tableauRef}
        url={url} 
        className="w-full h-full" 
        options={options} 
        token={token} 
      />
    </div>
  );
};

export default AnalyticsDashboard;
