import React, { useState, useEffect } from 'react';
import userPool from '../../lib/userPoolConfig';
import { TextField, Button, Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography } from '@mui/material';
import toast from 'react-hot-toast';
import { FaTrash } from 'react-icons/fa';
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from '../ui/alert-dialog';

const UserManagement = ({ org_id }) => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [type, setType] = useState('agent');
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [groupName, setGroupName] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);

  // Get group name from localStorage or userPool
  const getGroupName = async () => {
    try {
      // First try to get from localStorage
      const cognitoKey = import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY;
      const savedUsername = localStorage.getItem(cognitoKey || '');
      
      if (savedUsername) {
        // Get user attributes from userPool
        const user = userPool.getCurrentUser();
        if (user) {
          const attributes = await new Promise((resolve, reject) => {
            user.getUserAttributes((err, attributes) => {
              if (err) reject(err);
              else resolve(attributes || []);
            });
          });
          
          // Find the group name from custom attributes
          const groupAttribute = attributes.find(attr => attr.Name === 'custom:groupName');
          if (groupAttribute) {
            setGroupName(groupAttribute.Value);
            return groupAttribute.Value;
          }
        }
      }
      
      // Fallback: use org_id as group name
      setGroupName(org_id);
      return org_id;
    } catch (error) {
      console.error('Error getting group name:', error);
      // Fallback: use org_id as group name
      setGroupName(org_id);
      return org_id;
    }
  };

  // Fetch users list
  const fetchUsers = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get-users-list`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          group_name: groupName 
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      } else {
        toast.error('Failed to fetch users');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users');
    }
  };

  useEffect(() => {
    const initializeComponent = async () => {
      if (org_id) {
        await getGroupName();
      }
    };
    initializeComponent();
  }, [org_id]);

  useEffect(() => {
    if (org_id && groupName) {
      fetchUsers();
    }
  }, [org_id, groupName]);

  const handleAddAgent = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/add-user-to-group`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: username,
          group_name: groupName,
          email: email,
          type: type
        })
      });

      if (response.ok) {
        toast.success("Agent added successfully");
      setUsername('');
      setEmail('');
        setType('agent');
        // Refresh the users list
        fetchUsers();
      } else {
        // Get the error message from the response
        const errorData = await response.json().catch(() => ({ message: 'Unknown error occurred' }));
        const errorMessage = errorData.message || errorData.error || `HTTP ${response.status}: ${response.statusText}`;
        toast.error(errorMessage);
      }
    } catch (err) {
      console.error('Error adding agent:', err);
      toast.error(err.message || 'Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getAdminStatus = (customAttributes) => {
    if (!customAttributes) return 'Agent';
    return customAttributes['custom:Admin'] === 'true' ? 'Admin' : 'Agent';
  };

  const openDeleteDialog = (user) => {
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/delete-user`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: userToDelete.username,
          group_name: groupName
        })
      });
      if (response.ok) {
        toast.success('User deleted successfully');
        fetchUsers();
        closeDeleteDialog();
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error occurred' }));
        const errorMessage = errorData.message || errorData.error || `HTTP ${response.status}: ${response.statusText}`;
        toast.error(errorMessage);
      }
    } catch (err) {
      console.error('Error deleting user:', err);
      toast.error(err.message || 'Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1400, margin: '0 auto' }}>
      <Typography variant="h4" gutterBottom sx={{ color: '#1a3b2a', fontWeight: 'bold', mb: 4 }}>
        User Management
      </Typography>

      <Box sx={{ display: 'flex', gap: 3, flexDirection: { xs: 'column', lg: 'row' } }}>
        {/* Users List - Left Side */}
        <Box sx={{ flex: 2 }}>
          <Paper elevation={3} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ color: '#1a3b2a', mb: 3 }}>
              Users List
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: '#f5f5f5' }}>
                    <TableCell sx={{ fontWeight: 'bold' }}>Username</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Type</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Organization</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} align="center" sx={{ py: 4 }}>
                        <Typography variant="body1" color="textSecondary">
                          No users found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    users.map((user, index) => (
                      <TableRow key={index} sx={{ '&:nth-of-type(odd)': { bgcolor: '#fafafa' } }}>
                        <TableCell sx={{ fontWeight: 'medium' }}>{user.username}</TableCell>
                        <TableCell>{user.email || 'N/A'}</TableCell>
                        <TableCell>
                          <Box
                            sx={{
                              px: 2,
                              py: 0.5,
                              borderRadius: 1,
                              display: 'inline-block',
                              bgcolor: getAdminStatus(user.custom_attributes) === 'Admin' ? '#ffebee' : '#e8f5e8',
                              color: getAdminStatus(user.custom_attributes) === 'Admin' ? '#c62828' : '#2e7d32',
                              fontWeight: 'medium'
                            }}
                          >
                            {getAdminStatus(user.custom_attributes)}
                          </Box>
                        </TableCell>
                        <TableCell>{user.custom_attributes?.['custom:organizationId'] || 'N/A'}</TableCell>
                        <TableCell>
                          <AlertDialog open={deleteDialogOpen && userToDelete?.username === user.username} onOpenChange={setDeleteDialogOpen}>
                            <AlertDialogTrigger asChild>
                              <Button 
                                variant="outlined" 
                                color="error" 
                                size="small" 
                                onClick={() => openDeleteDialog(user)}
                                disabled={loading}
                              >
                                <FaTrash />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <div className="flex flex-col items-center justify-center py-4">
                                <AlertDialogDescription className="text-gray-700 text-center mb-4">
                                  Are you sure you want to delete this user?<br />
                                  This action cannot be undone.
                                </AlertDialogDescription>
                                <div className="flex gap-4 justify-center">
                                  <AlertDialogCancel className="rounded-md px-6 py-2">Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    className="bg-[#CD0ADD] text-white rounded-md px-6 py-2 hover:bg-[#a800b8] transition-colors duration-200"
                                    onClick={handleDeleteUser}
                                    asChild={false}
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </div>
                              </div>
                            </AlertDialogContent>
                          </AlertDialog>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Box>

        {/* Add User Form - Right Side */}
        <Box sx={{ flex: 1 }}>
          <Paper elevation={3} sx={{ p: 3, height: 'fit-content' }}>
            <Typography variant="h6" gutterBottom sx={{ color: '#1a3b2a', mb: 3 }}>
              Add New User
            </Typography>
            <Box component="form" onSubmit={handleAddAgent} sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <TextField
        label="Username"
        variant="outlined"
        value={username}
        onChange={(e) => setUsername(e.target.value)}
        required
                fullWidth
      />
      <TextField
        label="Email"
        variant="outlined"
                type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
                fullWidth
              />
              <TextField
                label="Type"
                variant="outlined"
                value={type}
                disabled
                fullWidth
                helperText="Default type is Agent"
      />
              <Button 
                type="submit" 
                variant="contained" 
                sx={{ 
                  bgcolor: '#CD0ADD', 
                  '&:hover': { bgcolor: '#a00a8a' },
                  mt: 2
                }}
                disabled={loading}
              >
                {loading ? 'Adding...' : 'Add Agent'}
      </Button>
            </Box>
          </Paper>
        </Box>
      </Box>
    </Box>
  );
};

export default UserManagement; 