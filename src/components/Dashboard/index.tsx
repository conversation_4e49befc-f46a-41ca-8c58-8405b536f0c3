import { useEffect, useState } from "react"
import { useNavigate } from "react-router-dom"
import TransactionsTable from "./DoumentTable"
import SettingPage from "./setting/SettingsPage"
import toast from "react-hot-toast"
import { LuWorkflow, LuLogOut } from "react-icons/lu"
import { RiTaskLine } from "react-icons/ri"
import { BsRobot, BsClockHistory, BsDatabase, BsGraphUp, BsPersonCircle, BsChatDots } from "react-icons/bs"
import { IoSettingsOutline } from "react-icons/io5"
import { PiBooks } from "react-icons/pi"
import { SiReadthedocs } from "react-icons/si"
import ChatHistory from "../../pages/history"
import WordVocabulary from "../oldui/WordVocabulary"
import Stepper from "../oldui/Stepper"
import LogoutDialog from "../oldui/LogoutDialog"
import Squareload from "../oldui/Squareload"
import LeadGeneration from "../oldui/Leadgenration"
import type { CognitoUserSession } from "amazon-cognito-identity-js"
import userPool from "../../lib/userPoolConfig"
import axios from "axios"
import ChatbotHome from "./Chatbot"
import Integration from "../oldui/Integration"
import AnalyticsDashboard from './AnalyticsDashboard'
import UserManagement from './UserManagement'
import ChatList from './ChatList'
import WebRTC from "../oldui/WebRTC";

// Define types
type SelectedOption =
  | "quicktalk"
  | "chathistory"
  | "data"
  | "integration"
  | "vocabulary"
  | "settings"
  | "logout"
  | "lead"
  | "analytics"
  | "userManagement"
  | "chatList"
  | "webrtc"
  | null

interface MenuItem {
  id: string
  icon: React.ReactNode
  label: string
}

const MainComponent = () => {
  const navigate = useNavigate()
  const [refresh, setRefresh] = useState(false)
  const [organ_id, setOrgan_id] = useState<string | null>(null)
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null)
  const [isLogoutDialogOpen, setLogoutDialogOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [selectedOption, setSelectedOption] = useState<SelectedOption>("quicktalk")
  const [isFirstSign, setIsFirstSign] = useState<boolean | null>(null)
  const [redirectingToDashboard, setRedirectingToDashboard] = useState(false)
  const [showStepper, setShowStepper] = useState(false)
  const [isAgentModuleActive, setIsAgentModuleActive] = useState(false)

  // Save custom attributes to backend
  const saveCustomAttributes = async (username: string, organizationId: string, admin: string) => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/save_custom_attributes`, {
        username,
        admin,
        organizationId,
      })
      console.log("Data saved:", response.data)
    } catch (error) {
      console.error("Error saving custom attributes:", error)
    }
  }

  // Fetch both admin status and user flag
  useEffect(() => {
    const fetchUserStatus = async () => {
      setLoading(true)
      try {
        const user = userPool.getCurrentUser()
        if (!user) {
          navigate("/")
          return
        }
        const session = await new Promise<CognitoUserSession | null>((resolve, reject) => {
          user.getSession((err: Error | null, session: CognitoUserSession | null) => {
            if (err) {
              reject(err)
            } else {
              resolve(session)
            }
          })
        })
        if (!session?.isValid()) {
          navigate("/")
          return
        }
        const attributes = await new Promise<any[]>((resolve, reject) => {
          user.getUserAttributes((err: unknown, attributes) => {
            if (err) {
              reject(err)
            } else {
              resolve(attributes || [])
            }
          })
        })
        const customAttributes = attributes.filter((attr) => attr.Name.startsWith("custom:"))
        const organizationId = customAttributes.find((attr) => attr.Name === "custom:organizationId")?.Value
        const admin = customAttributes.find((attr) => attr.Name === "custom:Admin")?.Value || "true"
        if (organizationId && admin) {
          setOrgan_id(organizationId)
          const username = localStorage.getItem(
            import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY as string
          )
          if (username) {
            // Save custom attributes
            await saveCustomAttributes(username, organizationId, admin)
            // Fetch both admin status and user flag
            const [adminRes, flagRes] = await Promise.all([
              axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/check_admin`, { username }),
              axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get_user_flag`, { username })
            ])
            setIsAdmin(Boolean(adminRes.data.admin))
            setIsFirstSign(Boolean(flagRes.data.flag))
          } else {
            toast.error("Username not found. Please login again.")
            setLoading(false)
          }
        } else {
          toast.error("Required user information is missing. Please login again.")
          setLoading(false)
        }
      } catch (error) {
        toast.error("An error occurred. Please try again.")
        setLoading(false)
      } finally {
        setLoading(false)
      }
    }
    fetchUserStatus()
  }, [navigate])

  // Handle logout
  const logout = async () => {
    setLoading(true)
    const user = userPool.getCurrentUser()

    if (user) {
      console.log("Signing out user:", user.getUsername())
      localStorage.removeItem("username")
      localStorage.removeItem("email")
      user.signOut()
      setLoading(false)
      navigate("/")
    } else {
      console.log("No user to sign out")
      setLoading(false)
    }
  }

  // Trigger logout when logout option is selected
  useEffect(() => {
    if (selectedOption === "logout") {
      logout()
    }
  }, [selectedOption])

  const fetchAgentModuleStatus = async () => {
    if (!organ_id) return;
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/agnet_module/subscription-status`, {
        organization_id: organ_id
      });
      setIsAgentModuleActive(Boolean(response.data.agent_module_subscription));
    } catch (err) {
      setIsAgentModuleActive(false);
    }
  };

  useEffect(() => {
    fetchAgentModuleStatus();
  }, [organ_id]);

  // Menu items for sidebar
  const menuItems: MenuItem[] = [
    { id: "quicktalk", icon: <BsRobot className="w-5 h-5 text-[#CD0ADD]" />, label: "QuickTalk" },
    { id: "chathistory", icon: <BsClockHistory className="w-5 h-5 text-[#CD0ADD]" />, label: "Chat History" },
    { id: "data", icon: <BsDatabase className="w-5 h-5 text-[#CD0ADD]" />, label: "Data" },
    { id: "integration", icon: <LuWorkflow className="w-5 h-5 text-[#CD0ADD]" />, label: "Integration" },
    { id: "vocabulary", icon: <PiBooks className="w-5 h-5 text-[#CD0ADD]" />, label: "Vocabulary" },
    { id: "analytics", icon: <BsGraphUp className="w-5 h-5 text-[#CD0ADD]" />, label: "Analytics" },
    { id: "settings", icon: <IoSettingsOutline className="w-5 h-5 text-[#CD0ADD]" />, label: "Settings" },
    { id: "lead", icon: <img src="/leadgen.png" alt="Lead Gen Icon" className="w-5 h-5" />, label: "Bot Function" },
    // { id: "webrtc", icon: <BsChatDots className="w-5 h-5 text-[#CD0ADD]" />, label: "WebRTC" },
    // { id: "Doucmentation", icon: <SiReadthedocs className="w-5 h-5 text-[#CD0ADD]" />, label: "Doucmentation" },
  ]

  // Handle stepper completion
  const handleStepperCompletion = () => {
    setRedirectingToDashboard(true)
    toast.success("You have successfully completed all steps!")
    setTimeout(async () => {
      setIsFirstSign(true)
      setShowStepper(false)
      setRedirectingToDashboard(false)
      const username = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY as string)
      if (username) {
        await checkAdminStatus(username)
      } else {
        toast.error("Could not verify admin status. Please refresh the page.")
      }
    }, 2000)
  }

  // Function to update
  const updateAgentModuleStatus = (status) => {
    setIsAgentModuleActive(status);
  };

  // Remove checkFirstSignFlag and checkAdminStatus from above, keep only for stepper completion
  const checkAdminStatus = async (username: string) => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/check_admin`, { username })
      setIsAdmin(Boolean(response.data.admin))
    } catch (err) {
      setIsAdmin(false)
    }
  }

  // Render content based on selected option and user state
  const renderContent = () => {
    // Wait for both admin and firstSign to be determined
    if (loading || isAdmin === null || isFirstSign === null) {
      return (
        <div className="flex justify-center items-center h-full">
          <div className="flex items-center space-x-2">
            <span className="text-black">Please wait while we check your status...</span>
            <Squareload />
          </div>
        </div>
      )
    }
    // If not admin, always show ChatList
    if (!isAdmin) {
      return <ChatList org_id={organ_id} />
    }
    // If admin and firstSign is false, show stepper
    if (isAdmin && !isFirstSign) {
      return (
        <div className="h-full">
          {redirectingToDashboard ? (
            <div className="fixed inset-0 flex items-center justify-center">
              <div className="flex flex-col items-center justify-center bg-white text-center p-6">
                <div className="relative flex items-center justify-center w-24 h-24">
                  <div className="absolute w-[140px] h-[140px] rounded-full border-[6px] border-[#CD0ADD] opacity-90 blur-[1px]"></div>
                  <div className="absolute w-[110px] h-[110px] rounded-full border-8 border-[#CD0ADD]"></div>
                  <span className="text-[#CD0ADD] text-7xl font-bold z-20">✔</span>
                  <div className="absolute w-[160px] h-[160px] rounded-full border-[3px] border-[#CD0ADD] opacity-90 blur-[2px]"></div>
                </div>
                <h1 className="text-2xl font-bold text-[#CD0ADD] pt-12">CONGRATULATIONS!</h1>
                <p className="text-gray-700 pt-4">Your QuickTalk is now ready to use</p>
              </div>
            </div>
          ) : (
            <Stepper onComplete={handleStepperCompletion} />
          )}
        </div>
      )
    }
    // If admin and firstSign is true, show dashboard
   if (isAdmin && isFirstSign) {
      switch (selectedOption) {
        case "quicktalk":
          return <ChatbotHome />
        case "chathistory":
          return organ_id ? <ChatHistory organization_id={organ_id} /> : null
        case "data":
          return organ_id ? <TransactionsTable org_id={organ_id} /> : null
        case "integration":
          return organ_id ? <Integration organ_id={organ_id} /> : null
        case "settings":
          return organ_id ? <SettingPage org_id={organ_id} /> : null
        case "userManagement":
          return organ_id ? <UserManagement org_id={organ_id} /> : null
        case "chatList":
          return organ_id ? <ChatList org_id={organ_id} /> : null
        case "vocabulary":
          return organ_id ? <WordVocabulary org_id={organ_id} /> : null
        case "lead":
          return organ_id ? <LeadGeneration orgid={organ_id} onAgentModuleStatusChange={fetchAgentModuleStatus} isAgentModuleActive={isAgentModuleActive} /> : null
        case "analytics":
          return <AnalyticsDashboard />
        case "webrtc":
           return organ_id ? <WebRTC org_id={organ_id} /> : null
        default:
          return null
      }
    }

    return null
  }

  return (
    <div className="font-manrope flex h-screen">
      {/* Sidebar - only show when user has completed first-time setup and is admin */}
      {isAdmin && isFirstSign && !showStepper && (
        <div className="fixed left-0 top-0 w-64 h-[110%] bg-gradient-to-b from-[#1a3b2a] via-[#0f2a24] to-[#0b1f1b] text-white flex flex-col py-6 border-r border-[#134e2b]">
          {/* Logo and Title */}
          <div className="flex items-center justify-center mb-8">
            <img 
              src="/QuickTalkLogo.png" 
              alt="QuickTalk" 
              className="w-full max-w-[200px] h-auto"
            />
          </div>

          {/* Navigation Menu */}
          <nav className="flex-1">
            <div className="space-y-2">
              {menuItems.map((item) => {
                if (item.id === "userManagement" && !isAgentModuleActive) return null;
                return (
                  <button
                    key={item.id}
                    onClick={() => setSelectedOption(item.id as SelectedOption)}
                    className={`w-full p-3 flex items-center transition-colors duration-200 
                      ${selectedOption === item.id ? "bg-gradient-to-r from-[#134e2b] to-[#5c3d69]" : "hover:bg-gradient-to-r from-[#134e2b] to-[#5c3d69]"}`}
                  >
                    <span className="inline-flex items-center justify-center mr-3">{item.icon}</span>
                    <span className="text-sm font-medium">{item.label}</span>
                  </button>
                );
              })}
              {/* Conditionally render User Management menu item */}
              {isAgentModuleActive && (
                <button
                  key="userManagement"
                  onClick={() => setSelectedOption("userManagement")}
                  className={`w-full p-3 flex items-center transition-colors duration-200 
                    ${selectedOption === "userManagement" ? "bg-gradient-to-r from-[#134e2b] to-[#5c3d69]" : "hover:bg-gradient-to-r from-[#134e2b] to-[#5c3d69]"}`}
                  >
                    <span className="inline-flex items-center justify-center mr-3"><BsPersonCircle className="w-5 h-5 text-[#CD0ADD]" /></span>
                    <span className="text-sm font-medium">User Management</span>
                  </button>
                )}
            </div>
          </nav>

          {/* Logout Button */}
          <div className="pb-16">
            <button
              onClick={() => setLogoutDialogOpen(true)}
              className="w-full p-3 flex items-center transition-colors duration-200 hover:bg-gradient-to-r from-[#134e2b] to-[#5c3d69]"
            >
              <span className="inline-flex items-center justify-center mr-3">
                <LuLogOut className="w-5 h-5 text-[#CD0ADD]" />
              </span>
              <span className="text-sm font-medium">Log Out</span>
            </button>
          </div>
        </div>
      )}
      {/* Main Content - full width during stepper */}
      <div
        id="dashboard-main-content"
        className={
          isAdmin && isFirstSign && !showStepper ? "ml-64 flex-1" : "w-full"
        }
      >
        <div className={isAdmin ? "h-full p-12" : 'h-full'}>{
          // Only allow navigation to User Management if agent module is active
          selectedOption === "userManagement" && !isAgentModuleActive
            ? null
            : renderContent()
        }</div>
      </div>

      {/* Logout Dialog */}
      <LogoutDialog
        isOpen={isLogoutDialogOpen}
        onClose={() => setLogoutDialogOpen(false)}
        onLogout={() => {
          setSelectedOption("logout")
          logout()
        }}
        loading={false}
      />
    </div>
  )
}

export default MainComponent