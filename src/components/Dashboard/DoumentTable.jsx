"use client"

import { useState, useEffect, useCallback } from "react"
import axios from "axios"
import { useNavigate } from "react-router-dom"
import FunctionSetting from '../Dashboard/function/FunctionSetting'
import Squareload from "../oldui/Squareload"
import toast from "react-hot-toast"
import IngestButton from "./IngestButton"
import FunctionTools from "../Dashboard/function/FunctionTools"
import { useLoader } from "../../context/LoaderContext"
import {
  Dialog,
  DialogHeader,
  DialogBody,
  DialogFooter,
  Button,
  Tabs,
  TabsHeader,
  TabsBody,
  Tab,
  TabPanel,
  Input,
  Menu,
  MenuHandler,
  MenuList,
  MenuItem,
} from "@material-tailwind/react"
import { MoreVertical, BookOpen, Pencil, Trash } from "lucide-react"

const LinkManagement = ({ org_id }) => {
  const [sources, setSources] = useState([])
  const [texts, setTexts] = useState([])
  const [indexes, setIndexes] = useState([])
  const [selectedSource, setSelectedSource] = useState(null)
  const [selectednamespace, setSelectedNamespace] = useState(null)
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedText, setSelectedText] = useState("")
  const [modalAction, setModalAction] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [loadingDelete, setLoadingDelete] = useState(false)
  const [textModalOpen, setTextModalOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState("")
  const [username, setUsername] = useState(null)
  const [namespace, setNamespace] = useState(null)
  const { setLoading, setMessage } = useLoader()
  const [selectedFile, setSelectedFile] = useState(null)
  const [activeTab, setActiveTab] = useState("document")
  const [websiteUrl, setWebsiteUrl] = useState("")
  const [apiUrl, setApiUrl] = useState("")
  const [websiteLinks, setWebsiteLinks] = useState([])
  const [websiteLoading, setWebsiteLoading] = useState(false)
  const [addingWebsite, setAddingWebsite] = useState(false)
  const [updateWebsiteModalOpen, setUpdateWebsiteModalOpen] = useState(false)
  const [selectedWeblink, setSelectedWeblink] = useState(null)
  const [updatedWebsiteUrl, setUpdatedWebsiteUrl] = useState("")
  const [deletingWebsite, setDeletingWebsite] = useState(false)
  const [updatingWebsite, setUpdatingWebsite] = useState(false)

  const navigate = useNavigate()
  console.log("useloader", useLoader)
  const fetchInitialSources = useCallback(async () => {
    setIsLoading(true)

    const fetchedUsername = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY)

    setUsername(fetchedUsername)

    if (!fetchedUsername) {
      console.error("Username is missing.")
      toast.error("Username is missing. Please login again.")
      setIsLoading(false)
      return
    }

    try {
      const { data } = await axios.post(`${import.meta.env.VITE_PUBLIC_BACKEND_API}/api/fetch-source/fetch`, {
        username: fetchedUsername,
      })
      setSources([...new Set(data.sources)])
      setTexts(data.texts)
      setIndexes([...new Set(data.indexNames)])
      setNamespace([...new Set(data.pineconenamespace)])
    } catch (error) {
      console.error("Error fetching sources:", error)
      toast.error("Data Not found No Document Ingested")
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Fetch website links when the active tab changes to "website"
  useEffect(() => {
    if (activeTab === "website") {
      fetchWebsiteLinks()
    }
  }, [activeTab])

  const fetchWebsiteLinks = async () => {
    setWebsiteLoading(true);
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/website_links/get`, {
        organisation_id: org_id,
      });
      
      if (response.data && response.data.response) {
        // Make sure we're handling the response consistently
        let links = response.data.response;
        
        // Handle nested array if needed
        if (Array.isArray(links) && links.length > 0 && Array.isArray(links[0])) {
          links = links.flat();
        }
        
        // Map to ensure consistent format
        const formattedLinks = links.map(link => {
          if (typeof link === 'string') {
            return { web_link: link, url: link, id: null };
          }
          return {
            web_link: link.web_link || '',
            url: link.url || link.web_link || '',
            weblink_id: link.weblink_id || link.id || null
          };
        });
        
        setWebsiteLinks(formattedLinks);
      } else {
        setWebsiteLinks([]);
      }
    } catch (error) {
      console.error("Error fetching website links:", error);
      toast.error("Failed to fetch website links");
      setWebsiteLinks([]);
    } finally {
      setWebsiteLoading(false);
    }
  };

  const addWebsiteLink = async () => {
    if (!websiteUrl.trim()) {
      toast.error("Please enter a valid website URL")
      return
    }

    setAddingWebsite(true)
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/website_links/save`, {
        organisation_id: org_id,
        website_link: websiteUrl.trim(),
      })

      if (response.status === 200 || response.status === 201) {
        toast.success("Website link added successfully")
        setWebsiteUrl("")
        fetchWebsiteLinks() // Refresh the list
      } else {
        toast.error("Failed to add website link")
      }
    } catch (error) {
      console.error("Error adding website link:", error)
      toast.error("Failed to add website link: " + (error.response?.data?.message || error.message))
    } finally {
      setAddingWebsite(false)
    }
  }

  useEffect(() => {
    fetchInitialSources()
  }, [fetchInitialSources])

  const handleDelete = (source, text, index, namespace) => {
    setSelectedSource(source)
    setSelectedText(text)
    setSelectedIndex(index)
    setModalAction("delete")
    setSelectedNamespace(namespace)
    setModalOpen(true)
  }

  const handleUpdate = (source, text, index, namespace) => {
    setSelectedSource(source)
    setSelectedText(text)
    setSelectedIndex(index)
    setModalAction("update")
    setSelectedNamespace(namespace)
    setModalOpen(true)
  }

  const showText = (index) => {
    setSelectedText(texts[index])
    setTextModalOpen(true)
  }

  const confirmAction = async () => {
    setLoadingDelete(true)
    setLoading(true)
    setMessage(modalAction === "delete" ? "Deletion in progress..." : "Update in progress...")

    try {
      if (!username) return

      if (modalAction === "delete") {
        setModalOpen(false)
        await axios.delete(`${import.meta.env.VITE_PUBLIC_BACKEND_API}/api/delete`, {
          data: { source: selectedSource, username, pinecone_namespace: selectednamespace },
        })
        toast.loading("Deleting document...", { duration: 3000 })
        await new Promise((resolve) => setTimeout(resolve, 12000))
        setSources((prevSources) => prevSources.filter((source) => source !== selectedSource))
        toast.success("Deletion successful!")
      } else if (modalAction === "update" && selectedFile) {
        setMessage("Update is in progress...")
        setModalOpen(false)
        const formData = new FormData()
        formData.append("file", selectedFile)
        formData.append("source", selectedSource)
        formData.append("username", username)
        formData.append("namespace", selectednamespace)

        const response = await axios.put(`${import.meta.env.VITE_PUBLIC_BACKEND_API}/api/update`, formData, {
          headers: { "Content-Type": "multipart/form-data" },
        })
        toast.loading("Updating document...", { duration: 3000 })
        await new Promise((resolve) => setTimeout(resolve, 12000))
        await fetchInitialSources()
        toast.success("Update successful!")
      }
    } catch (error) {
      console.error("Error:", error)
      toast.error("An error occurred. Please try again.")
    } finally {
      setLoadingDelete(false)
      setModalOpen(false)
      setLoading(false)
      setSelectedFile(null)
    }
  }

  const getWeblinkId = async (webLink) => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get/weblink_id`, {
        web_link: webLink
      });
      console.log("id", response.data.weblink_id);
      return response.data.weblink_id;
    } catch (error) {
      console.error("Error getting weblink ID:", error);
      throw error;
    }
  };

  const handleDeleteWebsite = async (link) => {
    const webLink = link.web_link || link.url || (typeof link === 'string' ? link : '');
    
    setDeletingWebsite(true);
    try {
      const weblink_id = await getWeblinkId(webLink);
      
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/delete_weblink`, {
        weblink_id: weblink_id,
        web_link: webLink,
      });
      
      if (response.status === 200) {
        toast.success("Website link deleted successfully");
        fetchWebsiteLinks(); // Refresh the list
      } else {
        toast.error("Failed to delete website link");
      }
    } catch (error) {
      console.error("Error deleting website link:", error);
      toast.error("Failed to delete website link: " + (error.response?.data?.message || error.message));
    } finally {
      setDeletingWebsite(false);
    }
  };

  const openUpdateWebsiteModal = (link) => {
    setSelectedWeblink(link);
    setUpdatedWebsiteUrl(link.web_link || link.url || (typeof link === 'string' ? link : ''));
    setUpdateWebsiteModalOpen(true);
  }

  const handleUpdateWebsite = async () => {
    if (!updatedWebsiteUrl.trim() || !selectedWeblink) {
      toast.error("Please enter a valid website URL");
      return;
    }

    const webLink = selectedWeblink.web_link || selectedWeblink.url || (typeof selectedWeblink === 'string' ? selectedWeblink : '');
    
    setUpdatingWebsite(true);
    try {
      const weblink_id = await getWeblinkId(webLink);
      
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/update_weblink`, {
        organisation_id: org_id,
        weblink_id: weblink_id,
        web_link: updatedWebsiteUrl.trim(),
      });

      if (response.status === 200) {
        toast.success("Website link updated successfully");
        setUpdateWebsiteModalOpen(false);
        fetchWebsiteLinks(); // Refresh the list
      } else {
        toast.error("Failed to update website link");
      }
    } catch (error) {
      console.error("Error updating website link:", error);
      toast.error("Failed to update website link: " + (error.response?.data?.message || error.message));
    } finally {
      setUpdatingWebsite(false);
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center">
        <Squareload loading={isLoading} size={50} />
      </div>
    )
  }

  if (!username) {
    return (
      <div className="flex flex-col justify-center items-cent overflow-hidden  bg-gray-50 p-8 text-center">
        <p className="text-xl text-gray-700 mb-6">You are not allowed to visit Guftugu without authentication...</p>
        <Button onClick={() => navigate("/")} className="text-lg px-6 py-3">
          Go to Login Page Please!
        </Button>
      </div>
    )
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 font-manrope">
      <div className="mb-6">
        <p className="text-sm text-gray-600 mb-4">
          Link your website, APIs or upload relevant documents to help your bot provide more accurate and
          business-specific responses
        </p>
      </div>

      <Tabs value={activeTab} className="font-manrope">
        <TabsHeader
          className="rounded-none border-b border-blue-gray-200 bg-transparent p-0 "
          indicatorProps={{
            className: "bg-transparent border-b-2 border-[#CD0ADD] shadow-none rounded-none ",
          }}
        >
          {["document", "website", "API(Tasks)"].map((tab) => (
            <Tab
              key={tab}
              value={tab}
              onClick={() => setActiveTab(tab)}
              className={`font-extrabold font-manrope py-3 px-6 ${activeTab === tab ? "text-[#CD0ADD]" : "text-black"}`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Tab>
          ))}
        </TabsHeader>

        <div className="p-4 font-manrope">
          <TabsBody>
            <TabPanel value="document" className="p-0">
              <div className="flex items-center gap-4 mb-6">
                <IngestButton onIngestComplete={fetchInitialSources} />
              </div>

              <table className="w-full table-auto bg-white border border-gray-200 rounded-lg font-manrope">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="px-6 py-3 border-b-2 border-gray-300"></th>
                    <th className="px-6 py-3 border-b-2 border-gray-300 w-16"></th>
                  </tr>
                </thead>
                <tbody>
                  {sources.map((source, index) => (
                    <tr key={index} className="even:bg-gray-50 odd:bg-white font-manrope">
                      <td className="px-6 py-4 border-b border-gray-200">{source}</td>
                      <td className="px-6 py-4 border-b border-gray-200">
                        <Menu placement="bottom-end">
                          <MenuHandler>
                            <Button variant="text" className="p-2">
                              <MoreVertical className="h-5 w-5 text-gray-500" />
                            </Button>
                          </MenuHandler>
                          <MenuList>
                            <MenuItem
                              className="flex items-center gap-2 text-[#CD0ADD] font-manrope"
                              onClick={() => handleUpdate(source, texts[index], indexes[index], namespace[index])}
                            >
                              <Pencil className="h-4 w-4" />
                              Update
                            </MenuItem>

                            <MenuItem
                              className="flex items-center gap-2 text-[#CD0ADD] font-manrope"
                              onClick={() => showText(index)}
                              variant="text"
                            >
                              <BookOpen className="h-4 w-4" />
                              Show Text
                            </MenuItem>

                            <MenuItem
                              className="flex items-center gap-2 text-[#CD0ADD] font-manrope"
                              onClick={() => handleDelete(source, texts[index], indexes[index], namespace[index])}
                            >
                              <Trash className="h-4 w-4" />
                              Delete
                            </MenuItem>
                          </MenuList>
                        </Menu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </TabPanel>

            <TabPanel value="website">
              <div className="flex items-center gap-4 mb-6">
                <Input
                  type="text"
                  label="Enter website URL"
                  value={websiteUrl}
                  onChange={(e) => setWebsiteUrl(e.target.value)}
                  className="flex-1"
                />
                <Button
                  className="bg-[#CD0ADD] normal-case font-manrope"
                  onClick={addWebsiteLink}
                  disabled={addingWebsite || !websiteUrl.trim()}
                >
                  {addingWebsite ? "Adding..." : "Add"}
                </Button>
              </div>

              {websiteLoading ? (
                <div className="flex justify-center items-center py-8">
                  <Squareload loading={websiteLoading} size={30} />
                </div>
              ) : websiteLinks.length > 0 ? (
                <table className="w-full table-auto bg-white border border-gray-200 rounded-lg font-manrope">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="px-6 py-3 border-b-2 border-gray-300 text-left">Website URL</th>
                      <th className="px-6 py-3 border-b-2 border-gray-300 w-16"></th>
                    </tr>
                  </thead>
                  <tbody>
                    {websiteLinks.map((link, index) => (
                      <tr key={index} className="even:bg-gray-50 odd:bg-white font-manrope">
                        <td className="px-6 py-4 border-b border-gray-200">
                          {link.url || link.web_link || (typeof link === 'string' ? link : '')}
                        </td>
                        <td className="px-6 py-4 border-b border-gray-200">
                          <Menu placement="bottom-end">
                            <MenuHandler>
                              <Button variant="text" className="p-2">
                                <MoreVertical className="h-5 w-5 text-gray-500" />
                              </Button>
                            </MenuHandler>
                            <MenuList>
                              <MenuItem
                                className="flex items-center gap-2 text-[#CD0ADD] font-manrope"
                                onClick={() => openUpdateWebsiteModal(link)}
                              >
                                <Pencil className="h-4 w-4" />
                                Update
                              </MenuItem>
                              <MenuItem
                                className="flex items-center gap-2 text-[#CD0ADD] font-manrope"
                                onClick={() => handleDeleteWebsite(link)}
                              >
                                <Trash className="h-4 w-4" />
                                Delete
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <p className="text-gray-600">No website links available.</p>
                </div>
              )}
            </TabPanel>

            <TabPanel value="API(Tasks)">
              <div className="w-full">
                <FunctionSetting org_id={org_id} />
              </div>
            </TabPanel>
          </TabsBody>
        </div>
      </Tabs>

      <Dialog open={modalOpen} handler={() => setModalOpen(false)}>
        <DialogHeader>{modalAction === "delete" ? "Confirm Deletion" : "Update File"}</DialogHeader>
        <DialogBody>
          {modalAction === "delete" ? (
            <p className="text-gray-600 font-manrope">
              Are you sure you want to delete <span className="font-semibold text-red-600">{selectedSource}</span>?
            </p>
          ) : (
            <>
              <p className="text-gray-600">
                Upload a new file for <span className="font-semibold text-blue-600">{selectedSource}</span>
              </p>
              <Input
                type="file"
                onChange={(e) => setSelectedFile(e.target.files[0])}
                className="mt-4"
                accept=".csv, .txt"
              />
            </>
          )}
        </DialogBody>
        <DialogFooter>
          <Button
            variant="text"
            color="red"
            onClick={() => setModalOpen(false)}
            className="mr-1 border-2 border-[#CD0ADD] text-[#CD0ADD]"
          >
            Cancel
          </Button>
          <Button
            className="bg-[#CD0ADD] text-white"
            onClick={confirmAction}
            disabled={loadingDelete || (modalAction === "update" && !selectedFile)}
          >
            {loadingDelete ? "Loading..." : modalAction === "delete" ? "Delete" : "Update"}
          </Button>
        </DialogFooter>
      </Dialog>

      {/* Text Content Modal */}
      <Dialog open={textModalOpen} handler={() => setTextModalOpen(false)}>
        <DialogHeader>Text Content</DialogHeader>
        <DialogBody className="max-h-[400px] overflow-y-auto">{selectedText}</DialogBody>
        <DialogFooter>
          <Button variant="text" color="red" onClick={() => setTextModalOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </Dialog>

      {/* Website Update Modal */}
      <Dialog open={updateWebsiteModalOpen} handler={() => setUpdateWebsiteModalOpen(false)}>
        <DialogHeader>Update Website Link</DialogHeader>
        <DialogBody>
          <p className="text-gray-600 mb-4">Update the website URL:</p>
          <Input
            type="text"
            label="Website URL"
            value={updatedWebsiteUrl}
            onChange={(e) => setUpdatedWebsiteUrl(e.target.value)}
            className="w-full"
          />
        </DialogBody>
        <DialogFooter>
          <Button
            variant="text"
            color="red"
            onClick={() => setUpdateWebsiteModalOpen(false)}
            className="mr-1 border-2 border-[#CD0ADD] text-[#CD0ADD]"
          >
            Cancel
          </Button>
          <Button
            className="bg-[#CD0ADD] text-white"
            onClick={handleUpdateWebsite}
            disabled={updatingWebsite || !updatedWebsiteUrl.trim()}
          >
            {updatingWebsite ? "Updating..." : "Update"}
          </Button>
        </DialogFooter>
      </Dialog>
    </div>
  )
}

export default LinkManagement

