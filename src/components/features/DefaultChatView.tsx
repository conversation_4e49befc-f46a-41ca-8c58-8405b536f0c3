
import { <PERSON>, CardContent } from "@/components/ui/card";
import { TrendingUp, Zap } from "lucide-react";

const DefaultChatView = () => {
  return (
    <div className="space-y-6">
      <div className="bg-white/10 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-2">
          <div className="w-6 h-6 bg-purple-400 rounded-full"></div>
          <span className="text-purple-200 text-sm font-medium">AI Assistant</span>
        </div>
        <div className="bg-purple-500 text-white p-3 rounded-lg mb-3 font-medium">
          Hello! I'm your AI assistant. How can I help you today?
        </div>
        <div className="bg-gray-700 text-gray-200 p-3 rounded-lg font-medium">
          I have a question about my order
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <TrendingUp className="w-8 h-8 text-green-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">99.2%</div>
            <div className="text-gray-300 text-sm font-medium">Resolution Rate</div>
          </CardContent>
        </Card>
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <Zap className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">1.2s</div>
            <div className="text-gray-300 text-sm font-medium">Avg Response</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DefaultChatView;
