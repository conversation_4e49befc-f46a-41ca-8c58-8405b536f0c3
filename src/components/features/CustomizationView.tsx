import { <PERSON>, CardContent } from "@/components/ui/card";
import { Palette, Upload, MessageCircle, Zap, Mic } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const CustomizationView = () => {
  return (
    <div className="space-y-6">
      {/* Logo Upload Section */}
      <div className="bg-white/10 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Upload className="w-5 h-5 text-purple-400" />
          <span className="text-purple-200 text-sm font-medium">Company Logo</span>
        </div>
        <div className="space-y-3">
          <div className="bg-gray-700/50 p-3 rounded border-2 border-dashed border-gray-600">
            <div className="text-center">
              <Upload className="w-6 h-6 text-gray-400 mx-auto mb-2" />
              <Button size="sm" className="bg-purple-500 hover:bg-purple-600 text-white">
                Choose file
              </Button>
              <p className="text-xs text-gray-400 mt-1">No file chosen</p>
            </div>
          </div>
        </div>
      </div>

      {/* Color Customization */}
      <div className="bg-white/10 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Palette className="w-5 h-5 text-pink-400" />
          <span className="text-pink-200 text-sm font-medium">Brand Colors</span>
        </div>
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white rounded-lg border-2 border-gray-300"></div>
            <span className="text-gray-200 text-sm">Background Color</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-600 rounded-lg border-2 border-white"></div>
            <span className="text-gray-200 text-sm">Font Color</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-purple-500 rounded-lg border-2 border-white"></div>
            <span className="text-gray-200 text-sm">AI Message Color</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-orange-500 rounded-lg border-2 border-white"></div>
            <span className="text-gray-200 text-sm">User Message Color</span>
          </div>
        </div>
      </div>

      {/* Reply Settings */}
      <div className="bg-white/10 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Mic className="w-5 h-5 text-yellow-400" />
          <span className="text-yellow-200 text-sm font-medium">Reply Settings</span>
        </div>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-gray-200 text-sm">Voice + Text responses</span>
            <Switch defaultChecked />
          </div>
        </div>
      </div>

      {/* Chat Preview */}
      <div className="bg-white/10 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-3">
          <MessageCircle className="w-5 h-5 text-blue-400" />
          <span className="text-blue-200 text-sm font-medium">Chat Preview</span>
        </div>
        <div className="bg-white rounded-lg p-3 space-y-2">
          <div className="flex items-start space-x-2">
            <Avatar className="w-8 h-8">
              <AvatarImage src="" alt="Company Logo" />
              <AvatarFallback className="bg-gray-200 text-gray-600 text-xs font-bold">
                LOGO
              </AvatarFallback>
            </Avatar>
            <div className="bg-purple-500 text-white p-2 rounded-lg text-sm max-w-xs">
              Hello! I'm your AI assistant. How can I help you today?
            </div>
          </div>
          <div className="flex justify-end">
            <div className="bg-orange-500 text-white p-2 rounded-lg text-sm max-w-xs">
              I have a question about my order
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <Palette className="w-8 h-8 text-purple-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">100%</div>
            <div className="text-gray-300 text-sm font-medium">Brand Match</div>
          </CardContent>
        </Card>
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <Zap className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">2min</div>
            <div className="text-gray-300 text-sm font-medium">Setup Time</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CustomizationView;
