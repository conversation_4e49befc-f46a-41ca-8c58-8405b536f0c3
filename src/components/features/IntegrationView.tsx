
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Globe, Zap, MoreVertical } from "lucide-react";

const IntegrationView = () => {
  return (
    <div className="space-y-4">
      {/* Header with tabs */}
      <div className="bg-white/10 rounded-lg p-4">
        <div className="flex space-x-4 mb-4">
          <button className="text-gray-300 px-3 py-1 rounded text-sm">WhatsApp</button>
          <button className="bg-pink-500 text-white px-3 py-1 rounded text-sm font-medium">LinkedIn</button>
          <button className="text-gray-300 px-3 py-1 rounded text-sm">Messenger</button>
        </div>
        
        {/* Platform integration section */}
        <div className="mb-4">
          <div className="flex space-x-2">
            <input 
              type="text" 
              placeholder="Connect platform account"
              className="flex-1 bg-gray-700 text-gray-200 px-3 py-2 rounded border border-gray-600 text-sm"
            />
            <button className="bg-pink-500 hover:bg-pink-600 text-white px-6 py-2 rounded text-sm font-medium">
              Connect
            </button>
          </div>
        </div>

        {/* Connected platforms section */}
        <div className="mb-4">
          <h4 className="text-gray-300 text-sm font-medium mb-2">Connected Platforms</h4>
        </div>
        
        {/* Platform list */}
        <div className="space-y-2">
          <div className="flex items-center justify-between bg-gray-700/50 p-3 rounded">
            <div className="flex items-center space-x-3">
              <Globe className="w-4 h-4 text-green-400" />
              <span className="text-gray-200 text-sm">LinkedIn Business</span>
            </div>
            <MoreVertical className="w-4 h-4 text-gray-400" />
          </div>
          <div className="flex items-center justify-between bg-gray-700/50 p-3 rounded">
            <div className="flex items-center space-x-3">
              <Globe className="w-4 h-4 text-green-400" />
              <span className="text-gray-200 text-sm">WhatsApp Business</span>
            </div>
            <MoreVertical className="w-4 h-4 text-gray-400" />
          </div>
        </div>
      </div>
      
      {/* Stats */}
      <div className="grid grid-cols-2 gap-4">
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <Globe className="w-8 h-8 text-green-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">3</div>
            <div className="text-gray-300 text-sm font-medium">Platforms</div>
          </CardContent>
        </Card>
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <Zap className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">Active</div>
            <div className="text-gray-300 text-sm font-medium">Status</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default IntegrationView;
