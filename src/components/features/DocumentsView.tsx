
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Globe, Code, Search, Plus, Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";

const DocumentsView = () => {
  const [activeTab, setActiveTab] = useState("documents");

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-white/10">
          <TabsTrigger value="documents" className="text-white data-[state=active]:bg-white/20">
            <FileText className="w-4 h-4 mr-2" />
            Document
          </TabsTrigger>
          <TabsTrigger value="website" className="text-white data-[state=active]:bg-white/20">
            <Globe className="w-4 h-4 mr-2" />
            Website
          </TabsTrigger>
          <TabsTrigger value="api" className="text-white data-[state=active]:bg-white/20 data-[state=active]:text-purple-400">
            <Code className="w-4 h-4 mr-2" />
            API(Tasks)
          </TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          <div className="bg-white/10 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <FileText className="w-5 h-5 text-blue-400" />
              <span className="text-blue-200 text-sm font-medium">Document Processing</span>
            </div>
            <div className="space-y-2">
              <div className="bg-blue-600 text-white p-2 rounded text-sm">
                PDF: Privacy Policy uploaded
              </div>
              <div className="bg-blue-600 text-white p-2 rounded text-sm">
                DOCX: Terms of Service uploaded
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="website" className="space-y-4">
          <div className="bg-white/10 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <Globe className="w-5 h-5 text-green-400" />
              <span className="text-green-200 text-sm font-medium">Website Intelligence</span>
            </div>
            <div className="space-y-3">
              <div className="flex items-center space-x-2 bg-gray-700 p-2 rounded">
                <span className="text-white text-sm">https://</span>
                <input 
                  type="text" 
                  placeholder="Enter website URL" 
                  className="bg-transparent text-white text-sm flex-1 outline-none"
                />
              </div>
              <div className="space-y-2">
                <div className="text-gray-300 text-xs">Connected Websites:</div>
                <div className="bg-green-600 text-white p-2 rounded text-sm blur-sm">
                  https://www.najoomi.ai
                </div>
                <div className="bg-green-600 text-white p-2 rounded text-sm blur-sm">
                  https://docs.najoomi.ai
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <div className="bg-white/10 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Code className="w-5 h-5 text-purple-400" />
                <span className="text-purple-200 text-sm font-medium">API Integration</span>
              </div>
              <Button size="sm" className="bg-purple-500 hover:bg-purple-600 text-white">
                <Plus className="w-4 h-4 mr-1" />
                ADD API
              </Button>
            </div>
            
            <div className="flex items-center space-x-2 mb-4 bg-gray-700/50 p-2 rounded">
              <Search className="w-4 h-4 text-gray-400" />
              <input 
                type="text" 
                placeholder="Search APIs" 
                className="bg-transparent text-white text-sm flex-1 outline-none"
              />
            </div>

            <div className="space-y-3">
              <div className="bg-purple-600 text-white p-3 rounded text-sm">
                <div className="flex items-center justify-between mb-1">
                  <span className="font-medium">Customer Data API</span>
                  <span className="text-xs bg-purple-800 px-2 py-1 rounded">POST</span>
                </div>
                <div className="text-purple-200 text-xs blur-sm">
                  https://api.najoomi.ai/customer/data
                </div>
              </div>
              
              <div className="bg-purple-600 text-white p-3 rounded text-sm">
                <div className="flex items-center justify-between mb-1">
                  <span className="font-medium">Plan Assignment API</span>
                  <span className="text-xs bg-purple-800 px-2 py-1 rounded">GET</span>
                </div>
                <div className="text-purple-200 text-xs blur-sm">
                  https://api.najoomi.ai/assign/plan
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="grid grid-cols-2 gap-4">
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <FileText className="w-8 h-8 text-blue-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">50+</div>
            <div className="text-gray-300 text-sm font-medium">Documents</div>
          </CardContent>
        </Card>
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <Globe className="w-8 h-8 text-green-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">99.8%</div>
            <div className="text-gray-300 text-sm font-medium">Accuracy</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DocumentsView;
