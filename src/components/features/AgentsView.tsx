
import { Card, CardContent } from "@/components/ui/card";
import { Users, TrendingUp } from "lucide-react";

const AgentsView = () => {
  return (
    <div className="space-y-6">
      <div className="bg-white/10 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Users className="w-5 h-5 text-orange-400" />
          <span className="text-orange-200 text-sm font-medium">Live Agent Transfer</span>
        </div>
        <div className="space-y-2">
          <div className="bg-orange-600 text-white p-2 rounded text-sm">
            Transferring to human agent...
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-400 rounded-full"></div>
            <span className="text-gray-200 text-sm">Agent <PERSON> - Available</span>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <Users className="w-8 h-8 text-orange-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">3s</div>
            <div className="text-gray-300 text-sm font-medium">Transfer Time</div>
          </CardContent>
        </Card>
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-4 text-center">
            <TrendingUp className="w-8 h-8 text-green-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">99%</div>
            <div className="text-gray-300 text-sm font-medium">Success Rate</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AgentsView;
