
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Mail, Users, TrendingUp, Clock } from "lucide-react";

const LeadGenerationView = () => {
  const leads = [
    { name: "<PERSON>", email: "<EMAIL>", phone: "+****************", source: "Website Chat", time: "2 min ago" },
    { name: "<PERSON>", email: "<EMAIL>", phone: "+****************", source: "Facebook Messenger", time: "15 min ago" },
    { name: "<PERSON>", email: "<EMAIL>", phone: "+****************", source: "WhatsApp", time: "1 hour ago" },
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-white mb-2">Smart Lead Generation</h3>
        <p className="text-gray-300 text-sm">AI collects contact information naturally during conversations</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-3 text-center">
            <Users className="w-6 h-6 text-blue-400 mx-auto mb-1" />
            <div className="text-white font-bold text-lg">127</div>
            <div className="text-gray-300 text-xs">Total Leads</div>
          </CardContent>
        </Card>
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-3 text-center">
            <TrendingUp className="w-6 h-6 text-green-400 mx-auto mb-1" />
            <div className="text-white font-bold text-lg">23%</div>
            <div className="text-gray-300 text-xs">Conversion Rate</div>
          </CardContent>
        </Card>
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-3 text-center">
            <Mail className="w-6 h-6 text-purple-400 mx-auto mb-1" />
            <div className="text-white font-bold text-lg">8</div>
            <div className="text-gray-300 text-xs">Today</div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Leads */}
      <Card className="bg-white/10 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm flex items-center">
            <Users className="w-4 h-4 mr-2" />
            Recent Leads
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="space-y-2">
            {leads.map((lead, index) => (
              <div key={index} className="flex items-center justify-between p-3 hover:bg-white/5 transition-colors">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-white font-medium text-sm">{lead.name}</span>
                    <Badge variant="secondary" className="text-xs bg-green-500/20 text-green-300 border-green-400">
                      New
                    </Badge>
                  </div>
                  <div className="text-gray-400 text-xs">{lead.email}</div>
                  <div className="text-gray-500 text-xs">{lead.source}</div>
                </div>
                <div className="text-right">
                  <div className="flex items-center text-gray-400 text-xs">
                    <Clock className="w-3 h-3 mr-1" />
                    {lead.time}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Conversation Example */}
      <Card className="bg-white/10 border-gray-600">
        <CardHeader className="pb-2">
          <CardTitle className="text-white text-sm">How It Works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="bg-purple-500/20 p-2 rounded text-purple-200 text-xs">
            AI: "I'd be happy to send you more information. Could I get your email address?"
          </div>
          <div className="bg-gray-700/50 p-2 rounded text-gray-200 text-xs">
            Customer: "Sure, it's <EMAIL>"
          </div>
          <div className="bg-purple-500/20 p-2 rounded text-purple-200 text-xs">
            AI: "Perfect! And may I have your name for our records?"
          </div>
          <div className="text-center">
            <Badge className="bg-green-500/20 text-green-300 border-green-400 text-xs">
              ✅ Lead Captured & Email Sent
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LeadGenerationView;
