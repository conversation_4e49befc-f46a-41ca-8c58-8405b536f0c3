
import { Phone, PhoneCall, Calendar, Target, Users, Clock } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

const CallHandlingView = () => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-white mb-2">Voice Solutions</h3>
        <p className="text-gray-200">Complete call handling and campaign automation</p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Inbound Call Handling */}
        <Card className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 border-blue-400/30">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg flex items-center justify-center">
                <Phone className="w-5 h-5 text-white" />
              </div>
              <CardTitle className="text-white">Inbound Call Management</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center space-x-2 text-gray-200">
              <Users className="w-4 h-4" />
              <span className="text-sm">AI-powered call routing</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-200">
              <Clock className="w-4 h-4" />
              <span className="text-sm">24/7 availability</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-200">
              <Target className="w-4 h-4" />
              <span className="text-sm">Smart escalation to live agents</span>
            </div>
          </CardContent>
        </Card>

        {/* Outbound Campaigns */}
        <Card className="bg-gradient-to-br from-green-500/20 to-blue-500/20 border-green-400/30">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-400 rounded-lg flex items-center justify-center">
                <PhoneCall className="w-5 h-5 text-white" />
              </div>
              <CardTitle className="text-white">Outbound Campaigns</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="text-sm text-gray-300 font-medium">Campaign Inputs:</div>
              <div className="text-xs text-gray-400 space-y-1">
                <div>• Product/Service Details</div>
                <div>• Contact Lists & Numbers</div>
                <div>• Mission (Demo booking, Follow-ups)</div>
              </div>
            </div>
            <div className="flex items-center space-x-2 text-gray-200">
              <Calendar className="w-4 h-4" />
              <span className="text-sm">Smart callback scheduling</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Campaign Flow Visualization */}
      <div className="bg-gray-800/30 rounded-lg p-4 border border-gray-600/30">
        <h4 className="text-white font-medium mb-3">Campaign Flow</h4>
        <div className="flex items-center justify-between text-sm">
          <div className="text-center">
            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mb-2">
              <span className="text-white text-xs">1</span>
            </div>
            <div className="text-gray-300">Setup</div>
          </div>
          <div className="flex-1 h-px bg-gray-600 mx-2"></div>
          <div className="text-center">
            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mb-2">
              <span className="text-white text-xs">2</span>
            </div>
            <div className="text-gray-300">Call</div>
          </div>
          <div className="flex-1 h-px bg-gray-600 mx-2"></div>
          <div className="text-center">
            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mb-2">
              <span className="text-white text-xs">3</span>
            </div>
            <div className="text-gray-300">Schedule</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallHandlingView;
