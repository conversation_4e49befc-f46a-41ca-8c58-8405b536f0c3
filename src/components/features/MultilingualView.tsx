
import { Globe, MessageSquare, Mic, Users } from "lucide-react";

const MultilingualView = () => {
  return (
    <div className="p-6 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-400/20">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Globe className="w-8 h-8 text-blue-400" />
          <div>
            <h3 className="text-xl font-semibold text-white">Global Language Support</h3>
            <p className="text-gray-300">Communicate naturally in 50+ languages</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-600">
          <div className="flex items-center space-x-2 mb-3">
            <MessageSquare className="w-5 h-5 text-blue-400" />
            <span className="text-white font-medium">Chat Support</span>
          </div>
          <p className="text-gray-300 text-sm">Real-time language detection and automatic switching within conversations</p>
        </div>
        
        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-600">
          <div className="flex items-center space-x-2 mb-3">
            <Mic className="w-5 h-5 text-blue-400" />
            <span className="text-white font-medium">Voice Calls</span>
          </div>
          <p className="text-gray-300 text-sm">Natural voice conversations in customer's preferred language</p>
        </div>
      </div>

      <div className="bg-gray-700/30 rounded-lg p-4 border border-gray-600">
        <div className="flex items-center space-x-2 mb-3">
          <Users className="w-5 h-5 text-blue-400" />
          <span className="text-white font-medium">Popular Languages</span>
        </div>
        <div className="flex flex-wrap gap-2">
          {["English", "Spanish", "French", "German", "Chinese", "Japanese", "Arabic", "Portuguese", "Hindi", "Russian"].map((lang) => (
            <span key={lang} className="px-3 py-1 bg-blue-500/20 text-blue-200 rounded-full text-xs border border-blue-400/30">
              {lang}
            </span>
          ))}
          <span className="px-3 py-1 bg-purple-500/20 text-purple-200 rounded-full text-xs border border-purple-400/30">
            +40 more
          </span>
        </div>
      </div>
    </div>
  );
};

export default MultilingualView;
