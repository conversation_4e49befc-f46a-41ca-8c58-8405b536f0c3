
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Bar<PERSON>hart3, T<PERSON>dingUp, MessageCircle, Clock, Users, Target } from "lucide-react";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>hart, Bar, XAxis, <PERSON>Axis, LineChart, Line, ResponsiveContainer } from "recharts";

const chartConfig = {
  positive: { color: "#8b5cf6" },
  negative: { color: "#ec4899" },
  neutral: { color: "#06b6d4" }
};

const sentimentData = [
  { name: "Positive", value: 65, color: "#8b5cf6" },
  { name: "Negative", value: 20, color: "#ec4899" },
  { name: "Neutral", value: 15, color: "#06b6d4" }
];

const usageData = [
  { time: "4 AM", conversations: 2 },
  { time: "6 AM", conversations: 4 },
  { time: "8 AM", conversations: 8 },
  { time: "10 AM", conversations: 6 },
  { time: "12 PM", conversations: 9 },
  { time: "2 PM", conversations: 5 }
];

const effectivenessData = [
  { name: "Success", value: 75, color: "#8b5cf6" },
  { name: "Partial", value: 15, color: "#ec4899" },
  { name: "Failed", value: 10, color: "#06b6d4" }
];

const AnalyticsView = () => {
  return (
    <div className="space-y-4">
      {/* Top Metrics Row */}
      <div className="grid grid-cols-2 gap-3">
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-bold text-lg">156</div>
                <div className="text-gray-300 text-xs">Total Messages</div>
              </div>
              <MessageCircle className="w-6 h-6 text-blue-400" />
            </div>
            <div className="text-green-400 text-xs mt-1">+22</div>
          </CardContent>
        </Card>
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-bold text-lg">42</div>
                <div className="text-gray-300 text-xs">Total Sessions</div>
              </div>
              <Users className="w-6 h-6 text-purple-400" />
            </div>
            <div className="text-green-400 text-xs mt-1">+8</div>
          </CardContent>
        </Card>
      </div>

      {/* Response Time & Conversation Time */}
      <div className="grid grid-cols-2 gap-3">
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-3">
            <div className="text-purple-400 font-bold text-lg">0m 19s</div>
            <div className="text-gray-300 text-xs">Avg Query Resolution</div>
          </CardContent>
        </Card>
        <Card className="bg-white/10 border-gray-600">
          <CardContent className="p-3">
            <div className="text-purple-400 font-bold text-lg">11m 47s</div>
            <div className="text-gray-300 text-xs">Avg Conversation Time</div>
          </CardContent>
        </Card>
      </div>

      {/* Sentiment Analysis */}
      <div className="bg-white/10 rounded-lg p-3">
        <div className="flex items-center space-x-2 mb-2">
          <Target className="w-4 h-4 text-pink-400" />
          <span className="text-pink-200 text-xs font-medium">Sentiment Analysis</span>
        </div>
        <div className="h-24">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={sentimentData}
                cx="50%"
                cy="50%"
                innerRadius={20}
                outerRadius={40}
                dataKey="value"
              >
                {sentimentData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Bot Usage Chart */}
      <div className="bg-white/10 rounded-lg p-3">
        <div className="flex items-center space-x-2 mb-2">
          <BarChart3 className="w-4 h-4 text-blue-400" />
          <span className="text-blue-200 text-xs font-medium">Usage by Time</span>
        </div>
        <div className="h-20">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={usageData}>
              <XAxis dataKey="time" hide />
              <YAxis hide />
              <Bar dataKey="conversations" fill="#8b5cf6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Effectiveness Chart */}
      <div className="bg-white/10 rounded-lg p-3">
        <div className="flex items-center space-x-2 mb-2">
          <TrendingUp className="w-4 h-4 text-green-400" />
          <span className="text-green-200 text-xs font-medium">Conversation Effectiveness</span>
        </div>
        <div className="h-20">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={effectivenessData}
                cx="50%"
                cy="50%"
                innerRadius={15}
                outerRadius={35}
                dataKey="value"
              >
                {effectivenessData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsView;
