import React from 'react';
import { Check, Minus } from 'lucide-react';

const PricingTable = ({ billingType }) => {
  const featureGroups = [
    {
      heading: "AI Powered Responses:",
      features: [
        "Unlimited Text replies",
        "Unlimited Voice Messages",
        "Inbound calls (soon)",
        "Outbound call support"
      ]
    },
    {
      heading: "Uploading Data:",
      features: [
        "Document Upload"
      ]
    },
    {
      heading: "Multilingual Support:",
      features: [
        "90+ Languages Available for texts & calls"
      ]
    },
    {
      heading: "Custom Branding:",
      features: [
        "Customize bot",
        "Upload Logo"
      ]
    },
    {
      heading: "Quality Management:",
      features: [
        "Analytics Dashboard",
        "Message History"
      ]
    },
    {
      heading: "Integrations (soon):",
      features: [
        "Platforms for bot integration",
        "Api integrations with CRM and CMS"
      ]
    }
  ];

  const tiers = [
    {
      name: "Basic",
      monthlyPrice: "$20",
      yearlyPrice: "$204",
      originalMonthlyPrice: "$50",  // Added original price for Monthly
      originalYearlyPrice: "$240",   // Added original price for Yearly
      buttontext: "Buy Now",
      features: {
        "Unlimited Text replies": true,
        "Unlimited Voice Messages": true,
        "Inbound calls (soon)": "-",
        "Outbound call support": "-",
        "Document Upload": "50 Docs/month",
        "90+ Languages Available for texts & calls": true,
        "Customize bot": true,
        "Upload Logo": true,
        "Analytics Dashboard": "-",
        "Message History": "-",
        "Platforms for bot integration": "-",
        "Api integrations with CRM and CMS": "-"
      }
    },
    {
      name: "Standard",
      monthlyPrice: "Contact us",
      yearlyPrice: "Contact us",
      buttontext: "Talk to Sales",
      features: {
        "Unlimited Text replies": true,
        "Unlimited Voice Messages": true,
        "Inbound calls (soon)": "300 mins/month",
        "Outbound call support": "-",
        "Document Upload": "100 Docs/month",
        "90+ Languages Available for texts & calls": "5 Languages of Choice",
        "Customize bot": true,
        "Upload Logo": true,
        "Analytics Dashboard": true,
        "Message History": "-",
        "Platforms for bot integration": "2",
        "Api integrations with CRM and CMS": "-"
      }
    },
    {
      name: "Premium",
      monthlyPrice: "Contact us",
      yearlyPrice: "Contact us",
      buttontext: "Talk to Sales",
      features: {
        "Unlimited Text replies": true,
        "Unlimited Voice Messages": true,
        "Inbound calls (soon)": "300 mins/month",
        "Outbound call support": "1000 calls/month",
        "Document Upload": "250 Docs/month",
        "90+ Languages Available for texts & calls": "20 Languages of Choice",
        "Customize bot": true,
        "Upload Logo": true,
        "Analytics Dashboard": true,
        "Message History": true,
        "Platforms for bot integration": "4",
        "Api integrations with CRM and CMS": "-"
      }
    },
    {
      name: "Enterprise",
      monthlyPrice: "Contact us",
      yearlyPrice: "Contact us",
      buttontext: "Talk to Sales",
      features: {
        "Unlimited Text replies": true,
        "Unlimited Voice Messages": true,
        "Inbound calls (soon)": "Unlimited",
        "Outbound call support": "Unlimited",
        "Document Upload": "Unlimited",
        "90+ Languages Available for texts & calls": "90+ Languages of Choice",
        "Customize bot": true,
        "Upload Logo": true,
        "Analytics Dashboard": true,
        "Message History": true,
        "Platforms for bot integration": "10",
        "Api integrations with CRM and CMS": true
      }
    }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-4">
      <div className="flex flex-col md:flex-row gap-6">

        <div className="w-full md:w-64 flex flex-col pt-8 md:pt-56 hidden md:block">
          {featureGroups.map((group, groupIndex) => (
            <div key={groupIndex} className="mb-6">
              <h4 className="font-semibold text-sm text-gray-900 mb-2">
                {group.heading}
              </h4>
              {group.features.map((feature) => (
                <div
                  key={feature}
                  className="text-sm text-gray-600 pl-4 h-8 flex items-center"
                >
                  {feature}
                </div>
              ))}
            </div>
          ))}
        </div>


        <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {tiers.map((tier, index) => (
            <div key={index} className="min-w-[200px] flex flex-col bg-white rounded-lg shadow-md transition-all duration-300 hover:shadow-xl hover:scale-105">
 
              <div className="border border-[#CD0ADD] p-4 rounded-t-lg text-center">
                <h3 className="text-xl text-[#CD0ADD] font-bold">{tier.name}</h3>
              </div>


              <div className="p-4 flex flex-col justify-between flex-1">

                <div className="text-center mb-8">

                  <p className="text-sm line-through text-gray-500">
                    <p className="text-sm line-through text-gray-500">
                      {billingType === 'monthly'
                        ? (tier.originalMonthlyPrice || '-----------------')
                        : (tier.originalYearlyPrice || '-----------------')}
                    </p>

                  </p>
                  <p className="text-2xl font-bold text-black  mb-4">
                    {billingType === 'monthly' ? tier.monthlyPrice : tier.yearlyPrice}
                    {tier.originalYearlyPrice && billingType === 'yearly' && (
                      <p className=" text-sm text-gray-500">
                        15% Off billed annually
                      </p>
                    )}
                  </p>

                  <button
                    className={`w-full py-2 px-4 bg-[#CD0ADD] text-white rounded-md hover:bg-purple-700 transition-all duration-300 hover:shadow-lg active:scale-95 ${(tier.name === 'Standard' || tier.name === 'Premium' || tier.name === 'Enterprise') && billingType === 'yearly'
                        ? 'mt-4'
                        : ''
                      }`}
                  >
                    {tier.buttontext}
                  </button>
                </div>


                <div className="block md:hidden">
                  {featureGroups.map((group, groupIndex) => (
                    <div key={groupIndex} className="mb-6">

                      <h4 className="font-semibold text-sm text-gray-900 mb-2">{group.heading}</h4>
                      {group.features.map((feature) => (
                        <div key={feature} className="h-8 flex items-center justify-start pl-2 text-sm">

                          <span className="text-gray-600">{feature}</span>

  
                          {typeof tier.features[feature] === 'boolean' ? (
                            tier.features[feature] ? (
                              <Check className="w-5 h-5 text-purple-600 ml-2" />
                            ) : (
                              <Minus className="w-5 h-5 text-gray-400 ml-2" />
                            )
                          ) : (
                            <span className="text-sm text-gray-600 ml-2">
                              {tier.features[feature]}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  ))}
                </div>


                <div className="hidden md:block">
                  {featureGroups.map((group, groupIndex) => (
                    <div key={groupIndex} className="mb-6">
                      <div className="h-6"></div>
                      {group.features.map((feature) => (
                        <div
                          key={feature}
                          className="h-8 flex items-center justify-center"
                        >
                          {typeof tier.features[feature] === 'boolean' ? (
                            tier.features[feature] ? (
                              <Check className="w-5 h-5 text-purple-600" />
                            ) : (
                              <Minus className="w-5 h-5 text-gray-400" />
                            )
                          ) : (
                            <span className="text-sm text-gray-600">
                              {tier.features[feature]}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PricingTable;

