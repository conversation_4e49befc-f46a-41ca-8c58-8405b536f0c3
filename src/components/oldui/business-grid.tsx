'use client'

interface ServiceCardProps {
    icon: string // Now a path to an image (SVG)
    title: string
    description: string
}

function ServiceCard({ icon, title, description }: ServiceCardProps) {
    return (
        <div className="relative group overflow-hidden rounded-lg bg-gradient-to-b from-[#033035] via-[#0C3330] to-[#183C1E] p-6 transition-all duration-300 hover:shadow-xl h-[200px]">
            <div className="flex flex-col items-center justify-center h-full text-white">
                {/* The icon is initially centered but moves to top-left on hover */}
                <div className="relative flex flex-col items-center mb-4 transition-all duration-300">
                    <div className="group-hover:absolute group-hover:opacity-0 group-hover:top-0 hover:scale-75 transition-all duration-300">
                        {/* Load the SVG as an image */}
                        <img src={icon} className="h-24 w-24" alt={title} />
                    </div>

                    {/* Title fades out on hover */}
                    <h3 className="text-center text-sm font-medium transition-opacity duration-300 group-hover:opacity-0">
                        {title}
                    </h3>
                </div>
                {/* Description fully revealed on hover */}
                <div className="absolute inset-0 flex items-center justify-center  transform translate-y-full group-hover:translate-y-0 transition-all duration-300 ease-in-out group-hover:h-full">
                    <p className="text-left text-sm px-4">{description}</p>
                </div>
            </div>
        </div>
    )
}

export default function BusinessGrid() {
    const services = [
        {
            icon: "/bank white.svg",
            title: "Banking & Finance",
            description: "No more long wait times or frustration—Quicktalk offers 24/7 customer support with human-like responses. Provide seamless customer support across every platform, ensuring their needs are met anytime, anywhere. "
        },
        {
            icon: "/courier.svg",
            title: "Courier Services & Online order",
            description: "QuickTalk is the friendly face your customers need. From tracking orders to answering queries and placing orders, so you can focus on managing your business while QuickTalk takes care of customers."
        },
        {
            icon: "/healthcare white 1.svg",
            title: "Medical & Healthcare",
            description: "QuickTalk manages your appointment scheduling, helps to quickly respond to customer queries, and avoids long waits so you can focus on the health of your clients."
        },
        {
            icon: "/Ticketing white.svg",
            title: "Ticketing",
            description: "QuickTalk enables instant ticket booking, easy management of cancellations and rescheduling, and offers route guidance for buses, trains, or flights. So, your customers remain calm and satisfied while prioritizing your business."
        },
        {
            icon: "/BPO's white.svg",
            title: "BPO's & Telcos",
            description: "QuickTalk is like your AI buddy that requires no training time, communicates in 90+ languages, and provides daily analytics at a glance, making you concentrate on delivering outstanding customer service."
        },
        {
            icon: "/businesses white.svg",
            title: "Your Business",
            description: "Whether you're a small startup or a large enterprise, QuickTalk adapts to your unique business needs. Our flexible solution takes your customer support to the next level, no matter what business you're in."
        }
    ]

    return (
        <div className="w-full px-4 py-12 font-manrope">
            <h1 className="mb-12 text-center text-5xl font-bold">
                QuickTalk Fits Every Business
            </h1>
            <div className="mx-auto grid max-w-6xl grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 justify-center items-center align-center">
                {services.map((service, index) => (
                    <ServiceCard
                        key={index}
                        icon={service.icon}
                        title={service.title}
                        description={service.description}
                    />
                ))}
            </div>
        </div>

    )
}
