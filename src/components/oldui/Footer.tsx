import Link from "next/link";
import { useRouter } from 'next/router'; // Import the useRouter hook
import { Facebook, Twitter, Linkedin, Instagram } from 'lucide-react';

export default function Footer() {
  const router = useRouter(); // Access the current route

  const showCTA = ['/Pricing', '/Solutions'].includes(router.pathname);

  return (
    <div className="font-manrope relative mt-20">
      {/* Conditionally render the CTA section */}
      {showCTA && (
        <div className="absolute -top-16 left-1/2 -translate-x-1/2 w-full max-w-4xl">
          <div className="pb-16 mx-4 bg-[#BF00CD] rounded-[2rem] py-12 px-14 flex flex-col sm:flex-row items-center justify-between">
            <h3 className="text-white text-xl sm:text-2xl font-semibold mb-4 sm:mb-0">
              Get started with QuickTalk today!
            </h3>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/Pricing"
                className=" bg-white text-center text-[#CD0ADD] px-6 py-2 rounded-lg text-sm font-semibold hover:bg-gray-100 transition-all duration-300"

              >
                VIEW PRICING
              </Link>
              <Link
                href="/demo"
                className="bg-transparent border-2 border-white text-white px-6 py-2 rounded-lg text-sm font-semibold hover:bg-white/10 transition-all duration-300"
              >
                GET A FREE DEMO
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Footer Content */}
      <footer className="bg-gradient-to-b from-[#033035] via-[#0C3330] to-[#183C1E] text-white pt-28 pb-12">
      <div className="container mx-auto grid grid-cols-1 md:grid-cols-[2fr,1fr,1fr,1fr] gap-12 px-6">
        {/* Logo Section */}
        <div className="flex flex-col justify-center items-center md:items-start">
          <div className="flex items-center gap-3">
            <div className="space-x-2 flex text-2xl font-semibold sm:pr-16">
              <Link href="/">
                <img src="/QuickTalkLogonewwhite.png" alt="Logo" className="h-18 md:h-[4rem] pt-1" />
              </Link>
            </div>
          </div>
        </div>

        {/* Company Links Section */}
        <div className="text-center md:text-left">
          <h3 className="text-[#BF00CD] font-medium text-lg mb-8">Company</h3>
          <ul className="space-y-3 text-gray-300 text-sm list-none">
            {['Home', 'About', 'Features', 'Pricing', 'Careers', 'Contact Us'].map((item) => (
              <li key={item}>
                <Link href="#" className="hover:text-[#BF00CD] transition duration-300">
                  {item}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Contact Section */}
        <div className="text-center md:text-left">
          <h3 className="text-[#BF00CD] font-medium text-lg mb-4">Reach us out</h3>
          <div className="text-sm space-y-4">
            {[
              { label: 'For general queries', email: '<EMAIL>' },
              { label: 'For sales queries', email: '<EMAIL>' },
            ].map((item) => (
              <div key={item.email}>
                <p className="text-gray-300 text-xs mb-1">{item.label}</p>
                <Link href={`mailto:${item.email}`} className="text-[#BF00CD] hover:underline">
                  {item.email}
                </Link>
              </div>
            ))}
          </div>
        </div>

        {/* Social Media Section */}
        <div className="text-center md:text-left">
          <h3 className="text-[#BF00CD] font-medium text-lg mb-4">Follow us</h3>
          <p className="text-sm text-gray-300 mb-4">Follow us on social media</p>
          <div className="flex justify-center md:justify-start space-x-4">
            {[
              { href: 'https://www.facebook.com/profile.php?id=61571355689803', icon: Facebook },
              { href: 'https://x.com/najoomitech', icon: Twitter },
              { href: 'https://www.linkedin.com/company/najoomi/', icon: Linkedin },
              { href: 'https://www.instagram.com/najoomitechnologies/', icon: Instagram },
            ].map((item, index) => (
              <Link key={index} href={item.href} className="hover:text-[#BF00CD] transition duration-300">
                <item.icon className="w-5 h-5" />
              </Link>
            ))}
          </div>
        </div>
      </div>
    </footer>
    </div>
  );
}
