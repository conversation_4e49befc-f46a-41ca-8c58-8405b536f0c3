import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
} from "@material-tailwind/react";

const PricingCard = ({
  title,
  plan,
  miniheading,
  soon,
  features,
}) => {
  return (
    <Card className="w-[300px] h-[500px] overflow-hidden border border-gray-200 shadow-xl rounded-lg transform transition-transform hover:scale-105">
      <CardHeader
        floated={false}
        shadow={false}
        color="transparent"
        className="m-0 rounded-none p-6 bg-gradient-to-r from-purple-500 to-pink-500 text-white"
      >
        <Typography variant="h3" className="font-semibold font-caprasimo text-center">
          {title}
        </Typography>
        <Typography variant="h4" className="font-semibold text-white text-center mt-2">
          {plan}
        </Typography>
        <Typography variant="h4" className="font-semibold text-white text-center mt-2">
          {soon}
        </Typography>
      </CardHeader>
      <CardBody className="p-6 pt-4 relative">

        <Typography variant="small" className="font-bold text-[#063C4F] text-center pt-4">
          {miniheading}
        </Typography>

        {/* Features List */}
        <ul className="mt-6 space-y-3">
          {features?.map((feature, index) => (
            <li key={index} className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-5 h-5 text-fuchsia-500 mr-2 text-[#BF00CD]"
              >
                <path
                  fillRule="evenodd"
                  d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z"
                  clipRule="evenodd"
                />
              </svg>
              <Typography variant="small" className="font-normal text-gray-700">
                {feature}
              </Typography>
            </li>
          ))}
        </ul>
      </CardBody>
    </Card>
  );
};

export default PricingCard;
