import React from 'react';
import { Button } from '@mui/material';
import { TbMessageChatbotFilled } from 'react-icons/tb';
import { LuLogOut } from 'react-icons/lu';
import { useRouter } from 'next/router';  // Import useRouter for navigation

interface HeaderProps {
  isMobile: boolean;
  toggleChatHistory: () => void;
  setIsModalOpen: (isOpen: boolean) => void;
}

export default function Header({ isMobile, toggleChatHistory, setIsModalOpen }: HeaderProps) {
  const router = useRouter();  // Initialize useRouter

  // Function to navigate to the home page
  const navigateHome = () => {
    router.push('/');  // Navigate to the home page
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-10 bg-white shadow-md ${
        isMobile ? 'hidden' : 'block'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
        {/* Chat History Button */}
        <Button
          variant="outlined"
          onClick={toggleChatHistory}
          className="hidden md:flex items-center"
        >
          <TbMessageChatbotFilled className="mr-2 text-xl" />
          Chat History
        </Button>
        
        {/* Home Button */}
        <Button
          variant="outlined"
          onClick={navigateHome}  // Call navigateHome function on click
          className="hidden md:flex items-center"
        >
          Home
        </Button>

        {/* Logout Button */}
        <Button
          variant="outlined"
          onClick={() => setIsModalOpen(true)}
          className="hidden md:flex items-center"
        >
          <LuLogOut className="mr-2 text-xl" />
          Logout
        </Button>
      </div>
    </header>
  );
}
