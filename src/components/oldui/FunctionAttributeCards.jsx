"use client"

import { useState, useEffect } from "react"
import {
  Card,
  CardHeader,
  CardBody,
  Typography,
  Button,
  IconButton,
  Collapse,
  Checkbox,
  Spinner,
} from "@material-tailwind/react"
import { ChevronDown, Pencil, Trash, Plus, Search, ArrowLeft } from "lucide-react"
import SettingsPage from "../Dashboard/function/FunctionTools"
import AddAttributeDialog from "./AddAttributeModal"

const FunctionsList = ({ functions, attributes, onEdit, onDelete, onDeleteAttribute, onAddAttribute, onFetch, isTicketOnly }) => {
  const [openCards, setOpenCards] = useState({})
  const [loading, setLoading] = useState({})
  const [searchQuery, setSearchQuery] = useState("")
  const [expandedFunctions, setExpandedFunctions] = useState({})
  const [showSettingsPage, setShowSettingsPage] = useState(false)
  const [addAttributeDialog, setAddAttributeDialog] = useState({
    isOpen: false,
    functionId: null,
  })

  // Add refreshing state
  const [refreshing, setRefreshing] = useState(false)

  const toggleOpen = (id) => {
    setOpenCards((prevState) => ({
      ...prevState,
      [id]: !prevState[id],
    }))
  }

  const toggleFunctionExpand = (functionId) => {
    setExpandedFunctions((prev) => ({
      ...prev,
      [functionId]: !prev[functionId],
    }))
  }

  // Safe toLowerCase helper function
  const safeToLowerCase = (value) => {
    if (typeof value === "string") {
      return value.toLowerCase()
    }
    // If not a string, convert to string first
    return String(value).toLowerCase()
  }

  const filteredFunctions = functions.filter((func) => {
    const searchQueryLower = searchQuery.toLowerCase()
    const functionNameLower = safeToLowerCase(func.functionName)
    const functionIdLower = safeToLowerCase(func.functionid)

    return functionNameLower.includes(searchQueryLower) || functionIdLower.includes(searchQueryLower)
  })

  const functionsWithAttributes = functions.map((func) => ({
    ...func,
    attributes: attributes.filter((attr) => attr.attributefid === func.functionid),
  }))

  const handleUpdate = async (functionId) => {
    setLoading((prev) => ({ ...prev, [functionId]: true }))
    try {
      await onEdit(functionId)
    } finally {
      setLoading((prev) => ({ ...prev, [functionId]: false }))
    }
  }

  const handleAddAttribute = async (functionId, attributeData) => {
    setLoading((prev) => ({ ...prev, [functionId]: true }))
    try {
      await onAddAttribute(functionId, attributeData)
      setAddAttributeDialog({ isOpen: false, functionId: null })
    } finally {
      setLoading((prev) => ({ ...prev, [functionId]: false }))
    }
  }

  const handleDelete = async (functionId, attributeIds) => {
    setLoading((prev) => ({ ...prev, [functionId]: true }))
    try {
      await onDelete(functionId, attributeIds)
      // If the function is deleted, make sure to remove it from expanded state
      setExpandedFunctions((prev) => {
        const newState = { ...prev }
        delete newState[functionId]
        return newState
      })
    } finally {
      setLoading((prev) => ({ ...prev, [functionId]: false }))
    }
  }

  const AttributehandleDelete = async (functionId, attributeId) => {
    setLoading((prev) => ({ ...prev, [functionId]: true }))
    try {
      await onDeleteAttribute(functionId, [attributeId])
    } finally {
      setLoading((prev) => ({ ...prev, [functionId]: false }))
    }
  }

  // Add a refreshFunctions method that calls onFetch
  const refreshFunctions = async () => {
    if (onFetch) {
      setRefreshing(true)
      try {
        await onFetch()
      } finally {
        setRefreshing(false)
      }
    }
  }

  // Toggle the SettingsPage view with refresh functionality
  const toggleSettingsPage = () => {
    // If we're currently showing settings page and switching back to list view
    if (showSettingsPage) {
      setShowSettingsPage(false)
      // Refresh data when returning to list view
      refreshFunctions()
    } else {
      setShowSettingsPage(true)
    }
  }

  // Render the detailed card for a function
  const renderFunctionCard = (func) => {
    const functionWithAttributes = {
      ...func,
      attributes: attributes.filter((attr) => attr.attributefid === func.functionid),
    }

    return (
      <Card className="shadow-lg hover:shadow-xl transition-shadow w-full mt-2 mb-4">
        <CardHeader
          floated={false}
          color="blue-gray"
          className="relative h-auto min-h-[6rem] flex flex-col justify-center bg-gradient-to-b from-[#1a3b2a] via-[#0f2a24] to-[#0b1f1b] p-4"
        >
          <div className="break-words">
            <Typography variant="h5" color="white" className="mb-1 text-base sm:text-lg font-manrope">
              {functionWithAttributes.functionName}
            </Typography>
            <Typography variant="small" color="white" className="opacity-80 font-manrope">
              F-ID: {functionWithAttributes.functionid}
            </Typography>
            <Typography variant="small" color="white" className="opacity-80 font-manrope">
              Description: {functionWithAttributes.functionDefinition}
            </Typography>
            <Typography variant="small" color="white" className="opacity-80 font-manrope">
              Api Method: {functionWithAttributes.apimethod}
            </Typography>
          </div>
        </CardHeader>
        <CardBody className="p-4">
          <div className="mb-4">
            <Typography variant="small" color="blue-gray" className="font-normal break-words">
              <span className="font-medium">API LINK:</span>{" "}
              <span className="break-all">{functionWithAttributes.functionapilink}</span>
            </Typography>
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 mb-4">
            <div className="flex gap-2 w-full sm:w-auto justify-end">
              <Button
                size="sm"
                color="blue"
                className="font-manrope flex items-center gap-1 flex-1 sm:flex-initial justify-center bg-[#CD0ADD] text-white hover:bg-purple-700"
                onClick={() => handleUpdate(functionWithAttributes.functionid)}
                disabled={loading[functionWithAttributes.functionid]}
              >
                <Pencil className="h-4 w-4" />
                <span className="whitespace-nowrap">Edit</span>
              </Button>
              <Button
                size="sm"
                color="red"
                variant="filled"
                className="font-manrope flex items-center gap-1 flex-1 sm:flex-initial justify-center bg-[#CD0ADD] text-white hover:bg-purple-700"
                onClick={() =>
                  handleDelete(
                    functionWithAttributes.functionid,
                    functionWithAttributes.attributes.map((attr) => attr.attributeid),
                  )
                }
                disabled={loading[functionWithAttributes.functionid]}
              >
                {loading[functionWithAttributes.functionid] ? (
                  <Spinner className="h-4 w-4" />
                ) : (
                  <>
                    <Trash className="h-4 w-4" />
                    <span className="whitespace-nowrap">Delete</span>
                  </>
                )}
              </Button>
              <Button
                variant="text"
                color="blue-gray"
                className="font-manrope flex items-center justify-center px-2 py-2"
                onClick={() => toggleOpen(functionWithAttributes.functionid)}
              >
                <div className="whitespace-nowrap">
                  {openCards[functionWithAttributes.functionid] ? "Hide" : "Show"} Attributes
                </div>
                <ChevronDown
                  className={`h-4 w-4 transition-transform ${openCards[functionWithAttributes.functionid] ? "rotate-180" : ""}`}
                />
              </Button>
            </div>
          </div>
          <Button
            size="sm"
            color="green"
            variant="outlined"
            className="font-manrope flex items-center gap-1 flex-1 sm:flex-initial justify-center mb-2 bg-[#CD0ADD] text-white hover:bg-purple-700"
            onClick={() => setAddAttributeDialog({ isOpen: true, functionId: functionWithAttributes.functionid })}
          >
            <Plus className="h-4 w-4" />
            <span className="whitespace-nowrap">Add Attribute</span>
          </Button>
          <Collapse open={openCards[functionWithAttributes.functionid]}>
            <div className="overflow-y-auto max-h-[300px] space-y-3 pr-2">
              {functionWithAttributes.attributes && functionWithAttributes.attributes.length > 0 ? (
                functionWithAttributes.attributes.map((attr) => (
                  <Card key={attr.attributeid} className="p-3 hover:bg-blue-gray-50 transition-colors">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                      <div className="flex-1 min-w-0">
                        <Typography
                          variant="h6"
                          color="blue-gray"
                          className="text-sm font-medium break-words font-manrope"
                        >
                          Name of Attribute: {attr.attributeName}
                        </Typography>
                        P-ID: {attr.attributeid}
                        <Typography
                          variant="h6"
                          color="blue-gray"
                          className="text-sm font-medium break-words font-manrope"
                        >
                          Attribute Description: {attr.attributeDescription}
                        </Typography>
                        <div className="flex items-center gap-2">
                          <Typography variant="h6" color="blue-gray" className="text-sm font-medium font-manrope">
                            Required:
                          </Typography>
                          <Checkbox checked={Boolean(attr.required)} disabled />
                        </div>
                      </div>
                      <IconButton
                        variant="text"
                        color="blue-gray"
                        className="font-manrope rounded-full self-end sm:self-center bg-[#CD0ADD] text-white hover:bg-purple-700"
                        onClick={() => onEdit(functionWithAttributes.functionid, attr.attributeid)}
                      >
                        <Pencil className="h-4 w-4" />
                      </IconButton>
                      <IconButton
                        variant="text"
                        color="red"
                        className="font-manrope rounded-full self-end sm:self-center bg-[#CD0ADD] text-white hover:bg-purple-700"
                        onClick={() => AttributehandleDelete(functionWithAttributes.functionid, attr.attributeid)}
                      >
                        {loading[functionWithAttributes.functionid] ? (
                          <Spinner className="h-4 w-4" />
                        ) : (
                          <>
                            <Trash className="h-4 w-4" />
                          </>
                        )}
                      </IconButton>
                    </div>
                  </Card>
                ))
              ) : (
                <Typography variant="small" color="blue-gray" className="text-center py-4 font-manrope">
                  No attributes available
                </Typography>
              )}
            </div>
          </Collapse>
        </CardBody>
      </Card>
    )
  }

  // If SettingsPage is shown, render it instead of the functions list
  if (showSettingsPage) {
    return (
      <div className="w-full">
        <div className="mb-6">
          <Button
            size="md"
            className="font-manrope flex items-center gap-1 bg-[#CD0ADD] text-white hover:bg-purple-700 px-6 py-2"
            onClick={toggleSettingsPage}
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="whitespace-nowrap">Back to Functions List</span>
          </Button>
        </div>
        <SettingsPage
          onComplete={toggleSettingsPage}
          isTicketOnly={isTicketOnly}
        />
      </div>
    )
  }

  // Add this filter function near the top with other filter functions
  const getFilteredFunctions = () => {
    let filteredResults = functions;
    
    // Filter for ticket generation functions when isTicketOnly is true
    if (isTicketOnly) {
      return filteredResults.filter(func => 
        func.functionName.toLowerCase() === "generate_ticket"
      );
    }
    
    // Filter out ticket generation functions when in normal view
    filteredResults = filteredResults.filter(func => 
      func.functionName.toLowerCase() !== "generate_ticket"
    );
    
    // Apply search filter only in normal view
    if (searchQuery) {
      return filteredResults.filter(func =>
        func.functionName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    return filteredResults;
  };

  // Add this function to check if ticket generation exists
  const hasTicketGenerationFunction = () => {
    return functions.some(func => 
      func.functionName.toLowerCase() === "generate_ticket"
    );
  };

  return (
    <div className="p-4 w-full font-manrope">
      {/* Only show search bar when not in ticket generation mode */}
      {!isTicketOnly && (
        <div className="mb-6">
          <div className="relative w-full max-w-[500px] mx-auto">
            <input
              type="text"
              placeholder="Search"
              className="w-full h-10 pl-4 pr-10 rounded-full border border-gray-300 focus:outline-none focus:border-[#CD0ADD] shadow-md"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
              <Search size={18} />
            </div>
          </div>
        </div>
      )}

      {/* Add Task Button - Hide if isTicketOnly and ticket generation exists */}
      {(!isTicketOnly || (isTicketOnly && !hasTicketGenerationFunction())) && (
        <div className="mb-6">
          <div className="flex justify-end items-center mb-4">
            <Button
              size="md"
              className="font-manrope flex items-center gap-1 bg-[#CD0ADD] text-white hover:bg-purple-700 px-8 py-3"
              onClick={toggleSettingsPage}
            >
              <Plus className="h-4 w-4" />
              <span className="whitespace-nowrap">
                {isTicketOnly ? "Add Ticket Generation" : "Add Task"}
              </span>
            </Button>
          </div>
        </div>
      )}

      {/* Function List View with Inline Cards */}
      <div className="space-y-2 w-full bg-white border border-gray-200 rounded-lg font-manrope">
        <table className="w-full table-auto">
          <thead>
            <tr className="bg-gray-100">
              <th className="px-6 py-3 border-b-2 border-gray-300 text-left"></th>
              <th className="px-6 py-3 border-b-2 border-gray-300 w-16"></th>
            </tr>
          </thead>
        </table>

        <div className="p-4">
          {getFilteredFunctions().length > 0 ? (
            getFilteredFunctions().map((func) => (
              <div key={func.functionid} className="mb-4">
                <div
                  className={`p-4 bg-white rounded-lg border ${
                    expandedFunctions[func.functionid] ? "border-[#CD0ADD] border-2" : "border-gray-200"
                  } cursor-pointer transition-all hover:shadow-md`}
                  onClick={() => toggleFunctionExpand(func.functionid)}
                >
                  <Typography className="font-manrope font-medium">{func.functionName}</Typography>
                </div>

                {/* Card for this function appears directly below when expanded */}
                {expandedFunctions[func.functionid] && renderFunctionCard(func)}
              </div>
            ))
          ) : (
            <Typography className="text-center py-8 text-gray-600">
              {isTicketOnly ? "No ticket generation function found." : "No functions found matching your search."}
            </Typography>
          )}
        </div>
      </div>

      <AddAttributeDialog
        isOpen={addAttributeDialog.isOpen}
        onClose={() => setAddAttributeDialog({ isOpen: false, functionId: null })}
        onSubmit={handleAddAttribute}
        functionId={addAttributeDialog.functionId}
        loading={loading[addAttributeDialog.functionId]}
      />
    </div>
  )
}

export default FunctionsList