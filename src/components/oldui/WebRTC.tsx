"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, MicOff, Phone, PhoneOff, Radio, Volume2, Wifi, WifiOff, MessageSquare } from "lucide-react"

// Add type declaration for window.WebRTCConfig
declare global {
  interface Window {
    WebRTCConfig?: any
    apiLinks?: any
  }
}

// Configuration for WebRTC Frontend
function getBackendUrl() {
  const hostname = window.location.hostname
  if (hostname === "localhost" || hostname === "127.0.0.1") {
    return "http://localhost:5055"
  } else if (hostname.includes("run.app")) {
    const backendUrl = (window as any).BACKEND_URL || "https://webrtc-backend-985795046666.us-central1.run.app"
    return backendUrl
  } else if (hostname.includes("herokuapp.com")) {
    return `https://your-backend-app.herokuapp.com`
  } else if (hostname.includes("azurewebsites.net")) {
    return `https://your-backend-app.azurewebsites.net`
  } else {
    return `${window.location.protocol}//api.${hostname}`
  }
}

// Initialize WebRTC configuration
if (typeof window !== "undefined") {
  window.WebRTCConfig = {
    backendUrl: getBackendUrl(),
    debug: window.location.hostname === "localhost",
    retryAttempts: 3,
    retryDelay: 1000,
    preferDirectApiCalls: true,
    apiTimeout: 10000,
  }

  if (window.WebRTCConfig.debug) {
    console.log("WebRTC Configuration:", window.WebRTCConfig)
  }
}

const baseUrl = import.meta.env.VITE_PUBLIC_WEBRTC_BACKEND_URI

// Speech recognition setup
const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition

interface WebRTCInterfaceProps {
  org_id: string
}

export default function WebRTCInterface({ org_id }: WebRTCInterfaceProps) {
  // State management
  const [isWebRTCActive, setIsWebRTCActive] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [transcript, setTranscript] = useState("")
  const [connectionStatus, setConnectionStatus] = useState<"disconnected" | "connecting" | "connected">("disconnected")
  const [remoteAudioStream, setRemoteAudioStream] = useState<MediaStream | null>(null)
  const remoteAudioRef = useRef<HTMLAudioElement | null>(null)

  // Refs for WebRTC objects
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null)
  const dataChannelRef = useRef<RTCDataChannel | null>(null)
  const localAudioStreamRef = useRef<MediaStream | null>(null)

  // For speech recognition
  const recognitionRef = useRef<any>(null)

  // Organization logic state
  const [sessionTools, setSessionTools] = useState<any[]>([])
  const [sessionInstructions, setSessionInstructions] = useState<string>("You are a helpful assistant.")
  const apiLinksRef = useRef<any>(null)

  // Helper function to get organization-specific instructions
  function get_org_instruct(organisationId: string | null) {
    return `You are a helpful and professional AI customer support assistant of ${organisationId} whose purpose
      is to answer customer queries accurately. Respond to customers' questions as if you are customer support
      for ${organisationId} itself — use "we", "our", "here at ${organisationId}", etc. Do NOT refer to ${organisationId} in third person.
     Make sure that NO bullet points are used. Answer in paragraph form. Keep the informative responses short and concise!
      Use handle_outof_scope_cases function for all user queries, DO NOT ANSWER ANY QUERY ON YOUR OWN.
     Make sure that NO bullet points are used. Answer in paragraph form.
     Keep the informative responses short and concise!
     Before calling a function, ALWAYS tell the user to wait please.
     Your response will be read aloud using speech-to-speech. Make sure your answer is clear, natural, and easy to understand when spoken. Do not use markdown, bullet points, or special formatting. If you mention any prices or amounts, write them in words instead of numbers for better pronunciation.
     Gender Neutrality Instructions:
     - Be completely gender-neutral in all replies.
     - Do not refer to yourself or the user as male or female.
     - NEVER use any gendered verb forms (e.g., "karta hoon", "karti hoon", "samajhta hoon", "samajhti hoon", "janta hoon", "jaanti hoon", etc.). Always use gender-neutral or impersonal phrasing, such as "yeh mumkin hai", "aap ke liye kiya ja sakta hai", or other constructions that do not reveal gender.
     - Do not use "sir", "madam", "baji", "bhai", "behn", etc.
     - Use terms like "aap" or "customer", 'Muaziz Saarif (معزز صارف)' instead. Avoid all gendered titles.
     Language and Tone Instructions:
     - Use everyday conversational tone.
     - In any language other than English, keep brand names, product names, and organization names in English — do not translate them.
     - Use natural, spoken language like you're talking to a customer or friend — avoid overly formal or literal translation.
     - If user uses Urdu, reply back in usual conversational Urdu, mixing in commonly used English terms (like "account", "internet", "router", installation etc.).
     - Avoid poetic, overly technical, or formal Urdu.
     - Keep it conversational and polite.
     IMPORTANT: You MUST respond in Urdu language. DO NOT RESPOND IN ANY OTHER LANGUAGE UNDER ANY CIRCUMSTANCES!
     NEVER mention language code or language name in the response!!!`
  }

  // Get default tools including handle_outof_scope_cases
  const getDefaultTools = () => [
    {
      type: "function",
      name: "handle_outof_scope_cases",
      description:
        "Use this function as a final resort if no other function is suitable for the users query. This is the primary tool for answering general knowledge questions, open-ended inquiries, or any request that falls outside the scope of other specialized functions. It queries a comprehensive knowledge base to find an answer.",
      parameters: {
        type: "object",
        properties: {
          user_query: {
            type: "string",
            description: "The users original, complete question or topic to search for in the knowledge base.",
          },
        },
        required: ["user_query"],
      },
    }
  ]

  // Get API endpoint for a function
  const getApiEndpoint = (functionName: string) => {
    if (typeof window !== "undefined" && (window as any).apiLinks && (window as any).apiLinks[functionName]) {
      const [url, method] = (window as any).apiLinks[functionName]
      return { url, method }
    }
    return null
  }

  // Function to get authentication headers for API calls
  const getAuthHeaders = (url: string) => {
    const headers: Record<string, string> = {}
    const organisationId = localStorage.getItem("organisation_id")
    if (organisationId) {
      headers["X-Organisation-ID"] = organisationId
    }
    return headers
  }

  // Function to make API calls through backend proxy (avoids CORS issues)
  const makeProxiedApiCall = async (functionName: string, parameters: any) => {
    try {
      const organisationId = localStorage.getItem("organisation_id") || "nayatel"
      const proxyUrl = `${baseUrl}/api/function-call/${organisationId}/${functionName}`
      const requestOptions: RequestInit = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify(parameters),
      }

      const response = await fetch(proxyUrl, requestOptions)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const result = await response.json()
      return result
    } catch (error: any) {
      throw new Error(`Proxied API call failed: ${error.message}`)
    }
  }

  // Function to make direct API calls to external endpoints (fallback)
  const makeApiCall = async (url: string, method: string, parameters: any) => {
    let timeoutId: any
    try {
      const requestOptions: RequestInit = {
        method: method.toUpperCase(),
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          ...getAuthHeaders(url),
        },
      }

      const controller = new AbortController()
      const timeout = (typeof window !== "undefined" && window.WebRTCConfig?.apiTimeout) || 10000
      timeoutId = setTimeout(() => controller.abort(), timeout)
      ;(requestOptions as any).signal = controller.signal

      if (["POST", "PUT"].includes(method.toUpperCase())) {
        requestOptions.body = JSON.stringify(parameters)
      } else if (method.toUpperCase() === "GET" && parameters && Object.keys(parameters).length > 0) {
        const queryParams = new URLSearchParams()
        Object.keys(parameters).forEach((key) => {
          if (parameters[key] !== undefined && parameters[key] !== null) {
            queryParams.append(key, parameters[key])
          }
        })
        if (queryParams.toString()) {
          url += (url.includes("?") ? "&" : "?") + queryParams.toString()
        }
      }

      const response = await fetch(url, requestOptions)
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const contentType = response.headers.get("content-type")
      let result
      if (contentType && contentType.includes("application/json")) {
        result = await response.json()
      } else {
        result = await response.text()
      }
      return result
    } catch (error: any) {
      clearTimeout(timeoutId)
      if (error.name === "AbortError") {
        throw new Error("API call timed out after 10 seconds")
      } else if (error.message.includes("Failed to fetch")) {
        throw new Error("Network error: Unable to reach the API endpoint")
      } else {
        throw new Error(`API call failed: ${error.message}`)
      }
    }
  }

  // Define local functions
  const fns = {
    getPageHTML: () => {
      return {
        success: true,
        html: document.documentElement.outerHTML,
      }
    },
    changeBackgroundColor: ({ color }: { color: string }) => {
      document.body.style.backgroundColor = color
      return { success: true, color }
    },
    changeTextColor: ({ color }: { color: string }) => {
      document.body.style.color = color
      return { success: true, color }
    },
    get_order_status: ({ tracking_number }: { tracking_number: string }) => {
      const orders: Record<string, any> = {
        "263332508": {
          status: "Delivered",
          message: "Your order was delivered on May 25, 2025",
          estimated_delivery: "2025-05-25",
          location: "Front door",
          carrier: "FedEx",
          items: "Electronics package",
        },
        "503504155": {
          status: "In Transit",
          message: "Your order is on the way and arriving soon!",
          estimated_delivery: "2025-05-28",
          location: "Distribution center - Chicago",
          carrier: "UPS",
          items: "Home & Garden items",
        },
      }

      if (orders[tracking_number]) {
        return orders[tracking_number]
      }
      return {
        status: "Not Found",
        message: "Tracking number not found in our system. Please check the number and try again.",
        estimated_delivery: "Unknown",
        location: "Unknown",
        carrier: "Unknown",
        items: "Unknown",
      }
    },
    // Add more local functions as needed...
  }

  // When an audio stream is received, add it to the page and play it
  const handleTrack = (event: RTCTrackEvent) => {
    setRemoteAudioStream(event.streams[0])
  }

  // Create a data channel for transmitting control messages
  const createDataChannel = () => {
    if (!peerConnectionRef.current) return

    dataChannelRef.current = peerConnectionRef.current.createDataChannel("response")

    dataChannelRef.current.addEventListener("open", async () => {
      try {
        const urlParams = new URLSearchParams(window.location.search)
        const urlOrgId = urlParams.get("organisation_id")
        if (urlOrgId) {
          localStorage.setItem("organisation_id", urlOrgId)
        }
      } catch {}
      await configureData(org_id)
    })

    dataChannelRef.current.addEventListener("message", async (ev) => {
      const msg = JSON.parse(ev.data)

      if (msg.type === "response.function_call_arguments.done") {
        let result
        try {
          const args = JSON.parse(msg.arguments)
          const startTime = performance.now()

          if (msg.name === "handle_outof_scope_cases") {
            try {
              // Handle RAG calls
              const currentOrgId = org_id || localStorage.getItem("organisation_id") || "default"
              const payload = {
                question: args.user_query,
                history: [],
                organisation_id: currentOrgId,
                newPrompt: "",
                lead_gen: false,
                voicemessage: false,
                user_language: "en",
                provideindexname: "",
                providenamespace: "",
                agent_module_subscription: false,
                user_data: [],
                user_language_voice: null,
                inboundcall: false
              }

              const response = await fetch(`${import.meta.env.VITE_PUBLIC_BACKEND_API}/api/chat`, {
                method: "POST",
                body: JSON.stringify(payload),
                headers: {
                  "Content-Type": "application/json",
                },
              })

              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
              }
              result = await response.json()
            } catch (error: any) {
              result = { error: error.message }
            }
          } else {
            // Use the locally defined helpers
            const apiEndpoint = getApiEndpoint(msg.name)
            if (apiEndpoint) {
              try {
                result = await makeApiCall(apiEndpoint.url, apiEndpoint.method, args)
              } catch (directError: any) {
                if (
                  directError.message.includes("CORS") ||
                  directError.message.includes("Failed to fetch") ||
                  directError.message.includes("Network error")
                ) {
                  try {
                    result = await makeProxiedApiCall(msg.name, args)
                  } catch (proxyError: any) {
                    throw new Error(`API call failed: Direct (${directError.message}), Proxy (${proxyError.message})`)
                  }
                } else {
                  throw directError
                }
              }
            } else {
              const fn = fns[msg.name as keyof typeof fns]
              if (fn !== undefined) {
                result = await fn(args)
              } else {
                throw new Error(`Function ${msg.name} not found in local functions or API links`)
              }
            }
          }

          const endTime = performance.now()
          const duration = endTime - startTime
          console.log(`Function result (${duration.toFixed(2)}ms):`, result)

          const event = {
            type: "conversation.item.create",
            item: {
              type: "function_call_output",
              call_id: msg.call_id,
              output: JSON.stringify(result),
            },
          }
          dataChannelRef.current?.send(JSON.stringify(event))

          setTimeout(() => {
            const response = { type: "response.create" }
            dataChannelRef.current?.send(JSON.stringify(response))
          }, 100)
        } catch (error: any) {
          const errorEvent = {
            type: "conversation.item.create",
            item: {
              type: "function_call_output",
              call_id: msg.call_id,
              output: JSON.stringify({ error: error.message }),
            },
          }
          dataChannelRef.current?.send(JSON.stringify(errorEvent))

          setTimeout(() => {
            const response = { type: "response.create" }
            dataChannelRef.current?.send(JSON.stringify(response))
          }, 100)
        }
      }
    })
  }

  // Configure data channel functions and tools
// Configure data channel functions and tools
const configureData = async (organisationId: string | null) => {
  console.log("Configuring data channel for org:", organisationId)

  const sessionConfig = {
    modalities: ["text", "audio"],
    tools: [] as any[],
    instructions: get_org_instruct(organisationId),
    speed: 1.2, // Default speed
  }

  if (organisationId) {
    try {
      const response = await fetch(`${baseUrl}/api/session-config/${organisationId}`)
      if (response.ok) {
        const dbConfig = await response.json()
        sessionConfig.tools = dbConfig.tools || []
        sessionConfig.instructions = dbConfig.instructions || get_org_instruct(organisationId)
        
        // Preserve speed setting - use from DB if available, otherwise keep default
        sessionConfig.speed = dbConfig.speed || 1.2

        if (dbConfig.api_links) {
          console.log("API Links:", dbConfig.api_links); 
          apiLinksRef.current = dbConfig.api_links
          if (typeof window !== "undefined") {
            ;(window as any).apiLinks = dbConfig.api_links
          }
          
        } else {
          apiLinksRef.current = null
        }
      } else {
        sessionConfig.tools = getDefaultTools()
        apiLinksRef.current = null
        // Keep default speed of 1.2
      }
    } catch {
      sessionConfig.tools = getDefaultTools()
      apiLinksRef.current = null
      // Keep default speed of 1.2
    }
  } else {
    sessionConfig.tools = getDefaultTools()
    apiLinksRef.current = null
    // Keep default speed of 1.2
  }

  setSessionTools(sessionConfig.tools)
  setSessionInstructions(sessionConfig.instructions)

  const event = {
    type: "session.update",
    session: {
      modalities: sessionConfig.modalities,
      tools: sessionConfig.tools,
      tool_choice: "auto",
      instructions: sessionConfig.instructions,
      speed: sessionConfig.speed, // Ensure speed is always included
      input_audio_noise_reduction: {type: "far_field"},
      temperature: 0.8,
      turn_detection: {
        type: "server_vad",
        threshold: 0.6,
        prefix_padding_ms: 300,
        silence_duration_ms: 300,
        create_response: true
      }
    },
  }

  console.log('Sending session.update with:');
  console.log('- Tools:', event.session.tools.length);
  console.log('- Tool names:', event.session.tools.map(t => t.name));
  console.log('- Instructions:', event.session.instructions.substring(0, 100) + '...');
  console.log('- Speed:', event.session.speed); // This will help you verify the speed value

  dataChannelRef.current?.send(JSON.stringify(event))
}

  // Toggle mute functionality
  const toggleMuteHandler = () => {
    if (!localAudioStreamRef.current) return

    localAudioStreamRef.current.getAudioTracks().forEach((track) => {
      track.enabled = !track.enabled
    })
    setIsMuted(!isMuted)
    console.log(isMuted ? "Audio unmuted" : "Audio muted")
  }

  // Start speech recognition
  const startSpeechRecognition = () => {
    if (!SpeechRecognition) {
      console.warn("SpeechRecognition API not supported in this browser.")
      return
    }

    const recognition = new SpeechRecognition()
    recognition.continuous = true
    recognition.interimResults = true

    recognition.onresult = (event: any) => {
      let interimTranscript = ""
      let finalTranscript = ""

      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          finalTranscript += event.results[i][0].transcript
        } else {
          interimTranscript += event.results[i][0].transcript
        }
      }

      setTranscript(finalTranscript + interimTranscript)
      if (finalTranscript || interimTranscript) {
        console.log(`[SpeechRecognition] transcript:`, finalTranscript + interimTranscript)
      }
    }

    recognition.onerror = (event: any) => {
      console.error("SpeechRecognition error:", event.error)
    }

    recognition.onend = () => {
      setIsListening(false)
    }

    recognition.start()
    recognitionRef.current = recognition
    setIsListening(true)
  }

  // Stop speech recognition
  const stopSpeechRecognition = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop()
      recognitionRef.current = null
    }
    setIsListening(false)
    setTranscript("")
  }

  // Start WebRTC connection
  const startWebRTC = () => {
    if (isWebRTCActive) return

    setConnectionStatus("connecting")
    peerConnectionRef.current = new RTCPeerConnection()
    peerConnectionRef.current.ontrack = handleTrack

    peerConnectionRef.current.onconnectionstatechange = () => {
      const state = peerConnectionRef.current?.connectionState
      if (state === "connected") {
        setConnectionStatus("connected")
      } else if (state === "disconnected" || state === "failed") {
        setConnectionStatus("disconnected")
      }
    }

    createDataChannel()
    console.log("Starting WebRTC")

    navigator.mediaDevices
      .getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      })
      .then((stream) => {
        console.log("Got audio stream")
        localAudioStreamRef.current = stream
        stream
          .getTracks()
          .forEach((track) => peerConnectionRef.current?.addTransceiver(track, { direction: "sendrecv" }))

        peerConnectionRef.current?.createOffer().then((offer) => {
          console.log("Created offer")
          peerConnectionRef.current?.setLocalDescription(offer)

          fetch(baseUrl + "/api/rtc-connect", {
            method: "POST",
            body: offer.sdp,
            headers: {
              "Content-Type": "application/sdp",
            },
          })
            .then((r) => r.text())
            .then((answer) => {
              console.log("Got answer")
              peerConnectionRef.current?.setRemoteDescription({ sdp: answer, type: "answer" })
              setConnectionStatus("connected")
            })
            .catch(() => {
              setConnectionStatus("disconnected")
            })
        })
      })
      .catch(() => {
        setConnectionStatus("disconnected")
      })

    setIsWebRTCActive(true)
    startSpeechRecognition()
  }

  // Stop WebRTC connection
  const stopWebRTC = () => {
    if (!isWebRTCActive) return

    console.log("Stopping WebRTC")

    if (peerConnectionRef.current) {
      const tracks = peerConnectionRef.current.getReceivers().map((receiver) => receiver.track)
      tracks.forEach((track) => track?.stop())
    }

    if (dataChannelRef.current) dataChannelRef.current.close()
    if (peerConnectionRef.current) peerConnectionRef.current.close()

    peerConnectionRef.current = null
    dataChannelRef.current = null
    localAudioStreamRef.current = null
    setRemoteAudioStream(null)
    setIsWebRTCActive(false)
    setConnectionStatus("disconnected")
    stopSpeechRecognition()
  }

  // Toggle WebRTC connection
  const toggleWebRTC = () => {
    if (isWebRTCActive) {
      stopWebRTC()
    } else {
      startWebRTC()
    }
  }

  // Set srcObject for remote audio player when stream changes
  useEffect(() => {
    if (remoteAudioRef.current && remoteAudioStream) {
      remoteAudioRef.current.srcObject = remoteAudioStream
    }
  }, [remoteAudioStream])

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      stopWebRTC()
    }
  }, [])

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case "connected":
        return "bg-green-500"
      case "connecting":
        return "bg-amber-500"
      case "disconnected":
        return "bg-gray-500"
      default:
        return "bg-gray-500"
    }
  }

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case "connected":
        return "Connected"
      case "connecting":
        return "Connecting..."
      case "disconnected":
        return "Disconnected"
      default:
        return "Unknown"
    }
  }

  return (
    <div className="w-full max-w-md p-2 bg-gradient-to-br from-background via-background to-muted rounded-2xl shadow-xl flex flex-col h-full min-h-0">
      <div className="w-full">
        {/* Header */}
        <header className="border-b border-border/50 bg-card/50 backdrop-blur-sm rounded-t-lg">
          <div className="px-4 py-3 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Radio className="h-4 w-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-foreground">QuickTalk WebRTC</h1>
                <p className="text-xs text-muted-foreground">Professional Voice Communication</p>
              </div>
            </div>
            <Badge variant="secondary" className={`${getConnectionStatusColor()} text-white`}>
              <div className="w-2 h-2 bg-current rounded-full mr-2 animate-pulse" />
              {getConnectionStatusText()}
            </Badge>
          </div>
        </header>

        {/* Main Content */}
        <main className="px-4 py-4 space-y-4">
          {/* Status Card */}
          <div className="grid grid-cols-1 gap-2">
            <div className="flex items-center gap-3 p-3 bg-white/60 dark:bg-muted/40 rounded-lg shadow-sm">
              {connectionStatus === "connected" ? (
                <Wifi className="h-5 w-5 text-green-500" />
              ) : (
                <WifiOff className="h-5 w-5 text-muted-foreground" />
              )}
              <div>
                <p className="text-xs font-semibold">WebRTC</p>
                <p className="text-xs text-muted-foreground">{getConnectionStatusText()}</p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-white/60 dark:bg-muted/40 rounded-lg shadow-sm">
              {isMuted ? <MicOff className="h-5 w-5 text-destructive" /> : <Mic className="h-5 w-5 text-green-500" />}
              <div>
                <p className="text-xs font-semibold">Microphone</p>
                <p className="text-xs text-muted-foreground">{isMuted ? "Muted" : "Active"}</p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-white/60 dark:bg-muted/40 rounded-lg shadow-sm">
              {isListening ? (
                <MessageSquare className="h-5 w-5 text-primary animate-pulse" />
              ) : (
                <MessageSquare className="h-5 w-5 text-muted-foreground" />
              )}
              <div>
                <p className="text-xs font-semibold">Speech Recognition</p>
                <p className="text-xs text-muted-foreground">{isListening ? "Listening" : "Inactive"}</p>
              </div>
            </div>
            
          </div>

          {/* Control Panel */}
          <div className="flex flex-row gap-3 justify-center mt-2">
            <Button
              onClick={toggleWebRTC}
              variant={isWebRTCActive ? "destructive" : "default"}
              size="sm"
              className="flex-1 font-semibold"
            >
              {isWebRTCActive ? (
                <>
                  <PhoneOff className="mr-2 h-5 w-5" />
                  End Call
                </>
              ) : (
                <>
                  <Phone className="mr-2 h-5 w-5" />
                  Start Call
                </>
              )}
            </Button>

            <Button
              onClick={toggleMuteHandler}
              variant={isMuted ? "destructive" : "default"}
              size="sm"
              disabled={!isWebRTCActive}
              className="flex-1 font-semibold"
            >
              {isMuted ? (
                <>
                  <MicOff className="mr-2 h-5 w-5" />
                  Unmute
                </>
              ) : (
                <>
                  <Mic className="mr-2 h-5 w-5" />
                  Mute
                </>
              )}
            </Button>
          </div>


          {/* Remote Audio Player Section */}
          {remoteAudioStream && (
            <div className="bg-white/80 dark:bg-card/80 rounded-lg shadow p-3 mt-2 flex flex-col items-center">
              <span className="flex items-center justify-center gap-2 text-base font-semibold mb-1">
                <Volume2 className="h-5 w-5 text-primary" />
                Remote Audio Stream
              </span>
              <audio ref={remoteAudioRef} autoPlay controls className="w-full rounded" />
              <Button onClick={stopWebRTC} variant="destructive" size="sm" className="mt-2">
                <PhoneOff className="mr-2 h-4 w-4" />
                End Call
              </Button>
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
