import React, { useState } from "react";
import { Plus } from "lucide-react";
import { FaRobot } from "react-icons/fa";

const AudioList = () => {
  const audioFiles = [
    {
      id: 1,
      title: "English Demo (Book Recommendation)",
      duration: "1:13",
      audioSrc: "/English Audio.mp3",
      description: "This is a demo of english."
    },
    {
      id: 2,
      title: "German Demo (Greetings)",
      duration: "1:22",
      audioSrc: "/German Audio.mp3",
      description: "This is a demo of german."
    },
    {
      id: 3,
      title: "Hindi Demo (Complaint Registration)",
      duration: "1:35",
      audioSrc: "/Hindi Audio.mp3",
      description: "This is a demo of hindi."
    },
    // {
    //   id: 4,
    //   title: "Arabic Demo (Order Tracking)",
    //   duration: "1:10",
    //   audioSrc: "/Arabic Audio.mp3",
    //   description: "This is a demo of arabic."
    // },
    {
      id: 5,
      title: "Urdu Demo (Tracking Multiple Orders)",
      duration: "1:10",
      audioSrc: "/trackingorder.mp3",
      description: "This is a demo of tracking order."
    }
  ];

  // Create separate state for each card
  const [openStates, setOpenStates] = useState({});

  const toggleDescription = (id) => {
    setOpenStates(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-8 font-manrope">
      <h1 className="text-5xl font-semibold text-center mb-16">
        Handles Inbound & Outbound Calls In 90+ Languages
      </h1>

      <div className="block">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {audioFiles.map((audio) => (
            <div
              key={audio.id}
              style={{
                minHeight: openStates[audio.id] ? 'auto' : '250px',
                height: 'fit-content'
              }}
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 block"
            >
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-lg overflow-hidden bg-purple-50 flex items-center justify-center">
                  <FaRobot className="w-8 h-8 text-purple-600" />
                </div>
                <h2 className="text-xl font-semibold">{audio.title}</h2>
              </div>

              <div className="mt-6">
                <audio
                  className="w-full"
                  controls
                  src={audio.audioSrc}
                >
                  Your browser does not support the audio element.
                </audio>
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => toggleDescription(audio.id)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <Plus className="w-5 h-5 text-gray-600" />
                </button>
              </div>

              {openStates[audio.id] && (
                <div className="mt-4 p-4 bg-gray-50 rounded-xl text-sm text-gray-600">
                  <p>{audio.description}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AudioList;