"use client"

import { useState, useEffect } from "react"
import axios from "axios"
import { toast } from "react-hot-toast"
import Squareload from "./Squareload"
import WhatsAppIntegration from "./WhatsAppIntegration" 

export default function IntegrationSetup({ organ_id }) {
  const [integration, setIntegration] = useState("")
  const [links, setLinks] = useState({
    whatsapp: "",
    inbound: "",
    outbound: ""
  })
  const [showEmbedCode, setShowEmbedCode] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [subscriptions, setSubscriptions] = useState({
    whatsapp_subscriptions: false,
    inbound_call_subscriptions: false,
    outbound_call_subscriptions: false
  })

  useEffect(() => {
    const fetchInitialData = async () => {
      setIsLoading(true)
      try {
        await Promise.all([
          fetchSubscriptionPreferences(),
          fetchSavedLinks()
        ])
      } catch (error) {
        console.error("Error loading initial data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (organ_id) {
      fetchInitialData()
    } else {
      setIsLoading(false)
    }
    setIntegration("website");
    setShowEmbedCode(true);
  }, [organ_id])

  const fetchSubscriptionPreferences = async () => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get_subscription_preferences`, {
        organisation_id: organ_id
      })

      setSubscriptions({
        whatsapp_subscriptions: response.data.whatsapp_subscriptions,
        inbound_call_subscriptions: response.data.inbound_call_subscriptions,
        outbound_call_subscriptions: response.data.outbound_call_subscriptions
      })
    } catch (error) {
      console.error("Error fetching subscription preferences:", error)
      toast.error("Failed to load subscription preferences")
    }
  }

  const fetchSavedLinks = async () => {
    try {
      // Fetch WhatsApp link
      const whatsappResponse = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/encryption/readsecret`, {
        organization_id: organ_id,
        service: "whatsapp",
      })

      if (whatsappResponse.data.encrypted_data) {
        const whatsappLink = `${process.env.NEXT_PUBLIC_WHATSAPP_BASELINK}/webhook/get-metadata?data=${whatsappResponse.data.encrypted_data}`
        setLinks((prev) => ({ ...prev, whatsapp: whatsappLink }))
      }

      // Fetch Inbound link
      const inboundResponse = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/encryption/readsecret`, {
        organization_id: organ_id,
        service: "inbound_call",
      })

      if (inboundResponse.data.encrypted_data) {
        const inboundLink = `${process.env.NEXT_PUBLIC_INBOUND_BASELINK}/incoming-call?data=${inboundResponse.data.encrypted_data}`
        setLinks((prev) => ({ ...prev, inbound: inboundLink }))
      }

      // Fetch Outbound link
      // const outboundResponse = await axios.post(`${process.env.NEXT_PUBLIC_DPWRAPPER_API}/encryption/readsecret`, {
      //   organization_id: organ_id,
      //   service: "outbound_call",
      // })

      // if (outboundResponse.data.encrypted_data) {
      //   const outboundLink = `${process.env.NEXT_PUBLIC_OUTBOUND_BASELINK}/outgoing-call?data=${outboundResponse.data.encrypted_data}`
      //   setLinks((prev) => ({ ...prev, outbound: outboundLink }))
      // }
    } catch (error) {
      console.error("Error fetching saved links:", error)
    }
  }

  const handleGenerateLink = async (service) => {
    try {
      const serviceMap = {
        whatsapp: { serviceKey: "whatsapp", baseLink: process.env.NEXT_PUBLIC_WHATSAPP_BASELINK, path: "/webhook/get-metadata" },
        inbound: { serviceKey: "inbound_call", baseLink: import.meta.en.VITE_PUBLIC_INBOUND_BASELINK, path: "/incoming-call" },
        outbound: { serviceKey: "outbound_call", baseLink: process.env.NEXT_PUBLIC_OUTBOUND_BASELINK, path: "/outgoing-call" }
      }

      const selectedService = serviceMap[service]

      if (!selectedService) {
        toast.error("Invalid service selected")
        return
      }

      const encryptionResponse = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/encryption/encrypt_data`, {
        organization_id: organ_id,
        service: selectedService.serviceKey,
      })

      const encryptedData = encryptionResponse.data.encrypted_data
      const finalLink = `${selectedService.baseLink}${selectedService.path}?data=${encryptedData}`

      setLinks((prev) => ({ ...prev, [service]: finalLink }))
      toast.success(`${service.charAt(0).toUpperCase() + service.slice(1)} link generated successfully!`)
    } catch (error) {
      toast.error(error.response?.data?.error || "An error occurred while processing the request.")
    }
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    toast.success("Link copied to clipboard!")
  }

  const generateEmbedCode = () => {
    const data = localStorage.getItem(process.env.NEXT_PUBLIC_COGNITO_LAST_AUTH_USER_KEY)
    const currentDomain = window.location.hostname
    return `
  <div id="quick-talk-widget" class="fixed bottom-4 right-4 z-50">
    <button
      id="chat-toggle"
      class="bg-blue-600 hover:bg-blue-700 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110"
      onclick="toggleChat()"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
        />
      </svg>
    </button>
  </div>
  
  <script>
  function toggleChat() {
    const widget = document.getElementById('quick-talk-widget');
    const existingChat = document.getElementById('chat-iframe-container');
    
    if (existingChat) {
      widget.innerHTML = \`
        <button
          id="chat-toggle"
          class="bg-blue-600 hover:bg-blue-700 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110"
          onclick="toggleChat()"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
            />
          </svg>
        </button>
      \`;
    } else {
      widget.innerHTML = \`
        <div id="chat-iframe-container" class="relative">
          <button
            onclick="toggleChat()"
            class="absolute top-2 right-2 bg-gray-200 hover:bg-gray-300 rounded-full w-8 h-8 flex items-center justify-center text-gray-600 z-10"
          >
            ×
          </button>
          <iframe
            src="https://${currentDomain}/Chatbotpublic?username=${data}"
            class="w-[400px] h-[300px] rounded-lg shadow-lg"
            frameBorder="0"
            allow="microphone; camera; autoplay; encrypted-media"
          ></iframe>
        </div>
      \`;
    }
  }
  </script>`
  }

  // Determine if we should show the generate link button
  const shouldShowGenerateButton = (type) => {
    return integration === type && !links[type]
  }

  // Determine which links to show based on the selected integration
  const shouldShowLink = (type) => {
    return integration === type && links[type];
  }

  if (isLoading) {
    return (
      <div className="items-center justify-center min-h-[200px]">
        <Squareload />
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 font-manrope">
      <h1 className="text-2xl font-bold mb-6">Choose a source you want to integrate your bot with</h1>

      <div className="mb-6">
        <select
          className="w-full md:w-80 p-2 border border-gray-300 rounded mb-4"
          value={integration}
          onChange={(e) => {
            setIntegration(e.target.value)
            if (e.target.value === "website") {
              setShowEmbedCode(true)
            } else {
              setShowEmbedCode(false)
            }
          }}
        >
          <option value="">Select integration</option>
          {subscriptions.whatsapp_subscriptions && (
            <option value="whatsapp">WhatsApp</option>
          )}
          {subscriptions.inbound_call_subscriptions && (
            <option value="inbound">Inbound</option>
          )}
          {subscriptions.outbound_call_subscriptions && (
            <option value="outbound">Outbound</option>
          )}
          <option value="website">Website</option>
        </select>

        {/* Use our new WhatsApp Integration component with tabs */}
        {integration === "whatsapp" && (
          <WhatsAppIntegration
            organ_id={organ_id}
            links={links}
            handleGenerateLink={handleGenerateLink}
            copyToClipboard={copyToClipboard}
          />
        )}

        {/* Generate Link buttons - only visible when no link exists for the selected integration */}
        {shouldShowGenerateButton("whatsapp") && (
          <button
            onClick={() => handleGenerateLink("whatsapp")}
            className="flex bg-[#CD0ADD] text-white px-4 py-2 rounded hover:bg-purple-700 mb-4"
          >
            Generate WhatsApp Link
          </button>
        )}

        {shouldShowGenerateButton("inbound") && (
          <button
            onClick={() => handleGenerateLink("inbound")}
            className="flex bg-[#CD0ADD] text-white px-4 py-2 rounded hover:bg-purple-700 mb-4"
          >
            Generate Inbound Link
          </button>
        )}

        {shouldShowGenerateButton("outbound") && (
          <button
            onClick={() => handleGenerateLink("outbound")}
            className="flex bg-[#CD0ADD] text-white px-4 py-2 rounded hover:bg-purple-700 mb-4"
          >
            Generate Outbound Link
          </button>
        )}

        {showEmbedCode && (
          <div className="mt-6">
            <h2 className="font-semibold mb-2">Embedded code for chatbot</h2>
            <div className="bg-gray-100 p-4 rounded mb-4 max-h-[300px] overflow-auto">
              <pre className="text-[10px] whitespace-pre">
                {`<div id="quick-talk-widget" class="fixed bottom-4 right-4 z-50">
  <button
    id="chat-toggle"
    class="bg-blue-600 hover:bg-blue-700 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110"
    onclick="toggleChat()"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-6 w-6"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
      />
    </svg>
  </button>
</div>

<script>
function toggleChat() {
  const widget = document.getElementById('quick-talk-widget');
  const existingChat = document.getElementById('chat-iframe-container');
  
  if (existingChat) {
    widget.innerHTML = \`
      <button
        id="chat-toggle"
        class="bg-blue-600 hover:bg-blue-700 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110"
        onclick="toggleChat()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
          />
        </svg>
      </button>
    \`;
  } else {
    widget.innerHTML = \`
      <div id="chat-iframe-container" class="relative">
        <button
          onclick="toggleChat()"
          class="absolute top-2 right-2 bg-gray-200 hover:bg-gray-300 rounded-full w-8 h-8 flex items-center justify-center text-gray-600 z-10"
        >
          ×
        </button>
        <iframe
          src="https://${window.location.hostname}/chatbot/${localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY)}"
          class="w-[400px] h-[300px] rounded-lg shadow-lg"
          frameBorder="0"
          allow="microphone; camera; autoplay; encrypted-media"
        ></iframe>
      </div>
    \`;
  }
}
</script>`}
              </pre>
            </div>
            <button
              onClick={() => copyToClipboard(generateEmbedCode())}
              className="flex bg-[#CD0ADD] text-white px-4 py-2 rounded hover:bg-purple-700 mb-4"
            >
              Copy Code
            </button>
          </div>
        )}
      </div>

      {/* Display generated links */}
      {(shouldShowLink("whatsapp") || shouldShowLink("inbound") || shouldShowLink("outbound")) && (
        <div className="mt-8">
          {shouldShowLink("inbound") && (
            <div className="mb-4">
              <h2 className="font-semibold">Inbound</h2>
              <p className="text-sm text-black mb-2">Generated Link</p>
              <div className="flex items-center gap-4">
                <p className="text-sm text-black mb-2">{links.inbound}</p>
              </div>
              <div className="pt-8 pb-8">
                <button
                  onClick={() => copyToClipboard(links.inbound)}
                  className="flex bg-[#CD0ADD] text-white px-4 py-2 rounded hover:bg-purple-700 mb-4"
                >
                  Copy Link
                </button>
              </div>
            </div>
          )}

          {shouldShowLink("outbound") && (
            <div className="mb-4">
              <h2 className="font-semibold">Outbound</h2>
              <p className="text-sm text-black mb-2">Generated Link</p>
              <div className="flex items-center gap-4">
                <p className="text-sm text-black mb-2">{links.outbound}</p>
              </div>
              <div className="pt-8 pb-8">
                <button
                  onClick={() => copyToClipboard(links.outbound)}
                  className="flex bg-[#CD0ADD] text-white px-4 py-2 rounded hover:bg-purple-700 mb-4"
                >
                  Copy Link
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}