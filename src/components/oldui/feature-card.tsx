import Image from 'next/image';
import { Card, CardBody, Typography } from "@material-tailwind/react";

interface FeatureCardProps {
  title: string;
  description: string;
  imageSrc: string;
  imagePosition: "left" | "right";
  className?: string;
}

const FeatureCard = ({
  title,
  description,
  imageSrc,
  imagePosition,
  className,
}: FeatureCardProps) => {
  return (
    <Card className={` border shadow-sm flex flex-col md:flex-row items-center max-w-full mx-auto shadow-md ${className}`}>
      <CardBody className={`  flex-1 order-2 ${imagePosition === "left" ? "md:order-2" : "md:order-1"}`}>
        <div
          className={`${imagePosition === "right" ? "md:pl-28" : ""
            }`}
        >
          <Typography variant="h4" color="black" className="font-manrope mb-2">
            {title}
          </Typography>
          <Typography
            color="black"
            className="font-normal font-manrope break-words max-w-full md:max-w-md"
          >
            {description}
          </Typography>
        </div>
      </CardBody>
      {imagePosition === "left" && (
        <div className="flex-1 order-1 md:order-1">
          <div className="mx-auto max-w-[400px] aspect-square overflow-hidden">
            <div className="flex h-full items-center justify-center">
              <Image
                src={imageSrc}
                alt={title}
                width={400}
                height={400}
                className="object-cover"
              />
            </div>
          </div>
        </div>
      )}
      {imagePosition === "right" && (
        <div className="flex-1 order-1 md:order-2 ">
          <div className="mx-auto max-w-[400px] aspect-square overflow-hidden">
            <div className="flex h-full items-center justify-center">
              <Image
                src={imageSrc}
                alt={title}
                width={400}
                height={400}
                className="object-cover"
              />
            </div>
          </div>
        </div>
      )}
    </Card>
  );
}

export default FeatureCard;
