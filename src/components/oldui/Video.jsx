import React from 'react';
import { motion } from 'framer-motion';

// Example of custom animation configuration
const rightSideAnimate = {
  initial: { opacity: 0, x: 200 }, // Start off-screen to the right
  whileInView: { opacity: 1, x: 0 }, // Move to the normal position with full opacity
};

export default function Component() {
  const videos = [
    { id: 1, src: '/Hindi Audio.mp4', description: 'AI Agent: Easy resolution of complaints! Resolve late delivery issues instantly with tracking ID assistance.' },
    { id: 2, src: '/German Audio.mp4', description: 'AI Agent: Easy resolution of complaints! Resolve late delivery issues instantly with tracking ID assistance.' },
    { id: 3, src: '/tracking Hindi.mp4', description: 'AI Agent: Intelligent solution to your concerns! Apologies for the first two incorrect tracking IDs, instant response on the correct tracking ID: Delivered!' },
    { id: 4, src: '/English Audio.mp4', description: 'AI Agent: Intelligent solution to your concerns! Apologies for the first two incorrect tracking IDs, instant response on the correct tracking ID: Delivered!' },
  ];

  return (
    <motion.div
      initial={rightSideAnimate.initial} // Use the custom initial animation
      whileInView={rightSideAnimate.whileInView} // Trigger animation when in view
      transition={{ duration: 1, ease: 'easeOut' }} // Duration and easing function
    >
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-6 pb-6 lg:pt-24 pb-20">
          {videos.map((video) => (
            <div key={video.id} className="flex flex-col justify-center items-center">
              {video.src.endsWith('.mp4') ? (
                <video
                  src={video.src}
                  controls
                  className="h-[300px] md:h-[550px] object-cover rounded-lg shadow-md"
                >
                  Your browser does not support the video tag.
                </video>
              ) : (
                <img
                  src={video.src}
                  alt={`Video ${video.id}`}
                  className="h-[300px] md:h-[550px] object-cover rounded-lg shadow-md"
                />
              )}
              <p className="mt-4 text-xl text-center text-gray-700">{video.description}</p>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
}
