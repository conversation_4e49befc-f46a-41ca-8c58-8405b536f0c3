import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>eader,
  DialogBody,
  DialogFooter,
  Button,
  Input,
  Textarea,
  Checkbox,
} from "@material-tailwind/react"
import { ClipLoader } from "react-spinners"

const EditModal = ({ isOpen, onClose, data, onSave }) => {
  const [editedData, setEditedData] = useState({
    function: {},
    attribute: {},
  })

  const [editMode, setEditMode] = useState("function")
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen && data) {
      setEditedData({
        function: data.function || {},
        attribute: {
          ...data.attribute,
          attributeRequired: data.attribute?.attributeRequired ?? false,
        },
      })
      setEditMode(data.attribute ? "attribute" : "function")
    }
  }, [isOpen, data])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
  
    if (type === "checkbox") {
      // Handle checkbox change (like "attributeRequired")
      setEditedData((prevData) => ({
        ...prevData,
        [editMode]: {
          ...prevData[editMode],
          [name]: checked,  // directly use `checked` here to handle both checked and unchecked
        },
      }));
    } else {
      // Handle text input change (simple input or textarea)
      setEditedData((prevData) => ({
        ...prevData,
        [editMode]: {
          ...prevData[editMode],
          [name]: value,
        },
      }));
    }
  };
  
  
  


  const handleSave = async () => {
    setLoading(true)
    try {
      await onSave(editedData)
      onClose()
    } catch (error) {
      console.error("Error saving data:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} handler={onClose} size="sm">
      <DialogHeader>{editMode === "function" ? "Edit Function" : "Edit Attribute"}</DialogHeader>
      <DialogBody divider className="h-[20rem] flex flex-col w-full gap-6">
        {editMode === "function" ? (
          <>
            <Input
              label="Function Name"
              name="functionName"
              value={editedData.function.functionName || ""}
              onChange={handleChange}
              className="mb-4"
            />
            <Textarea
              label="Function Description"
              name="functionDefinition"
              value={editedData.function.functionDefinition || ""}
              onChange={handleChange}
              className="mb-4"
            />
            <Input
              label="API Link"
              name="functionapilink"
              value={editedData.function.functionapilink || ""}
              onChange={handleChange}
              className="mb-4"
            />
          </>
        ) : (
          <>
            <Input
              label="Attribute Name"
              name="attributeName"
              value={editedData.attribute.attributeName || ""}
              onChange={handleChange}
              className="mb-4"
            />
            <Textarea
              label="Attribute Description"
              name="attributeDescription"
              value={editedData.attribute.attributeDescription || ""}
              onChange={handleChange}
              className="mb-4"
            />
            <Checkbox
              label="Required"
              name="required"
              checked={editedData.attribute.required}
              onChange={handleChange}
            />
          </>
        )}
      </DialogBody>
      <DialogFooter>
        <Button variant="text" color="red" onClick={onClose} className="mr-1 bg-[#CD0ADD] text-white hover:bg-purple-700">
          <span>Cancel</span>
        </Button>
        <Button variant="text"  onClick={handleSave} className="bg-[#CD0ADD] text-white hover:bg-purple-700" disabled={loading}>
          {loading ? <ClipLoader size={20} color="white" loading={loading} /> : <span>Save</span>}
        </Button>
      </DialogFooter>
    </Dialog>
  )
}

export default EditModal

