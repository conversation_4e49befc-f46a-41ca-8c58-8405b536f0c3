"use client"

import { But<PERSON> } from "@material-tailwind/react"
import Link from "next/link"

export default function Banner() {
  return (
    <div className="relative min-h-[80vh] bg-[#003333] px-4 py-16 font-manrope">
      <div className="pt-24 mx-auto max-w-6xl text-center">
        <h1 className="mb-6 text-4xl font-bold text-white md:text-5xl lg:text-6xl">
          SaaS Based,{" "}
          <span className="text-white">Human-less,</span>
          <br />
          Omnichannel Contact Center
        </h1>

        <p className="mx-auto mb-8 max-w-3xl text-center text-gray-300">
        QuickTalk is the world&apos;s first all-in-one AI customer service solution, offering seamless integration across every channel, multilingual support, automated responses, and actionable insights.
        </p>

        <div className="mb-12 flex flex-wrap items-center justify-center gap-4 ">
          <Link href='/Pricing'>
          <Button
            variant="outlined"
            className="font-manrope min-w-[140px] border-[#CD0ADD] text-[#FF00FF] hover:bg-[#FF00FF] hover:text-white"
          >
            View Pricing
          </Button>
          </Link>
          <Link href="/demo">
          <Button
            className="font-manrope min-w-[140px] bg-[#CD0ADD] text-white hover:bg-[#FF00FF]/90"
          >
            Book A Free Demo
          </Button>
          </Link>
        </div>

        <div className="mx-auto aspect-video max-w-6xl rounded-3xl p-4">
          <div className="relative w-full h-full rounded-3xl overflow-hidden">
            <video
              className="w-full h-full object-cover"
              controls
              src="/customer service.mp4"
              autoPlay
              muted
              loop
              playsInline
            >
              Your browser does not support the video tag.
            </video>
          </div>
        </div>

      </div>
    </div>
  )
}
