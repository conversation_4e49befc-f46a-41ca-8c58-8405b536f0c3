import React, { useEffect, useRef, useImperativeHandle, forwardRef, useState } from 'react';

const TableauEmbed = forwardRef(({ url, options, token, onError }, ref) => {
  const vizRef = useRef(null);
  const vizContainerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  console.log("token recived",token)

  useEffect(() => {
    // Load Tableau Embedding API v3 script
    const loadTableauScript = () => {
      return new Promise((resolve, reject) => {
        // Check if script is already loaded
        if (typeof window !== 'undefined' && window.customElements && window.customElements.get('tableau-viz')) {
          resolve();
          return;
        }

        // Check if script is already being loaded
        const existingScript = document.querySelector(`script[src*="tableau.embedding"]`);
        if (existingScript) {
          existingScript.addEventListener('load', () => {
            // Wait for custom element to be defined
            const checkCustomElement = setInterval(() => {
              if (window.customElements && window.customElements.get('tableau-viz')) {
                clearInterval(checkCustomElement);
                resolve();
              }
            }, 100);
            
            setTimeout(() => {
              clearInterval(checkCustomElement);
              resolve(); // Proceed even if custom element check times out
            }, 5000);
          });
          existingScript.addEventListener('error', reject);
          return;
        }

        const script = document.createElement('script');
        script.src = 'https://us-east-1.online.tableau.com/javascripts/api/tableau.embedding.3.latest.min.js';
        script.type = 'module';
        script.crossOrigin = 'anonymous';
        
        script.onload = () => {
          console.log('Tableau script loaded successfully');
          // Wait for custom element to be defined
          let attempts = 0;
          const maxAttempts = 50;
          
          const checkCustomElement = setInterval(() => {
            attempts++;
            if (window.customElements && window.customElements.get('tableau-viz')) {
              clearInterval(checkCustomElement);
              console.log('tableau-viz custom element is ready');
              resolve();
            } else if (attempts >= maxAttempts) {
              clearInterval(checkCustomElement);
              console.log('Custom element check timed out, proceeding anyway');
              resolve(); // Proceed anyway, might still work
            }
          }, 100);
        };
        
        script.onerror = (error) => {
          console.error('Script loading error:', error);
          reject(new Error('Failed to load Tableau API'));
        };
        
        document.head.appendChild(script);
      });
    };

    const initializeViz = async () => {
      if (vizRef.current || !url || !token) {
        console.log('Skipping initialization:', { 
          hasViz: !!vizRef.current, 
          hasUrl: !!url, 
          hasToken: !!token 
        });
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        console.log('Starting Tableau initialization...');
        
        await loadTableauScript();
        console.log('Script loaded, creating viz element...');
        
        // Create the tableau-viz element
        const viz = document.createElement('tableau-viz');
        viz.setAttribute('src', url);
        viz.setAttribute('token', token);
        
        // Set additional attributes
        if (options?.toolbar) {
          viz.setAttribute('toolbar', options.toolbar);
        }
        
        if (options?.hideTabs) {
          viz.setAttribute('hide-tabs', '');
        }
        
        if (options?.device) {
          viz.setAttribute('device', options.device);
        }
        
        // Set dimensions
        viz.setAttribute('width', options?.width || '100%');
        viz.setAttribute('height', options?.height || '100%');

        // Set authentication mode
        viz.setAttribute('authentication-mode', 'token');

        // Add event listeners
        viz.addEventListener('firstInteractive', (e) => {
          console.log('Tableau viz loaded successfully:', e);
          setIsLoading(false);
        });

        viz.addEventListener('vizLoadError', (e) => {
          console.error('Viz load error:', e);
          setError('Failed to load Tableau visualization');
          setIsLoading(false);
          if (onError) {
            onError(e);
          }
        });

        if (vizContainerRef.current) {
          // Clear any existing content
          vizContainerRef.current.innerHTML = '';
          vizContainerRef.current.appendChild(viz);
          vizRef.current = viz;
          console.log('Viz element added to container');
          
          // Set loading to false after a timeout if no events fire
          setTimeout(() => {
            if (isLoading) {
              console.log('Setting loading to false after timeout');
              setIsLoading(false);
            }
          }, 10000);
        }

      } catch (error) {
        console.error('Error initializing Tableau visualization:', error);
        setError(error.message);
        setIsLoading(false);
        if (onError) {
          onError(error);
        }
      }
    };

    initializeViz();

    return () => {
      if (vizRef.current && vizContainerRef.current) {
        try {
          vizContainerRef.current.removeChild(vizRef.current);
        } catch (e) {
          console.warn('Could not remove viz element:', e);
        }
        vizRef.current = null;
      }
    };
  }, [url, token, options, onError]);

  useImperativeHandle(ref, () => ({
    refreshViz: async () => {
      console.log('Refreshing viz...');
      if (vizRef.current && vizContainerRef.current) {
        try {
          vizContainerRef.current.removeChild(vizRef.current);
          vizRef.current = null;
          await initializeViz();
        } catch (error) {
          console.error('Error refreshing viz:', error);
          if (onError) {
            onError(error);
          }
        }
      }
    },
    getViz: () => vizRef.current
  }));

  if (error) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center p-8">
          <h3 className="text-lg font-semibold text-red-600 mb-2">
            Failed to Load Tableau Visualization
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => {
              setError(null);
              setIsLoading(true);
              if (vizRef.current) {
                vizRef.current = null;
              }
            }} 
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-screen relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading Tableau visualization...</p>
          </div>
        </div>
      )}
      <div
        ref={vizContainerRef}
        className="w-full h-full"
        style={{ 
          minHeight: '600px',
          backgroundColor: isLoading ? '#f8f9fa' : 'transparent'
        }}
      />
    </div>
  );
});

TableauEmbed.displayName = 'TableauEmbed';

export default TableauEmbed;