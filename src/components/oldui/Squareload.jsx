"use client"

import { useEffect } from "react"

export default function Squareload() {
  useEffect(() => {
    // Add the keyframe animations to the document
    const style = document.createElement("style")
    style.textContent = `
      @keyframes loader {
        0% { transform: rotate(0deg); }
        25% { transform: rotate(180deg); }
        50% { transform: rotate(180deg); }
        75% { transform: rotate(360deg); }
        100% { transform: rotate(360deg); }
      }
      @keyframes loader-inner {
        0% { height: 0%; }
        25% { height: 0%; }
        50% { height: 100%; }
        75% { height: 100%; }
        100% { height: 0%; }
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  return (
    <div className="h-screen flex items-center justify-center">
      <span className="inline-block w-[30px] h-[30px] relative border-4 border-[#CD0ADD] animate-[loader_2s_infinite_ease]">
        <span className="inline-block w-full bg-[#CD0ADD] animate-[loader-inner_2s_infinite_ease-in]"></span>
      </span>
    </div>
  )
}

