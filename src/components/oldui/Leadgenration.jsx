"use client"

import { useState, useEffect } from "react"
import { toast } from "react-hot-toast"
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format } from 'date-fns';
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from "../ui/alert-dialog";
import FunctionSettingsCheck from "../Dashboard/function/FunctionSetting";
const LeadGeneration = ({ orgid, updateAgentModuleStatus, isAgentModuleActive, onAgentModuleStatusChange }) => {
  const [isEnabled, setIsEnabled] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState(null)

  // State for Lead List
  const [leads, setLeads] = useState([])
  const [originalLeads, setOriginalLeads] = useState([])
  const [leadListLoading, setLeadListLoading] = useState(true)
  const [leadListError, setLeadListError] = useState(null)

  // State for filter dropdown
  const [isFilterDropdownOpen, setIsFilterDropdownOpen] = useState(false)

  // State for Lead List section visibility
  const [isLeadListVisible, setIsLeadListVisible] = useState(false)

  // State for date filter
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState(null)

  const API_URL = `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/settings/botOptions/get_update`
  const LEAD_LIST_API_URL = `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/leads`
  const AGENT_MODULE_API_URL = `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/agnet_module/subscription-status`
  const TICKET_API_URL = `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get/update/ticket_generation`

  // State for Agent Module Toggle
  const [isAgentEnabled, setIsAgentEnabled] = useState(false)
  const [isAgentLoading, setIsAgentLoading] = useState(true)
  const [isAgentUpdating, setIsAgentUpdating] = useState(false)
  const [agentError, setAgentError] = useState(null)

  // State for Ticket Generation Toggle
  const [isTicketEnabled, setIsTicketEnabled] = useState(false);
  const [isTicketLoading, setIsTicketLoading] = useState(true);
  const [isTicketUpdating, setIsTicketUpdating] = useState(false);
  const [ticketError, setTicketError] = useState(null);

  // Dialog state
  const [dialogOpen, setDialogOpen] = useState(false);
  const [pendingToggle, setPendingToggle] = useState(null); // 'lead' or 'agent' or 'ticket'
  const [pendingValue, setPendingValue] = useState(false);

  // Add new state for ticket function visibility and existence
  const [isTicketFunctionVisible, setIsTicketFunctionVisible] = useState(false);
  const [ticketFunctionExists, setTicketFunctionExists] = useState(false);
  const [ticketFunctionLoading, setTicketFunctionLoading] = useState(false);

  useEffect(() => {
    fetchToggleState()
  }, [orgid])

  // Fetch Lead List data
  useEffect(() => {
    const fetchLeads = async () => {
      try {
        setLeadListLoading(true)
        setLeadListError(null)
        const response = await fetch(LEAD_LIST_API_URL, {
          method: "POST", // Assuming POST based on the toggle API
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ organisation_id: orgid }), // Assuming orgid is needed for fetching leads
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch lead list: ${response.status}`)
        }

        const data = await response.json()
        console.log("Fetched leads:", data)
        setOriginalLeads(data) // Store original data
        console.log("Original leads after fetch:", data) // Log original leads
        sortLeads('desc') // Sort by most recent initially
        toast.success("Lead list loaded successfully")
      } catch (err) {
        console.error("Error fetching lead list:", err)
        setLeadListError("Failed to load lead list")
        toast.error("Failed to load lead list")
      } finally {
        setLeadListLoading(false)
      }
    }

    if (orgid) { // Fetch leads only if orgid is available
      fetchLeads()
    }
  }, [orgid])

  // Effect to show data when component becomes visible and leads is empty but originalLeads has data
  useEffect(() => {
    if (isLeadListVisible && leads.length === 0 && originalLeads.length > 0 && !selectedDate) {
      sortLeads('desc') // Restore original leads and sort by most recent
      console.log("Restoring leads from originalLeads on visibility  change.")
    }
  }, [isLeadListVisible, leads.length, originalLeads.length, selectedDate])

  // Fetch Agent Module subscription status
  useEffect(() => {
    const fetchAgentStatus = async () => {
      try {
        setIsAgentLoading(true)
        setAgentError(null)
        const response = await fetch(AGENT_MODULE_API_URL, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ organization_id: orgid }),
        })
        if (!response.ok) throw new Error(`Failed to fetch agent module status: ${response.status}`)
        const data = await response.json()
        setIsAgentEnabled(Boolean(data.agent_module_subscription))
        toast.success("Agent module status loaded successfully")
        if (onAgentModuleStatusChange) onAgentModuleStatusChange();
      } catch (err) {
        setAgentError("Failed to load agent module status")
        toast.error("Failed to load agent module status")
      } finally {
        setIsAgentLoading(false)
      }
    }
    if (orgid) fetchAgentStatus()
  }, [orgid, onAgentModuleStatusChange])

  // Fetch Ticket Generation status
  useEffect(() => {
    const fetchTicketStatus = async () => {
      try {
        setIsTicketLoading(true);
        setTicketError(null);
        const response = await fetch(TICKET_API_URL, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ organization_id: orgid }),
        });
        if (!response.ok) throw new Error(`Failed to fetch ticket status: ${response.status}`);
        const data = await response.json();
        setIsTicketEnabled(Boolean(data.
          ticket_generation_value));
        toast.success("Ticket status loaded successfully");
      } catch (err) {
        setTicketError("Failed to load ticket status");
        toast.error("Failed to load ticket status");
      } finally {
        setIsTicketLoading(false);
      }
    };
    if (orgid) fetchTicketStatus();
  }, [orgid]);

  // Fetch ticket generation function existence
  const fetchTicketFunctionExists = async () => {
    if (!orgid) return;
    setTicketFunctionLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/taskmanager/function_setting/get`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ organisationId: orgid })
      });
      if (!response.ok) throw new Error('Failed to fetch function data');
      const data = await response.json();
      const functions = data.functions || [];
      // Check if any function is 'generate_ticket'
      const exists = functions.some(f => (f.functionName || '').toLowerCase() === 'generate_ticket');
      setTicketFunctionExists(exists);
    } catch (err) {
      setTicketFunctionExists(false);
    } finally {
      setTicketFunctionLoading(false);
    }
  };

  // Fetch on orgid or ticket enabled change
  useEffect(() => {
    if (isTicketEnabled && orgid) {
      fetchTicketFunctionExists();
    }
  }, [isTicketEnabled, orgid]);

  const fetchToggleState = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ organisation_id: orgid }),
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch toggle state: ${response.status}`)
      }

      const data = await response.json()
      console.log("Fetched data:", data)

      // Handle the array response format
      const enabledValue = Array.isArray(data.lead_generation) ? data.lead_generation[0] : data.lead_generation
      const admin_email = Array.isArray(data.admin_email) ? data.admin_email[0] : data.admin_email
      setIsEnabled(Boolean(enabledValue))
      toast.success("Settings loaded successfully")
    } catch (err) {
      console.error("Error fetching toggle state:", err)
      setError("Failed to load settings")
      toast.error("Failed to load settings")
    } finally {
      setIsLoading(false)
    }
  }

  // Show dialog instead of toggling immediately
  const handleLeadDialog = () => {
    setPendingToggle('lead');
    setPendingValue(!isEnabled);
    setDialogOpen(true);
  };
  const handleAgentDialog = () => {
    setPendingToggle('agent');
    setPendingValue(!isAgentEnabled);
    setDialogOpen(true);
  };
  const handleTicketDialog = () => {
    setPendingToggle('ticket');
    setPendingValue(!isTicketEnabled);
    setDialogOpen(true);
  };

  // Confirmed toggle handler
  const handleConfirmToggle = async () => {
    setDialogOpen(false);
    if (pendingToggle === 'lead') {
      try {
        const email = localStorage.getItem("userEmail");
        setIsUpdating(true);
        setError(null);
        setIsEnabled(pendingValue);
        const response = await fetch(API_URL, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            update_option: String(pendingValue),
            organisation_id: orgid,
            admin_email: email,
          }),
        });
        if (!response.ok) {
          setIsEnabled(!pendingValue);
          throw new Error(`Failed to update toggle state: ${response.status}`);
        }
        toast.success(`Lead Generation ${pendingValue ? "enabled" : "disabled"} successfully`);
      } catch (err) {
        setError("Failed to save settings");
        toast.error("Failed to save settings");
      } finally {
        setIsUpdating(false);
      }
    } else if (pendingToggle === 'agent') {
      try {
        setIsAgentUpdating(true);
        setAgentError(null);
        setIsAgentEnabled(pendingValue);
        const response = await fetch(AGENT_MODULE_API_URL, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            organization_id: orgid,
            subscription_status: pendingValue,
          }),
        });
        if (!response.ok) {
          setIsAgentEnabled(!pendingValue);
          throw new Error(`Failed to update agent module status: ${response.status}`);
        }
        toast.success(`Transfer to Agent ${pendingValue ? "enabled" : "disabled"} successfully`);
        if (onAgentModuleStatusChange) onAgentModuleStatusChange();
      } catch (err) {
        setAgentError("Failed to save agent module status");
        toast.error("Failed to save agent module status");
      } finally {
        setIsAgentUpdating(false);
      }
    } else if (pendingToggle === 'ticket') {
      try {
        setIsTicketUpdating(true);
        setTicketError(null);
        setIsTicketEnabled(pendingValue);
        const response = await fetch(TICKET_API_URL, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            organization_id: orgid,
            ticket_generation: pendingValue,
          }),
        });
        if (!response.ok) {
          setIsTicketEnabled(!pendingValue);
          throw new Error(`Failed to update ticket status: ${response.status}`);
        }
        toast.success(`Ticket Generation ${pendingValue ? "enabled" : "disabled"} successfully`);
      } catch (err) {
        setTicketError("Failed to save ticket status");
        toast.error("Failed to save ticket status");
      } finally {
        setIsTicketUpdating(false);
      }
    }
    setPendingToggle(null);
  };

  // Handle date selection from DatePicker
  const handleDateChange = (date) => {
    setSelectedDate(date)
    setIsDatePickerOpen(false) // Close date picker after selection
    filterLeadsByDate(date)
  }

  // Client-side sorting
  const sortLeads = (order) => {
    const sorted = [...originalLeads].sort((a, b) => {
      const dateA = new Date(a.lead_generated_at)
      const dateB = new Date(b.lead_generated_at)
      console.log(`Sorting: Date A: ${a.lead_generated_at} -> ${dateA}, Date B: ${b.lead_generated_at} -> ${dateB}`)
      if (order === 'asc') {
        return dateA - dateB
      } else {
        return dateB - dateA
      }
    })
    setLeads(sorted)
  }

  // Client-side filtering by date
  const filterLeadsByDate = (date) => {
    if (!date) {
      setLeads(originalLeads) // If no date selected, show all leads
      return
    }
    const filtered = originalLeads.filter(lead => {
      const leadDate = new Date(lead.lead_generated_at)
      console.log(`Filtering: Lead Date: ${lead.lead_generated_at} -> ${leadDate}, Selected Date: ${date}`)
      // Compare by year, month, and day
      return leadDate.getFullYear() === date.getFullYear() &&
        leadDate.getMonth() === date.getMonth() &&
        leadDate.getDate() === date.getDate()
    })
    setLeads(filtered)
  }

  return (
    <div className="p-4 w-full">
      <h2 className="text-lg font-normal mb-4">Choose a task for your bot</h2>

      <div className="flex flex-col gap-4">
        {/* Lead Generation Card */}
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm w-full overflow-hidden">
          {/* Card Header with Toggle */}
          <div className="flex flex-col p-6 border-b border-gray-200">
            <div className="flex justify-between items-center mb-4">
              {isLoading ? (
                <div className="flex items-center justify-center w-full">
                  <div className="w-6 h-6 border-4 border-gray-300 border-t-[#CD0ADD] rounded-full animate-spin"></div>
                </div>
              ) : (
                <>
                  <span className="text-black text-lg">Lead Generation</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={isEnabled}
                      onChange={handleLeadDialog}
                      className="sr-only peer"
                      disabled={isUpdating}
                    />
                    <div className="w-14 h-7 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:bg-[#CD0ADD] after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:after:translate-x-7 flex items-center justify-center">
                      {isUpdating && (
                        <div className="absolute w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      )}
                    </div>
                  </label>
                </>
              )}
            </div>
            {/* Lead List Button below heading */}
            {isEnabled && (
              <button
                className={`text-[#CD0ADD] text-lg font-semibold flex items-center focus:outline-none hover:text-[#a800b8] transition-colors duration-200`}
                onClick={() => setIsLeadListVisible(!isLeadListVisible)}
              >
                Lead list
                <svg
                  className={`ml-1 w-4 h-4 transform transition-transform ${isLeadListVisible ? 'rotate-180' : 'rotate-0'}`}
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>

          {/* Lead List Content */}
          {isEnabled && isLeadListVisible && (
            <div className="p-6">
              <div className="flex justify-end mb-4">
                {/* Filter Dropdown */}
                <div className="relative">
                  <button
                    className="text-gray-600 flex items-center"
                    onClick={() => setIsFilterDropdownOpen(!isFilterDropdownOpen)}
                  >
                    Filter by <span className="ml-1">&#9776;</span>
                  </button>
                  {isFilterDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                      <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                        <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" onClick={() => {
                          sortLeads('desc')
                          setIsFilterDropdownOpen(false)
                        }}>
                          Recent to oldest
                        </button>
                        <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" onClick={() => {
                          sortLeads('asc')
                          setIsFilterDropdownOpen(false)
                        }}>
                          Oldest to recent
                        </button>
                        <button
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          role="menuitem"
                          onClick={() => {
                            setIsDatePickerOpen(true)
                            setIsFilterDropdownOpen(false) // Close filter dropdown when date filter is selected
                          }}
                        >
                          Filter by date
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Date Picker */}
                  <div className="absolute right-0 mt-2 p-4 bg-white rounded-md shadow-lg z-20" style={{ display: isDatePickerOpen ? 'block' : 'none' }}>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker
                        label="Select Date"
                        value={selectedDate}
                        onChange={handleDateChange}
                        open={isDatePickerOpen} // Control open state
                        onClose={() => setIsDatePickerOpen(false)} // Close when dismissed
                        slotProps={{
                          textField: { InputProps: { readOnly: true } }, // Make the input read-only
                        }}
                      // You might need to further customize to hide the input completely
                      />
                    </LocalizationProvider>
                  </div>
                  {/* Clear Date Filter button */}
                  {selectedDate && (
                    <button
                      className="ml-2 text-sm text-red-600 hover:text-red-800 focus:outline-none"
                      onClick={() => {
                        setSelectedDate(null) // Clear selected date
                        sortLeads('desc') // Restore original leads and sort by most recent
                        setIsDatePickerOpen(false) // Close date picker if open
                        console.log("Original leads after clearing filter:", originalLeads) // Log original leads on clear
                      }}
                    >
                      Clear filter
                    </button>
                  )}
                </div>
              </div>

              {/* Lead List Table */}
              {leadListLoading ? (
                <div className="flex items-center justify-center w-full p-8">
                  <div className="w-6 h-6 border-4 border-gray-300 border-t-[#CD0ADD] rounded-full animate-spin"></div>
                </div>
              ) : leadListError ? (
                <div className="text-red-500 text-sm">{leadListError}</div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-[#CD0ADD] text-white">
                      <tr>
                        <th scope="col" className="w-1/4 px-6 py-3 text-left text-[16px] font-bold capitalize tracking-wider">
                          Lead Name
                        </th>
                        <th scope="col" className="w-1/4 px-6 py-3 text-left text-[16px] font-bold capitalize tracking-wider">
                          Contact Info
                        </th>
                        <th
                          scope="col"
                          className="w-1/4 px-6 py-3 text-left text-[16px] font-bold capitalize tracking-wider"
                        >
                          Date & Time
                        </th>
                        <th scope="col" className="w-1/4 px-6 py-3 text-left text-[16px] font-bold capitalize tracking-wider">
                          Chat Summary
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200 text-black">
                      {leads.map((lead, index) => (
                        <tr key={index}>
                          <td className="w-1/4 px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {lead.user_name ? lead.user_name : '---'}
                          </td>
                          <td className="w-1/4 px-6 py-4 whitespace-nowrap text-sm text-black">
                            {/* Display contact number and email address with line break */}
                            <div>
                              {lead.contact_number ? lead.contact_number : '---'}
                              <br />
                              {lead.email_address ? lead.email_address : '---'}
                            </div>
                          </td>
                          <td className="w-1/4 px-6 py-4 whitespace-nowrap text-sm text-black">
                            {lead.lead_generated_at ? format(new Date(lead.lead_generated_at), 'MM/dd/yyyy hh:mm a') : ''} {/* Format date and time */}
                          </td>
                          <td className="w-1/4 px-6 py-4 text-sm font-medium break-words">
                            {lead.chat_summary ? lead.chat_summary : '---'} {/* Display chat_summary */}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
          {error && <div className="px-6 pb-4 text-red-500 text-sm">{error}</div>}
        </div>

        {/* Transfer to Agent Card */}
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm w-full">
          <div className="flex justify-between items-center p-6">
            {isAgentLoading ? (
              <div className="flex items-center justify-center w-full">
                <div className="w-6 h-6 border-4 border-gray-300 border-t-[#CD0ADD] rounded-full animate-spin"></div>
              </div>
            ) : (
              <>
                <span className="text-black text-lg">Transfer to Agent</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={isAgentEnabled}
                    onChange={handleAgentDialog}
                    className="sr-only peer"
                    disabled={isAgentUpdating}
                  />
                  <div className="w-14 h-7 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:bg-[#CD0ADD] after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:after:translate-x-7 flex items-center justify-center">
                    {isAgentUpdating && (
                      <div className="absolute w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    )}
                  </div>
                </label>
              </>
            )}
          </div>
          {agentError && <div className="px-6 pb-4 text-red-500 text-sm">{agentError}</div>}
        </div>

        {/* Ticket Generation Card */}
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm w-full overflow-hidden">
          <div className="flex flex-col p-6 border-b border-gray-200">
            <div className="flex justify-between items-center mb-4">
              {isTicketLoading ? (
                <div className="flex items-center justify-center w-full">
                  <div className="w-6 h-6 border-4 border-gray-300 border-t-[#CD0ADD] rounded-full animate-spin"></div>
                </div>
              ) : (
                <>
                  <span className="text-black text-lg">Ticket Generation</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={isTicketEnabled}
                      onChange={handleTicketDialog}
                      className="sr-only peer"
                      disabled={isTicketUpdating}
                    />
                    <div className="w-14 h-7 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:bg-[#CD0ADD] after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:after:translate-x-7 flex items-center justify-center">
                      {isTicketUpdating && (
                        <div className="absolute w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      )}
                    </div>
                  </label>
                </>
              )}
            </div>
            {/* Add/View Function Button below heading */}
            {isTicketEnabled && !ticketFunctionLoading && (
              <button
                className={`text-[#CD0ADD] text-lg font-semibold flex items-center focus:outline-none hover:text-[#a800b8] transition-colors duration-200`}
                onClick={() => setIsTicketFunctionVisible(!isTicketFunctionVisible)}
              >
                {ticketFunctionExists ? 'View function' : 'Add function'}
                <svg
                  className={`ml-1 w-4 h-4 transform transition-transform ${isTicketFunctionVisible ? 'rotate-180' : 'rotate-0'}`}
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            )}
            {isTicketEnabled && ticketFunctionLoading && (
              <div className="flex items-center justify-center w-full">
                <div className="w-4 h-4 border-2 border-gray-300 border-t-[#CD0ADD] rounded-full animate-spin"></div>
              </div>
            )}
          </div>

          {/* Ticket Function Content */}
          {isTicketEnabled && isTicketFunctionVisible && (
            <div className="p-6">
              <div className="w-full">
                <FunctionSettingsCheck
                  org_id={orgid}
                  onComplete={() => setIsTicketFunctionVisible(false)}
                  isTicketOnly={true} // Add this prop
                />
              </div>
            </div>
          )}
          {ticketError && <div className="px-6 pb-4 text-red-500 text-sm">{ticketError}</div>}
        </div>
      </div>

      {/* Confirmation Dialog */}
      <AlertDialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <AlertDialogContent>
          <div className="flex flex-col items-center justify-center py-4">
            <AlertDialogDescription className="text-gray-700 text-center mb-4">
              Are you sure you want to confirm this change? <br />
              This action will update your settings immediately.
            </AlertDialogDescription>
            <div className="flex gap-4 justify-center">
              <AlertDialogCancel className="rounded-md px-6 py-2">Cancel</AlertDialogCancel>
              <AlertDialogAction
                className="bg-[#CD0ADD] text-white rounded-md px-6 py-2 hover:bg-[#a800b8] transition-colors duration-200"
                onClick={handleConfirmToggle}
                asChild={false}
              >
                Confirm
              </AlertDialogAction>
            </div>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </div>

  )
}

export default LeadGeneration