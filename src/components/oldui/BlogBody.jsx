const BlogBody = ({ body, introduction, conclusion }) => {
  // Helper function to check if data exists and has content
  const hasContent = (data) => {
    return data && Array.isArray(data) && data.length > 0;
  };

  const renderContent = (item) => {
    // Handle the component-based structure (for body)
    if (item.__component) {
      switch (item.__component) {
        case "content.body-text":
          return item.body_text.map((textItem, index) => renderTextBlock(textItem, `body-text-${index}`));

        case "content.image-block":
        case "content.image-block font-manrope":
          return (
            <div key={item.id} className="my-4">
              {item.image && (
                <>
                  <img
                    src={`${item.image.url}`}
                    alt={item.image.alternativeText || item.image.name}
                    width={item.image.width}
                    height={item.image.height}
                    className="w-full h-auto rounded-lg shadow-lg"
                  />
                  {item.caption && <div className="font-manrope text-center text-gray-500 mt-2 text-sm">{item.caption}</div>}
                </>
              )}
            </div>
          );

        default:
          return null;
      }
    }
    // Handle the direct type-based structure (for introduction and conclusion)
    else if (item.type) {
      return renderTextBlock(item, `item-${Math.random()}`);
    }

    return null;
  };

  const renderTextBlock = (textItem, index) => {
    switch (textItem.type) {
      case "heading":
        // Add proper heading size classes based on level
        const headingClasses = {
          1: "text-5xl font-bold mt-6 mb-4 font-manrope",
          2: "text-4xl font-bold mt-6 mb-4 font-manrope",
          3: "text-3xl font-bold mt-6 mb-4 font-manrope",
          4: "text-2xl font-bold mt-6 mb-4 font-manrope",
          5: "text-xl font-bold mt-6 mb-4 font-manrope",
          6: "text-lg font-bold mt-6 mb-4 font-manrope"
        };
        const level = textItem.level || 3;
        const HeadingTag = `h${level}`;
        return (
          <HeadingTag key={index} className={headingClasses[level]}>
            {textItem.children && textItem.children.map((child, idx) => renderChild(child, idx))}
          </HeadingTag>
        );

      case "paragraph":
        return (
          <p key={index} className="mb-4">
            {textItem.children && textItem.children.map((child, idx) => renderChild(child, idx))}
          </p>
        );

      case "list":
        if (textItem.format === "ordered") {
          return (
            <ol key={index} className="list-decimal list-inside my-4">
              {textItem.children &&
                textItem.children.map((listItem, idx) => (
                  <li key={idx}>
                    {listItem.children && listItem.children.map((child, childIdx) => renderChild(child, childIdx))}
                  </li>
                ))}
            </ol>
          );
        } else {
          return (
            <ul key={index} className="list-disc list-inside my-4">
              {textItem.children &&
                textItem.children.map((listItem, idx) => (
                  <li key={idx}>
                    {listItem.children && listItem.children.map((child, childIdx) => renderChild(child, childIdx))}
                  </li>
                ))}
            </ul>
          );
        }

      case "quote":
        return (
          <blockquote key={index} className="border-l-4 border-gray-400 pl-4 italic my-4 text-gray-600">
            {textItem.children && textItem.children.map((child, idx) => renderChild(child, idx))}
          </blockquote>
        );

      case "code":
        return (
          <pre key={index} className="bg-gray-100 text-gray-800 p-4 rounded-lg my-4">
            <code>{textItem.plainText}</code>
          </pre>
        );

      case "link":
        return (
          <a
            key={index}
            href={textItem.url}
            className="text-blue-500 underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            {textItem.children && textItem.children.map((child, idx) => renderChild(child, idx))}
          </a>
        );

      default:
        return null;
    }
  };

  const renderChild = (child, index) => {
    if (child && child.type === "text") {
      return (
        <span
          key={index}
          style={{
            fontWeight: child.bold ? "bold" : "normal",
            fontStyle: child.italic ? "italic" : "normal",
            textDecoration: child.underline ? "underline" : "none",
          }}
        >
          {child.text}
        </span>
      );
    } else if (child && child.type === "link") {
      return (
        <a
          key={index}
          href={child.url}
          className="text-blue-500 underline"
          target="_blank"
          rel="noopener noreferrer"
        >
          {child.children && child.children.map((linkChild, idx) => renderChild(linkChild, idx))}
        </a>
      );
    }
    return null;
  };

  return (
    <div className="text-lg text-gray-700 space-y-6">
      {/* Render introduction if it exists */}
      {hasContent(introduction) && (
        <div className="introduction mb-8">
          {introduction.map((item, index) => (
            <div key={`intro-${index}`}>{renderContent(item)}</div>
          ))}
        </div>
      )}

      {/* Render body if it exists */}
      {hasContent(body) && (
        <div className="body mb-8">
          {body.map((item, index) => (
            <div key={`body-${index}`}>{renderContent(item)}</div>
          ))}
        </div>
      )}

      {/* Render conclusion if it exists */}
      {hasContent(conclusion) && (
        <div className="conclusion">
          {conclusion.map((item, index) => (
            <div key={`conclusion-${index}`}>{renderContent(item)}</div>
          ))}
        </div>
      )}

      {/* Show a message if no content exists */}
      {!hasContent(introduction) && !hasContent(body) && !hasContent(conclusion) && <p>No content available.</p>}
    </div>
  );
};

export default BlogBody;