import React, { useState, useEffect } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import { Edit, Trash, Search, Globe } from "lucide-react";
import { Dialog, DialogHeader, DialogBody, DialogFooter } from "@material-tailwind/react";

export default function WordVocabulary({ org_id }) {
  const [words, setWords] = useState({});
  const [newWordKey, setNewWordKey] = useState("");
  const [newWordValue, setNewWordValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [editKey, setEditKey] = useState(null);
  const [editValue, setEditValue] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState("");
  const [wordToDelete, setWordToDelete] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showList, setShowList] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [translating, setTranslating] = useState(false);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);

  // Language options
  const languages = [
    { code: "en", name: "English" },
    { code: "ur", name: "Urdu" },
    { code: "es", name: "Spanish" },
    { code: "fr", name: "French" },
    { code: "de", name: "German" },
    { code: "it", name: "Italian" },
    { code: "pt", name: "Portuguese" },
    { code: "zh", name: "Chinese" },
    { code: "ja", name: "Japanese" },
    { code: "ko", name: "Korean" },
    { code: "ar", name: "Arabic" },
  ];

  useEffect(() => {
    if (org_id) {
      fetchWords();
    }
  }, [org_id]);

  useEffect(() => {
    // Translate when a language is selected and there's text to translate
    if (selectedLanguage && newWordKey) {
      translateText(newWordKey, selectedLanguage);
    }
  }, [selectedLanguage, newWordKey]);

  const fetchWords = async () => {
    try {
      const response = await axios.get(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/word_dictionary/${org_id}`);
      setWords(response.data.word_dictionary || {});
    } catch (error) {
      toast.error("Error fetching words");
    }
  };

  const openDialogHandler = (mode, key, value) => {
    setDialogMode(mode);
    if (mode === "edit") {
      setEditKey(key);
      setEditValue(value);
      setWordToDelete(key); // Store the original key
    } else if (mode === "delete") {
      setWordToDelete(key);
    }
    setOpenDialog(true);
  };

  const closeDialogHandler = () => {
    setEditKey(null);
    setEditValue("");
    setWordToDelete(null);
    setOpenDialog(false);
  };

  const addWord = async () => {
    if (newWordKey.trim() && newWordValue.trim()) {
      setLoading(true);
      try {
        await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/word_dictionary/${org_id}`, {
          key: newWordKey,
          value: newWordValue,
        });
        setWords((prev) => ({ ...prev, [newWordKey]: newWordValue }));
        setNewWordKey("");
        setNewWordValue("");
        setSelectedLanguage("");
        toast.success("Word added successfully!");
        // Show the list after adding a word
        setShowList(true);
      } catch (error) {
        toast.error("Error adding word.");
      } finally {
        setLoading(false);
      }
    } else {
      toast.error("Both key and value are required.");
    }
  };

  const updateWord = async () => {
    if (!editKey || !editValue) return toast.error("Key and Value are required.");
    setLoading(true);
    try {
      const originalKey = wordToDelete;
      await axios.put(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/word_dictionary/${org_id}`, {
        key: originalKey,
        value: editValue,
        newKey: editKey,
      });
      setWords((prevWords) => {
        const updatedWords = { ...prevWords };
        delete updatedWords[originalKey];
        updatedWords[editKey] = editValue;
        return updatedWords;
      });
      toast.success("Successfully updated");
    } catch (error) {
      toast.error("Failed to update word");
    } finally {
      setLoading(false);
      closeDialogHandler();
    }
  };

  const translateText = async (text, targetLanguage) => {
    if (!text || !targetLanguage) return;
    
    setTranslating(true);
    try {
      // Using the translation API from your backend
      const response = await axios.post('/api/translate', {
        text,
        targetLanguage
      });
      
      if (response.data.translatedText) {
        setNewWordValue(response.data.translatedText);
      } else {
        toast.error("Translation failed");
      }
    } catch (error) {
      console.error("Translation error:", error);
      toast.error("Error translating text");
    } finally {
      setTranslating(false);
    }
  };

  const deleteWord = async () => {
    setLoading(true);
    try {
      await axios.delete(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/word_dictionary/${org_id}`, {
        data: { key: wordToDelete },
      });
      setWords((prevWords) => {
        const updatedWords = { ...prevWords };
        delete updatedWords[wordToDelete];
        return updatedWords;
      });
      toast.success("Successfully deleted");
    } catch (error) {
      toast.error("Failed to delete word");
    } finally {
      setLoading(false);
      closeDialogHandler();
    }
  };

  // Toggle between input form and words list
  const toggleView = () => {
    setShowList(!showList);
  };

  // Toggle language dropdown
  const toggleLanguageDropdown = () => {
    setShowLanguageDropdown(!showLanguageDropdown);
  };

  // Filter words based on search term
  const filteredWords = Object.entries(words).filter(
    ([key, value]) => 
      key.toLowerCase().includes(searchTerm.toLowerCase()) || 
      value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="max-w-4xl mx-auto p-4 font-manrope">
      <h2 className="text-4xl font-bold mb-4 text-[#CD0ADD]">Vocab List</h2>
      
      {/* Description text */}
      <div className="mb-6">
        <p className="text-sm text-gray-700">
          List the words that are pronounced incorrectly by your bot or that you would prefer the bot to use 
          alternative words for those specific words.
        </p>
      </div>

      {/* Input Form - Only show if showList is false */}
      {!showList && (
        <div className="mb-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
            <div className="relative">
              <div className="relative">
                <input
                  type="text"
                  placeholder="word to replace"
                  value={newWordKey}
                  onChange={(e) => setNewWordKey(e.target.value)}
                  className="w-full border border-gray-300 p-2 rounded pr-10"
                  required
                />
                <button 
                  onClick={toggleLanguageDropdown}
                  className="absolute gap-2 right-2 top-2 text-gray-500 hover:text-[#CD0ADD]"
                >
                  {/* <Globe size={20} />  */}  Translate 

                </button>
              </div>
              
              {/* Language dropdown */}
              {showLanguageDropdown && (
                <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded shadow-lg">
                  <div className="p-2 border-b text-sm font-medium text-gray-700">
                    Translate to:
                  </div>
                  <div className="max-h-60 overflow-y-auto">
                    {languages.map((lang) => (
                      <button
                        key={lang.code}
                        onClick={() => {
                          setSelectedLanguage(lang.code);
                          setShowLanguageDropdown(false);
                        }}
                        className="w-full text-left p-2 hover:bg-gray-100 text-sm"
                      >
                        {lang.name}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div>
              <input
                type="text"
                placeholder="word to replace with"
                value={newWordValue}
                onChange={(e) => setNewWordValue(e.target.value)}
                className="w-full border border-gray-300 p-2 rounded"
                required
              />
              {translating && (
                <p className="text-xs text-gray-500 mt-1">Translating...</p>
              )}
              {selectedLanguage && (
                <div className="flex items-center text-xs text-gray-500 mt-1">
                  <span>Translated to: {languages.find(l => l.code === selectedLanguage)?.name}</span>
                  <button 
                    onClick={() => setSelectedLanguage("")}
                    className="ml-2 text-[#CD0ADD] hover:underline"
                  >
                    Reset
                  </button>
                </div>
              )}
            </div>
          </div>
          <button
            onClick={addWord}
            disabled={loading}
            className="w-full bg-[#CD0ADD] text-white p-2 rounded hover:bg-purple-700 transition duration-200 mb-2"
          >
            {loading ? "Adding..." : "Add Word"}
          </button>
        </div>
      )}

      {/* Vocab List Button */}
      <button
        onClick={toggleView}
        className="w-full bg-white border border-[#CD0ADD] text-[#CD0ADD] p-2 rounded hover:bg-purple-50 transition duration-200 mb-4"
      >
        {showList ? "Add New Word" : "Vocab List"}
      </button>

      {/* Words List - Only show if showList is true */}
      {showList && (
        <>
          {/* Search Bar */}
          <div className="mb-4">
            <div className="relative flex items-center">
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full border border-[#CD0ADD] p-2 rounded-full pl-10"
              />
              <div className="absolute left-3">
                <Search size={18} className="text-gray-400" />
              </div>
            </div>
          </div>

          {/* Words List */}
          <div className="bg-white rounded">
            <div className="grid grid-cols-2 bg-[#CD0ADD] text-white p-2 rounded-t">
              <div className="font-medium">Word</div>
              <div className="font-medium">New Version</div>
            </div>
            
            {filteredWords.length > 0 ? (
              filteredWords.map(([key, value], index) => (
                <div 
                  key={key} 
                  className={`grid grid-cols-2 p-3 items-center ${
                    index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                  } border-b border-gray-200 relative group`}
                >
                  <div>{key}</div>
                  <div className="flex justify-between items-center">
                    <span>{value}</span>
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <button 
                        onClick={() => openDialogHandler("edit", key, value)}
                        className="text-[#CD0ADD] hover:text-purple-600 mr-2"
                      >
                        <Edit size={16} />
                      </button>
                      <button 
                        onClick={() => openDialogHandler("delete", key)}
                        className="text-[#CD0ADD] hover:text-red-600"
                      >
                        <Trash size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-gray-500">
                {searchTerm ? "No matching words found" : "No words added yet"}
              </div>
            )}
          </div>
        </>
      )}

      {/* Dialog for Edit/Delete */}
      <Dialog open={openDialog} handler={setOpenDialog}>
        <DialogHeader className="text-lg font-bold text-center text-purple-600">
          {dialogMode === "edit" ? "Edit Word" : "Delete Word"}
        </DialogHeader>
        <DialogBody className="p-4">
          {dialogMode === "edit" ? (
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Edit word"
                value={editKey}
                onChange={(e) => setEditKey(e.target.value)}
                className="w-full border border-gray-300 p-3 rounded"
              />
              <input
                type="text"
                placeholder="Edit replacement"
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="w-full border border-gray-300 p-3 rounded"
              />
            </div>
          ) : (
            <div className="text-center text-gray-700">
              Are you sure you want to delete the word <strong>{wordToDelete}</strong>?
            </div>
          )}
        </DialogBody>
        <DialogFooter className="space-x-2">
          {dialogMode === "edit" ? (
            <>
              <button
                onClick={updateWord}
                className="bg-[#CD0ADD] text-white py-2 px-4 rounded hover:bg-purple-700 transition duration-200"
                disabled={loading}
              >
                {loading ? "Saving..." : "Save"}
              </button>
              <button
                onClick={closeDialogHandler}
                className="border border-[#CD0ADD] text-[#CD0ADD] py-2 px-4 rounded hover:text-purple-700 transition duration-200"

              >
                Cancel
              </button>
            </>
          ) : (
            <>
              <button
                onClick={deleteWord}
                className="bg-[#CD0ADD] text-white py-2 px-4 rounded hover:bg-purple-700 transition duration-200"
                disabled={loading}
              >
                {loading ? "Deleting..." : "Delete"}
              </button>
              <button
                onClick={closeDialogHandler}
                className="border border-[#CD0ADD] text-[#CD0ADD] py-2 px-4 rounded hover:text-purple-700 transition duration-200"
              >
                Cancel
              </button>
            </>
          )}
        </DialogFooter>
      </Dialog>
    </div>
  );
}