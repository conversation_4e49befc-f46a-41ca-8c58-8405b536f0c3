import React from 'react'
import { motion } from 'framer-motion';

// Example of custom animation configuration
const leftSideAnimate = {
  initial: { opacity: 0, x: -200 }, // Start off-screen to the left
  whileInView: { opacity: 1, x: 0 }, // Move to the normal position with full opacity
};
const features = [
  {
    icon: <img src="/usecase1/pic1.png" alt="Upload Icon" className="w-[500px]" />,
    title: "Upload & Integrate",
    description: [
      "Easily upload essential documents or",
      "connect to your CRM for seamless",
      "information flow.",
    ],
    isRightIcon: false,
  },
  {
    icon: <img src="/Group 10.svg" alt="Upload Icon" className="w-[500px]" />,
    title: "Choose Your Agents",
    description: [
      "Scale your automated agents as",
      "needed to handle customer demand.",
    ],
    isRightIcon: true,
  },
  {
    icon: <img src="/Group 10.svg" alt="Upload Icon" className="w-[500px]" />,
    title: "Multi-Platform Outreach",
    description: [
      "From calls to messages across social",
      "media, engage customers on every",
      "major platform.",
    ],
    isRightIcon: false,
  },
]

export default function FeaturePageCard() {
  return (
    <motion.div
    initial={leftSideAnimate.initial} // Use the custom initial animation
            whileInView={leftSideAnimate.whileInView} // Trigger animation when in view
            transition={{ duration: 1, ease: 'easeOut' }} // Duration and easing function
          >
    <div className="max-w-7xl mx-auto px-4 md:px-6 py-6 md:py-10">
      <div className="space-y-20 md:space-y-32">
        {features.map((feature, index) => (
          <div
            key={index}
            className={`flex flex-col md:flex-row items-center justify-between ${
              feature.isRightIcon ? "md:flex-row-reverse" : ""
            }`}
          >
            <div className="flex-shrink-0">
              {feature.icon}
            </div>
            <div className={`${feature.isRightIcon ? "md:text-right" : "md:text-left"} max-w-sm`}>
              <h3 className="font-serif text-3xl md:text-4xl font-bold text-[#063C4F] mb-4">
                {feature.title}
              </h3>
              <p className="text-lg md:text-xl text-black leading-relaxed">
                {feature.description.map((line, lineIndex) => (
                  <React.Fragment key={lineIndex}>
                    {line}
                    {lineIndex < feature.description.length - 1 && <br />}
                  </React.Fragment>
                ))}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
    </motion.div>
  )
}