import { useState, useEffect } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'

export default function Component() {
  const [textBefore, setTextBefore] = useState('')
  const [textAfter, setTextAfter] = useState('')
  const fullTextBefore = 'POWERED BY'
  const fullTextAfter = 'TECHNOLOGIES'

  useEffect(() => {
    const intervalBefore = setInterval(() => {
      setTextBefore((current) => {
        if (current.length === fullTextBefore.length) {
          clearInterval(intervalBefore)
          return current
        }
        return fullTextBefore.slice(0, current.length + 1)
      })
    }, 100)

    const intervalAfter = setInterval(() => {
      setTextAfter((current) => {
        if (current.length === fullTextAfter.length) {
          clearInterval(intervalAfter)
          return current
        }
        return fullTextAfter.slice(0, current.length + 1)
      })
    }, 100)

    return () => {
      clearInterval(intervalBefore)
      clearInterval(intervalAfter)
    }
  }, [])

  return (
    <div className="flex flex-col items-center justify-center fixed inset-0 bg-[#063C4F] px-4 sm:px-8 md:px-16">
      <motion.div
        className="w-16 h-16 mb-12"
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 2,
          ease: "easeInOut",
          times: [0, 0.5, 1],
          repeat: Infinity,
        }}
      >
        <div className="w-full h-full rounded-full border-4 border-t-[#FF9F00] border-r-[#FF9F00] border-b-[#0066CC] border-l-[#0066CC] animate-spin" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.5 }}
        className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4"
      >
        <h2 className="text-2xl font-bold text-white animate-neonGlow text-center sm:text-left">
          {textBefore}
        </h2>
        <Image
          src="/najoomilogo.png"
          alt="Najoomi Technologies Logo"
          className="w-48 h-auto"
          width={192}
          height={48}
          priority
        />
        <h2 className="text-2xl font-bold text-white animate-neonGlow text-center sm:text-left">
          {textAfter}
        </h2>
      </motion.div>
    </div>
  )
}