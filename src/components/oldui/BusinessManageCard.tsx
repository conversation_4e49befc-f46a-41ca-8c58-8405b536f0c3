'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, useAnimation } from 'framer-motion'
import { Play, Pause } from 'lucide-react'

interface CardData {
  subheading: string;
  text: string[];
  video?: string;
}

const cardData = [
  {
    subheading: "Integrates With Any <br>Platform You Can Think Of",
    text: [
      "QuickTalk's omnichannel support connects",
      "with any platform, social media, website, and",
      "everything in between.",
    ],
    video: "/videos/integrates.mp4",
  },
  {
    subheading: "Customizable To Fit <br>Every Business Model",
    text: [
      "Whether you're a small startup or a large",
      "enterprise, QuickTalk adapts to meet your",
      "unique business needs.",
    ],
    video: "/videos/customizable.mp4",
  },
  {
    subheading: "Caters Inbound  &  <br>Outbound Calls",
    text: [
      "Can make calls to customers and also replies",
      "to calls without any support.",
    ],
    video: "/videos/calls.mp4",
  },
  {
    subheading: "Understands 90+ <br>Languages",
    text: [
      "Our multilingual support is designed to",
      "seamlessly understand and converse with",
      "your customers in over 90 languages, across",
      "the globe.",
    ],
    video: "/videos/multilingual.mp4",
  },
  {
    subheading: "Provides Customer <br>  Support 24/7",
    text: [
      "Our AI agent is available every time, making",
      "sure every customer inquiry gets a timely",
      "response.",
    ],
    video: "/videos/instant.mp4",
  },
  {
    subheading: "Cost Effective <br>  All-in-One Solution",
    text: [
      "Save your money with QuickTalk, the",
      "cost-cutting solution that handles multiple",
      "tasks at once.",
    ],
    video: "/videos/COST EFFECTIVE.mp4",
  },
  {
    subheading: "Quick & Easy Setup",
    text: [
      "Our expert team provides fast and seamless",
      "QuickTalk integration, with setup completed",
      "in minutes.",
    ],
    video: "/videos/Quick and easy.mp4",
  },
  {
    subheading: "Talk To Customers,<br> Their Way",
    text: [
      "QuickTalk is designed to send text",
      "messages, voice messages, and make",
      "calls without any human assistance.",
    ],
    video: "/videos/talk to customers.mp4",
  },
];

const VideoSlider: React.FC<{ video: string, controls?: any }> = ({ video, controls }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isMuted, setIsMuted] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.load();
      
      const handleCanPlay = () => {
        setIsLoaded(true);
        if (videoRef.current) {
          videoRef.current.play().catch(error => {
            console.log("Video autoplay failed:", error);
          });
        }
      };

      videoRef.current.addEventListener('canplay', handleCanPlay);
      
      return () => {
        if (videoRef.current) {
          videoRef.current.removeEventListener('canplay', handleCanPlay);
        }
      };
    }
  }, [video]);

  const toggleAudio = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(!isMuted);
    }
  };

  const handleVideoEnd = () => {
    if (videoRef.current) {
      videoRef.current.currentTime = 0;
      videoRef.current.play().catch(error => {
        console.log("Video replay failed:", error);
      });
    }
  };

  return (
    <motion.div
      className="w-full md:w-[600px] h-[200px] md:h-[370px] flex items-center justify-center relative group rounded-lg"
      initial={{ opacity: 0, y: 20 }}
      animate={controls}
    >
      <video 
        ref={videoRef}
        className="w-full h-full object-cover"
        playsInline
        muted={isMuted}
        loop
        preload="metadata"
        onEnded={handleVideoEnd}
      >
        <source src={video} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      
      {isLoaded && (
        <button
          onClick={toggleAudio}
          className="absolute bottom-4 right-4 flex items-center justify-center bg-white bg-opacity-90 rounded-full p-3 transform transition-all duration-300 hover:scale-110"
        >
          {isMuted ? (
            <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
            </svg>
          ) : (
            <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
            </svg>
          )}
        </button>
      )}
    </motion.div>
  );
};

const FixedViewportScrollingCard: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0)
  const [isFixed, setIsFixed] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const controls = useAnimation()
  const lastScrollTime = useRef<number>(0)
  const scrollThreshold = 50
  const scrollSensitivity = 1

  useEffect(() => {
    const handleScroll = () => {
      const now = Date.now()
      if (now - lastScrollTime.current < scrollThreshold) {
        return
      }
      lastScrollTime.current = now

      if (containerRef.current && cardRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect()
        const viewportHeight = window.innerHeight
        const totalScrollDistance = containerRect.height - viewportHeight
        const bottomPadding = viewportHeight * 0.05

        if (containerRect.top <= 0 && containerRect.bottom >= viewportHeight) {
          setIsFixed(true)
          const progress = (Math.abs(containerRect.top) / (totalScrollDistance - bottomPadding)) * scrollSensitivity
          const newIndex = Math.min(
            Math.floor(progress * cardData.length),
            cardData.length - 1
          )

          if (containerRect.bottom <= viewportHeight + bottomPadding) {
            setIsFixed(false)
          }

          if (newIndex !== activeIndex) {
            setActiveIndex(newIndex)
          }
        } else {
          setIsFixed(false)
        }
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [activeIndex])

  useEffect(() => {
    controls.start({ opacity: 1, y: 0, transition: { duration: 0.3 } })
  }, [activeIndex, controls])

  const baseHeight = window.innerWidth < 768 ? (window.innerHeight || 600) * 0.9 : (window.innerHeight || 700) * 0.7
  const containerHeight = `${(cardData.length + 0.1) * baseHeight}px`
  const currentCard = cardData[activeIndex]

  return (
    <div
      ref={containerRef}
      style={{ height: containerHeight }}
      className="relative font-manrope"
    >
      <div
        ref={cardRef}
        className={`w-full px-4 pt-40 md:px-6 md:pt-28 lg:px-24 ${isFixed ? 'fixed top-0 left-0 right-0' : 'absolute'} ${!isFixed && activeIndex === cardData.length - 1 ? 'bottom-8' : ''}`}
      >
        <div className="bg-gradient-to-b from-[#033035] via-[#0C3330] to-[#183C1E] rounded-3xl p-4 md:p-6 flex flex-col items-center space-y-6 md:space-y-10 relative">
          <motion.h1
            className="text-2xl md:text-3xl lg:text-5xl font-bold text-white text-center mb-2 md:mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={controls}
          >
            Manage Your Business Like Never Before
          </motion.h1>

          <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full">
            <div className="text-white flex-1 flex flex-col justify-center space-y-2 md:space-y-4 min-h-[200px] md:min-h-[300px] leading-none">
              <motion.h2
                className="text-xl md:text-2xl lg:text-4xl text-[#E44FFA] font-semibold pl-2 md:pl-10 text-center md:text-left"
                key={`heading-${activeIndex}`}
                initial={{ opacity: 0, y: 20 }}
                animate={controls}
              >
                <span
                  dangerouslySetInnerHTML={{ __html: currentCard.subheading }}
                />
              </motion.h2>

              <div className="text-center lg:text-left">
                {currentCard.text.map((para, index) => (
                  <motion.p
                    key={`para-${activeIndex}-${index}`}
                    className="text-sm md:text-base lg:text-xl pl-2 md:pl-10"
                    initial={{ opacity: 0, y: 20 }}
                    animate={controls}
                    transition={{ delay: index * 0.1 }}
                  >
                    {para}
                  </motion.p>
                ))}
              </div>
            </div>

            {currentCard.video && (
              <VideoSlider video={currentCard.video} controls={controls} />
            )}
          </div>

          <motion.div
            className="flex justify-center space-x-2 mt-4"
            initial={{ opacity: 0, y: 20 }}
            animate={controls}
          >
            {cardData.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${index === activeIndex ? 'bg-white w-4' : 'bg-white bg-opacity-50'}`}
              />
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default FixedViewportScrollingCard