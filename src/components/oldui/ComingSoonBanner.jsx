
import { useState } from "react"
import {
  <PERSON><PERSON>,
  In<PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>eader,
  DialogBody,
  DialogFooter,
} from "@material-tailwind/react"
import { Facebook, Twitter, Instagram, Loader2, Linkedin } from 'lucide-react'
import emailjs from "@emailjs/browser"
import toast from "react-hot-toast"

export default function Banner() {
  const [formData, setFormData] = useState({ email: "" })
  const [errors, setErrors] = useState({ email: "" })
  const [loading, setLoading] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.id]: e.target.value,
    })
  }

  const validateEmail = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!formData.email) {
      setErrors((prev) => ({ ...prev, email: "Email is required" }))
      return false
    } else if (!emailRegex.test(formData.email)) {
      setErrors((prev) => ({ ...prev, email: "Email is not valid" }))
      return false
    }
    setErrors((prev) => ({ ...prev, email: "" }))
    return true
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    const isEmailValid = validateEmail()

    if (!isEmailValid) return

    setLoading(true)

    try {
      const serviceId = 'service_2wpb3tg'
      const templateId = 'template_i0ma3xd'
      const userId = 'mcd1TyaKkqFaRaPtG'

      const emailParams = {
        to_email: "<EMAIL>",
        from_email: formData.email,
        message: "I want to get early access of quicktalk",
      }

      await emailjs.send(serviceId, templateId, emailParams, userId)
      const response = await fetch('/api/add-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: formData.email }),
      })

      if (!response.ok) {
        throw new Error('Database insertion failed')
      }
  
      toast.success("Thanks for subscribing!")
      setFormData({ email: "" })
      setIsModalOpen(false)
    } catch (error) {
      console.error("Error:", error)
      toast.error("An error occurred. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-black px-6 py-8 font-sans relative overflow-hidden">
      {/* Logo */}
      <div className="max-w-6xl mx-auto pt-4">
        <img src="/earlyacess.png" alt="Quicktalk now" className="h-12 mb-12" />
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <h1 className="text-[4rem] font-bold text-left text-white w-full leading-none">
              Coming Soon!
            </h1>
            <p className="text-gray-400 text-left text-[2rem] max-w-lg">
              SaaS Based, Human-less,
              Omnichannel Contact Center
            </p>
            <p className="text-gray-400 text-left text-lg max-w-lg">
              QuickTalk is the world&apos;s first all-in-one AI customer service solution, offering seamless integration across every channel, multilingual support, automated responses, and actionable insights.
            </p>
            <Button
              onClick={() => setIsModalOpen(true)}
              variant="outlined"
              className="font-manrope flex justify-start min-w-[140px] border-[#CD0ADD] text-[#FF00FF] hover:bg-[#FF00FF] hover:text-white"
            >
              Early Access
            </Button>

            {/* Social Links */}
            <div className="pt-8">
              <div className="flex gap-4">
                <a href="https://www.facebook.com/profile.php?id=61571355689803" className="text-white hover:text-[#4ADE80] transition-colors">
                  <Facebook size={20} />
                </a>
                <a href="https://x.com/najoomitech" className="text-white hover:text-[#4ADE80] transition-colors">
                  <Twitter size={20} />
                </a>
                <a href="https://www.instagram.com/najoomitechnologies/" className="text-white hover:text-[#4ADE80] transition-colors">
                  <Instagram size={20} />
                </a>
                <a href="https://www.linkedin.com/company/najoomi/" className="text-white hover:text-[#4ADE80] transition-colors">
                  <Linkedin size={20} />
                </a>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="absolute inset-0  bg-gradient-to-b from-[#033035] via-[#0C3330] to-[#183C1E] rounded-full blur-3xl opacity-20" />
            <div className="relative z-10 rounded-full w-full max-w-md mx-auto overflow-hidden">
              <div className="absolute inset-0  bg-gradient-to-b from-[#033035] via-[#0C3330] to-[#183C1E] opacity-50" />
              <img
                src="/Circle.svg"
                alt="Team collaboration"
                className="relative z-10 w-full h-full object-cover animate-move"
              />
            </div>
          </div>
        </div>
      </div>

      <Dialog open={isModalOpen} handler={() => setIsModalOpen(false)} size="sm" className="flex w-72 flex-col gap-6">
        <DialogHeader>Get in Touch</DialogHeader>
        <DialogBody>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                id="email"
                type="email"
                label="Your Email Address"
                value={formData.email}
                onChange={handleChange}
                variant="standard"
                error={!!errors.email}
                className=" !border-t-blue-gray-200 focus:!border-t-gray-900"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email}</p>
              )}
            </div>
          </form>
        </DialogBody>
        <DialogFooter className="space-x-2">
          <Button
            className="text-[#FF00FF]"
            variant="text"
            color="red"
            onClick={() => setIsModalOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="outlined"
            className="font-manrope text-center flex justify-start  border-[#CD0ADD] text-[#FF00FF] hover:bg-[#FF00FF] hover:text-white"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading && <Loader2 className="animate-spin w-4 h-4" />}
            Subscribe
          </Button>
        </DialogFooter>
      </Dialog>
    </div>
  )
}