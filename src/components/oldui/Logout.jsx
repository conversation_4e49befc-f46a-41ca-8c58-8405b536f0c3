import React, { useState, useEffect } from 'react';
import { CognitoUserPool } from 'amazon-cognito-identity-js'; // Assuming AWS Cognito is used
import userPool from '@/lib/userPoolConfig';
const Logout = () => {
  const [loading, setLoading] = useState(false);

  const logout = async () => {
    setLoading(true);
    const user = userPool.getCurrentUser();

    if (user) {
      console.log('Signing out user:', user.getUsername());
      localStorage.removeItem('username');
      localStorage.removeItem('email');
      user.signOut();
      setLoading(false); // Update loading state after sign out
      window.location.href = '/'; // Redirect to home after logout
    } else {
      console.log('No user to sign out');
      setLoading(false); // Make sure to update loading state even if no user is found
    }
  };

  return (
    <div className="flex justify-center items-center h-screen">
      <button
        onClick={logout}
        className="bg-red-500 text-white py-2 px-4 rounded-lg"
        disabled={loading} // Disable the button when loading
      >
        {loading ? 'Logging Out...' : 'Logout'}
      </button>
    </div>
  );
};

export default Logout;
