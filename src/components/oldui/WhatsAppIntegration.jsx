"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON>Head<PERSON>, TabsBody, Tab, TabPanel, Input } from "@material-tailwind/react"
import { Toaster, toast } from "react-hot-toast"
import { GoChevronUp, GoChevronDown } from 'react-icons/go'

export default function WhatsAppIntegration({ organ_id, links, handleGenerateLink, copyToClipboard }) {
  const [activeTab, setActiveTab] = useState("meta")
  const [expandWebhook, setExpandWebhook] = useState(false)
  const [showSavedToken, setShowSavedToken] = useState(false)
  const [showTooltip, setShowTooltip] = useState({
    phoneId: false,
    verifyToken: false,
    reenterVerifyToken: false,
    webhook: false,
    graphApiToken: false,
  })

  // Form state
  const [formData, setFormData] = useState({
    phoneId: "",
    graphApiToken: "",
  })

  // Editing states for each field
  const [editingFields, setEditingFields] = useState({
    phoneId: false,
    graphApiToken: false,
  })

  // Form validation
  const [errors, setErrors] = useState({})

  // Number verification states
  const [isVerificationOn, setIsVerificationOn] = useState(false)
  const [authApiUrl, setAuthApiUrl] = useState("")
  const [isEditing, setIsEditing] = useState(false)
  const [hasApi, setHasApi] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [showWebhook, setShowWebhook] = useState(false)
  const [credentialsSaved, setCredentialsSaved] = useState(false)

  // New: Track which API feature is being configured
  const [configuringFeature, setConfiguringFeature] = useState(null)
  // New: Track enabled state for each feature
  const [apiFeatures, setApiFeatures] = useState({
    numberVerification: false,
    userDetails: false,
    authToken: false,
  })

  // New: For method dropdown and auth fields
  const [apiMethod, setApiMethod] = useState({
    numberVerification: 'POST',
    userDetails: 'POST',
    authToken: 'POST',
  })
  const [apiInputs, setApiInputs] = useState({
    numberVerification: '',
    userDetails: '',
    authToken: '',
    authTokenUsername: '',
    authTokenPassword: '',
  })
  const [authFields, setAuthFields] = useState({
    username: '',
    password: '',
    confirmPassword: '',
  })

  // Track editing state for each API feature
  const [apiEditing, setApiEditing] = useState({
    numberVerification: false,
    userDetails: false,
    authToken: false,
    authTokenCredentials: true,
  })

  // Track if the value is truly saved/enabled
  const [apiSaved, setApiSaved] = useState({
    numberVerification: false,
    userDetails: false,
    authToken: false,
    authTokenCredentials: false,
  })

  // Tooltip content
  const tooltipContent = {
    phoneId: "Phone Number ID is generated in Meta Business Suite under System Users tab",
    webhook: "Enter webhook in developers.facebook.com -> WhatsApp -> Configuration -> Webhook -> Callback URL",
    graphApiToken: "Graph API Token is generated in Meta Business Suite under System Users tab",
  }

  // Password visibility state
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Fetch API configuration and credentials on component mount
  useEffect(() => {
    const fetchApiConfig = async () => {
      try {
        setIsLoading(true)

        // Fetch organization update data for verification settings
        const updateOrgResponse = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get/update_organization`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            organization_id: organ_id,
          }),
        })

        const updateOrgData = await updateOrgResponse.json()

        if (updateOrgData.authentication_api_link) {
          setAuthApiUrl(updateOrgData.authentication_api_link)
          setHasApi(true)
        }

        setIsVerificationOn(updateOrgData.authentic_state === true)

        // Fetch WhatsApp credentials from the correct endpoint
        const credentialsResponse = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get_org_credentials`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            organization_id: organ_id,
          }),
        })

        const credentialsData = await credentialsResponse.json()
        console.log("Credentials data:", credentialsData)

        // Check if credentials data exists and populate the form
        if (credentialsData) {
          // Populate form fields with the fetched data
          setFormData({
            phoneId: credentialsData.phone_number_id || "",
            graphApiToken: credentialsData.graph_api_token || "",
          })

          // NEW: Populate API feature fields from credentialsData
          setApiInputs({
            numberVerification: credentialsData.authentication_api_link || "",
            userDetails: credentialsData.user_details_api || "",
            authToken: credentialsData.get_auth_token_api || "",
            authTokenUsername: credentialsData.auth_token_username || "",
            authTokenPassword: credentialsData.auth_token_password || "",
          })

          // Set apiSaved for fields that have a value from API
          setApiSaved({
            numberVerification: !!credentialsData.authentication_api_link,
            userDetails: !!credentialsData.user_details_api,
            authToken: !!credentialsData.get_auth_token_api,
            authTokenCredentials: !!credentialsData.auth_token_username || !!credentialsData.auth_token_password,
          })

          // Set apiEditing: if field is saved, editing is false; if not saved, editing is true
          setApiEditing({
            numberVerification: !credentialsData.authentication_api_link,
            userDetails: !credentialsData.user_details_api,
            authToken: !credentialsData.get_auth_token_api,
            authTokenCredentials: !(credentialsData.auth_token_username || credentialsData.auth_token_password),
          })

          // Show webhook button if all essential fields have data from the API
          if (
            credentialsData.phone_number_id &&
            credentialsData.graph_api_token
          ) {
            setShowWebhook(true)
            setCredentialsSaved(true)
          }
        }

        setIsLoading(false)
      } catch (error) {
        toast.error("Failed to fetch configuration")
        console.error("Error fetching configuration:", error)
        setIsLoading(false)
      }
    }

    if (organ_id) {
      fetchApiConfig()
    } else {
      setIsLoading(false)
    }
  }, [organ_id])

  // Handle field edit/save
  const handleFieldEdit = async (fieldName) => {
    if (editingFields[fieldName]) {
      // Save the field
      try {
        const loadingToast = toast.loading("Saving field...")

        const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/whatsapp_authentication/add_update`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            organization_id: organ_id,
            phone_number_id: formData.phoneId,
            graph_api_token: formData.graphApiToken,
          }),
        })

        toast.dismiss(loadingToast)

        if (response.ok) {
          toast.success("Field saved successfully")
          setEditingFields(prev => ({ ...prev, [fieldName]: false }))

          // If graph API token was saved, show webhook section
          if (fieldName === 'graphApiToken' && formData.phoneId) {
            setShowWebhook(true)
            setCredentialsSaved(true)
          }
        } else {
          toast.error("Failed to save field")
        }
      } catch (error) {
        toast.error("An error occurred while saving")
        console.error("Error saving field:", error)
      }
    } else {
      // Enable editing
      setEditingFields(prev => ({ ...prev, [fieldName]: true }))
    }
  }

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle form submission
  const handleSaveAll = async () => {
    try {
      // Show loading toast
      const loadingToast = toast.loading("Saving WhatsApp settings...")

      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/whatsapp_authentication/add_update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          organization_id: organ_id,
          phone_number_id: formData.phoneId,
          graph_api_token: formData.graphApiToken,
        }),
      })

      const responseData = await response.json()

      // Dismiss loading toast
      toast.dismiss(loadingToast)

      if (response.ok) {
        // Show success toast
        toast.success("WhatsApp settings saved successfully")
        console.log("WhatsApp settings saved successfully:", responseData)

        // Enable webhook button after successful save if all fields have data
        if (formData.phoneId && formData.graphApiToken) {
          setShowWebhook(true)
          setCredentialsSaved(true)
        }
      } else {
        // Show error toast
        toast.error("Failed to save WhatsApp settings")
        console.error("Failed to save WhatsApp settings:", responseData)
      }
    } catch (error) {
      toast.error("An error occurred while saving settings")
      console.error("Error saving WhatsApp settings:", error)
    }
  }

  // Handle API URL actions (Edit, Save)
  const handleApiAction = async () => {
    if (hasApi && !isEditing) {
      // If API exists and not in edit mode, enable editing
      setIsEditing(true)
    } else {
      // Save API URL
      try {
        const loadingToast = toast.loading("Saving API URL...")

        const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get/update_organization`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            organization_id: organ_id,
            authentication_api_link: authApiUrl,
          }),
        })

        toast.dismiss(loadingToast)

        if (response.ok) {
          setHasApi(true)
          setIsEditing(false)
          toast.success("API URL saved successfully")
        } else {
          toast.error("Failed to save API URL")
        }
      } catch (error) {
        toast.error("Failed to update API URL")
        console.error("Error updating API URL:", error)
      }
    }
  }

  // Handle toggling verification
  const handleToggleVerification = async (turnOn) => {
    try {
      // If turning on verification without an API, save the API URL first
      if (turnOn && !hasApi) {
        if (!authApiUrl.trim()) {
          toast.error("Please enter an API URL first")
          return
        }

        // Save the API URL first
        const saveApiLoadingToast = toast.loading("Saving API URL...")

        const saveApiResponse = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get/update_organization`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            organization_id: organ_id,
            authentication_api_link: authApiUrl,
          }),
        })

        toast.dismiss(saveApiLoadingToast)

        if (!saveApiResponse.ok) {
          toast.error("Failed to save API URL")
          return
        }

        setHasApi(true)
        setIsEditing(false)
      }

      const loadingToast = toast.loading(`${turnOn ? "Enabling" : "Disabling"} number verification...`)

      // API payload for updating verification status
      const payload = {
        organization_id: organ_id,
      }

      if (turnOn) {
        // For turning on verification - need to include API URL
        payload.authentication_api_link = authApiUrl
        payload.authentic_state = true
      } else {
        // For turning off verification
        payload.authentic_state = false
      }

      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/get/update_organization`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      toast.dismiss(loadingToast)

      if (response.ok) {
        setIsVerificationOn(turnOn)
        toast.success(`Number verification ${turnOn ? "enabled" : "disabled"} successfully`)
      } else {
        toast.error(`Failed to ${turnOn ? "enable" : "disable"} verification`)
      }
    } catch (error) {
      toast.error(`Failed to ${turnOn ? "enable" : "disable"} verification`)
      console.error(`Error ${turnOn ? "enabling" : "disabling"} verification:`, error)
    }
  }

  // Handle copying to clipboard
  const handleCopyToken = () => {
    copyToClipboard(formData.verifyToken);
    toast.success("Verify Token copied to clipboard");
  };

  // Add this before return
  const isFirstTime = !credentialsSaved;
  const allFieldsFilled = formData.phoneId && formData.graphApiToken;

  // New: Handle enable/disable for API features with edit logic and API call
  const handleFeatureButton = async (feature) => {
    // If field is filled and not editing, treat as Disable
    if (apiSaved[feature] && !apiEditing[feature]) {
      setApiEditing((prev) => ({ ...prev, [feature]: true }));
      setApiFeatures((prev) => ({ ...prev, [feature]: false }));
      setApiSaved((prev) => ({ ...prev, [feature]: false }));
      // Do NOT clear the value, just make it editable
      return;
    }
    // Else, treat as Enable (save)
    let apiValue = '';
    let apiKey = '';
    if (feature === 'numberVerification') {
      apiValue = apiInputs.numberVerification;
      apiKey = 'authentication_api_link';
    } else if (feature === 'userDetails') {
      apiValue = apiInputs.userDetails;
      apiKey = 'user_details_api';
    } else if (feature === 'authToken') {
      apiValue = apiInputs.authToken;
      apiKey = 'get_auth_token_api';
    } else if (feature === 'authTokenUsername') {
      apiValue = apiInputs.authTokenUsername;
      apiKey = 'auth_token_username';
    } else if (feature === 'authTokenPassword') {
      apiValue = apiInputs.authTokenPassword;
      apiKey = 'auth_token_password';
    }
    if (!apiValue || apiValue.trim() === '') {
      toast.error('API URL cannot be empty');
      return;
    }
    try {
      const loadingToast = toast.loading('Saving API info...');
      const response = await fetch('https://devquicktalkdbwrapper.najoomi.ai/update_auth_info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          organization_id: organ_id,
          [apiKey]: apiValue,
        }),
      });
      toast.dismiss(loadingToast);
      if (response.ok) {
        toast.success('API info saved successfully');
        setApiEditing((prev) => ({ ...prev, [feature]: false }));
        setApiFeatures((prev) => ({ ...prev, [feature]: true }));
        setApiSaved((prev) => ({ ...prev, [feature]: true }));
      } else {
        toast.error('Failed to save API info');
      }
    } catch (error) {
      toast.error('Error saving API info');
    }
  };

  const handleAuthTokenCredentialsButton = async () => {
    // If saved and not editing, treat as Disable
    if (apiSaved.authTokenCredentials && !apiEditing.authTokenCredentials) {
      setApiEditing((prev) => ({ ...prev, authTokenCredentials: true }));
      setApiSaved((prev) => ({ ...prev, authTokenCredentials: false }));
      return;
    }
    // Else, treat as Enable (save)
    if (!apiInputs.authTokenUsername.trim() || !apiInputs.authTokenPassword.trim()) {
      toast.error('Username and Password cannot be empty');
      return;
    }
    // Confirm password check (if editing or first time)
    if ((apiEditing.authTokenCredentials || !apiSaved.authTokenCredentials) && (authFields.confirmPassword !== apiInputs.authTokenPassword)) {
      toast.error('Passwords do not match');
      return;
    }
    try {
      const loadingToast = toast.loading('Saving credentials...');
      const response = await fetch('https://devquicktalkdbwrapper.najoomi.ai/update_auth_info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          organization_id: organ_id,
          auth_token_username: apiInputs.authTokenUsername,
          auth_token_password: apiInputs.authTokenPassword,
        }),
      });
      toast.dismiss(loadingToast);
      if (response.ok) {
        toast.success('Credentials saved successfully');
        setApiEditing((prev) => ({ ...prev, authTokenCredentials: false }));
        setApiSaved((prev) => ({ ...prev, authTokenCredentials: true }));
        setAuthFields(f => ({ ...f, confirmPassword: '' }));
      } else {
        toast.error('Failed to save credentials');
      }
    } catch (error) {
      toast.error('Error saving credentials');
    }
  };

  // Add a delete handler for each feature
  const handleFeatureDelete = async (feature) => {
    if (feature === 'authTokenCredentials') {
      try {
        const loadingToast = toast.loading('Deleting credentials...');
        const response = await fetch('https://devquicktalkdbwrapper.najoomi.ai/whatsapp/delete/auth_details', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            organization_id: organ_id,
            auth_token_username: true,
            auth_token_password: true,
          }),
        });
        toast.dismiss(loadingToast);
        if (response.ok) {
          toast.success('Credentials deleted successfully');
          setApiInputs((prev) => ({ ...prev, authTokenUsername: '', authTokenPassword: '' }));
          setApiSaved((prev) => ({ ...prev, authTokenCredentials: false }));
          setApiEditing((prev) => ({ ...prev, authTokenCredentials: true }));
        } else {
          toast.error('Failed to delete credentials');
        }
      } catch (error) {
        toast.error('Error deleting credentials');
      }
      return;
    }
    let apiKey = '';
    if (feature === 'numberVerification') {
      apiKey = 'authentication_api_link';
    } else if (feature === 'userDetails') {
      apiKey = 'user_details_api';
    } else if (feature === 'authToken') {
      apiKey = 'get_auth_token_api';
    } else if (feature === 'authTokenUsername') {
      apiKey = 'auth_token_username';
    } else if (feature === 'authTokenPassword') {
      apiKey = 'auth_token_password';
    }
    try {
      const loadingToast = toast.loading('Deleting API info...');
      const response = await fetch('https://devquicktalkdbwrapper.najoomi.ai/whatsapp/delete/auth_details', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          organization_id: organ_id,
          [apiKey]: true,
        }),
      });
      toast.dismiss(loadingToast);
      if (response.ok) {
        toast.success('API info deleted successfully');
        setApiInputs((prev) => ({ ...prev, [feature]: '' }));
        setApiSaved((prev) => ({ ...prev, [feature]: false }));
        setApiEditing((prev) => ({ ...prev, [feature]: true }));
      } else {
        toast.error('Failed to delete API info');
      }
    } catch (error) {
      toast.error('Error deleting API info');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-pulse">Loading configuration...</div>
        <Toaster position="top-right" />
      </div>
    )
  }

  return (
    <div className="mt-4 font-manrope">
      <Tabs value={activeTab} onChange={(value) => setActiveTab(value)} className="font-manrope">
        <TabsHeader
          className="rounded-none border-b border-blue-gray-200 bg-transparent p-0"
          indicatorProps={{
            className: "bg-transparent border-b-2 border-[#CD0ADD] shadow-none rounded-none ",
          }}
        >
          <Tab
            value="meta"
            className={`font-manrope py-2 px-4 font-medium ${activeTab === "meta" ? "text-[#CD0ADD]" : "text-gray-500"}`}
            onClick={() => setActiveTab("meta")}
          >
            Meta Account
          </Tab>
          <Tab
            value="api-features"
            className={`font-manrope py-2 px-4 font-medium ${activeTab === "api-features" ? "text-[#CD0ADD]" : "text-gray-500"}`}
            onClick={() => setActiveTab("api-features")}
          >
            API Features
          </Tab>
        </TabsHeader>

        <TabsBody>
          <TabPanel value="meta" className="px-0 font-manrope">
            <div className="mb-6">
              <p className="mb-4 text-lg">To integrate bot with WhatsApp you need to</p>

              <h3 className="text-[#CD0ADD] font-medium mb-2">Setup Your Meta Account</h3>
              <p className="text-sm text-gray-600 mb-4">
                Here is the{" "}
                <a
                  href="https://najoomi-07f909e2.mintlify.app/quicktalk"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[#CD0ADD] hover:underline"
                >
                  setup manual
                </a>{" "}
                for Meta
              </p>

              <div className="space-y-4">

                {/* Graph API Token field */}
                <div className="flex items-center">
                  <div className="relative">
                    <Input
                      type="text"
                      name="graphApiToken"
                      label="Graph API Token"
                      placeholder="Enter Graph API Token"
                      value={formData.graphApiToken}
                      onChange={handleInputChange}
                      disabled={credentialsSaved ? !editingFields.graphApiToken : false}
                      className="w-[250px]"
                      labelProps={{
                        className: "text-[#CD0ADD] font-manrope",
                      }}
                      containerProps={{
                        className: "min-w-[250px] ",
                      }}
                      variant="outlined"
                    />
                  </div>
                  <div className="flex items-center ml-2">
                    <div className="relative">
                      <button
                        className="text-gray-400"
                        onMouseEnter={() => setShowTooltip({ ...showTooltip, graphApiToken: true })}
                        onMouseLeave={() => setShowTooltip({ ...showTooltip, graphApiToken: false })}
                      >
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          ></path>
                        </svg>
                      </button>
                      {showTooltip.graphApiToken && (
                        <div className="absolute left-[30px] top-0 -translate-y-1/2 z-50 w-64 p-2 text-sm bg-yellow-200 text-black rounded-md shadow-lg">
                          {tooltipContent.graphApiToken}
                        </div>
                      )}
                    </div>
                    {credentialsSaved && formData.graphApiToken && (
                      <button
                        onClick={() => handleFieldEdit('graphApiToken')}
                        className="ml-2 px-3 py-1 bg-[#CD0ADD] text-white rounded hover:bg-purple-700"
                      >
                        {editingFields.graphApiToken ? 'Save' : 'Edit'}
                      </button>
                    )}
                  </div>
                </div>

                {/* Phone Number ID field */}
                <div className="flex items-center">
                  <div className="relative">
                    <Input
                      type="text"
                      name="phoneId"
                      label="Phone Number ID"
                      placeholder="Enter Phone Number ID"
                      value={formData.phoneId}
                      onChange={handleInputChange}
                      disabled={credentialsSaved ? !editingFields.phoneId : false}
                      className="w-[250px]"
                      labelProps={{
                        className: "text-[#CD0ADD] font-manrope",
                      }}
                      containerProps={{
                        className: "min-w-[250px] ",
                      }}
                      variant="outlined"
                    />
                  </div>
                  <div className="flex items-center ml-2">
                    <div className="relative">
                      <button
                        className="text-gray-400"
                        onMouseEnter={() => setShowTooltip({ ...showTooltip, phoneId: true })}
                        onMouseLeave={() => setShowTooltip({ ...showTooltip, phoneId: false })}
                      >
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          ></path>
                        </svg>
                      </button>
                      {showTooltip.phoneId && (
                        <div className="absolute left-[30px] top-0 -translate-y-1/2 z-50 w-64 p-2 text-sm bg-yellow-200 text-black rounded-md shadow-lg">
                          {tooltipContent.phoneId}
                        </div>
                      )}
                    </div>
                    {credentialsSaved && formData.phoneId && (
                      <button
                        onClick={() => handleFieldEdit('phoneId')}
                        className="ml-2 px-3 py-1 bg-[#CD0ADD] text-white rounded hover:bg-purple-700"
                      >
                        {editingFields.phoneId ? 'Save' : 'Edit'}
                      </button>
                    )}
                  </div>
                </div>

                {/* Save All button - Only show on first time, and only enabled if all fields are filled */}
                {!credentialsSaved && (
                  <div>
                    <button
                      onClick={handleSaveAll}
                      className="px-4 py-2 bg-[#CD0ADD] text-white rounded hover:bg-pink-800 disabled:opacity-50"
                      disabled={!formData.phoneId || !formData.graphApiToken}
                    >
                      Save All
                    </button>
                  </div>
                )}

                <div>
                  {/* Saved Token Dropdown */}
                  {credentialsSaved && (
                    <div className="mt-14 space-y-4">
                      <div
                        className="flex items-center cursor-pointer text-[#CD0ADD] mb-2"
                        onClick={() => setShowSavedToken(!showSavedToken)}
                      >
                        <h4 className="text-[#CD0ADD] font-medium">Verify Token</h4>
                        <svg
                          className={`w-5 h-5 ml-1 transform ${showSavedToken ? "rotate-180" : ""}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </div>

                      {showSavedToken && (
                        <div className="p-4 bg-gray-50 rounded-lg mb-4">
                          <p className="mb-2 text-sm">
                            Copy this Graph api token and paste it in WhatsApp webhook verification in developers.facebook.com
                          </p>
                          <div className="flex items-center">
                            <p className="bg-white p-2 border border-gray-300 text-sm text-gray-600 truncate flex-1">
                              najoomi-whatsapp-webhook-123
                            </p>
                            <button
                              onClick={handleCopyToken}
                              className="ml-2 text-[#CD0ADD] hover:text-purple-700"
                            >
                              <svg
                                className="w-5 h-5"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                                ></path>
                              </svg>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Webhook Section - Only display if credentials are saved to the API */}
                  {showWebhook && credentialsSaved && (
                    <div className="mt-14 space-y-4">
                      <div>
                        <div className="flex items-center">
                          <div
                            className="flex items-center cursor-pointer"
                            onClick={() => setExpandWebhook(!expandWebhook)}
                          >
                            <h3 className="text-[#CD0ADD] font-medium">Webhook</h3>
                            <svg
                              className={`w-5 h-5 ml-1 text-[#CD0ADD] transform ${expandWebhook ? "rotate-180" : ""}`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                          </div>
                          <div className="relative ml-10">
                            <div className="relative">
                              <button
                                className="text-gray-400"
                                onMouseEnter={() => setShowTooltip({ ...showTooltip, webhook: true })}
                                onMouseLeave={() => setShowTooltip({ ...showTooltip, webhook: false })}
                              >
                                <svg
                                  className="w-5 h-5"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                  ></path>
                                </svg>
                              </button>
                            </div>
                            {showTooltip.webhook && (
                              <div className="absolute left-[30px] top-0 -translate-y-1/2 z-50 w-64 p-2 text-sm bg-yellow-200 text-black rounded-md shadow-lg">
                                {tooltipContent.webhook}
                              </div>
                            )}
                          </div>
                        </div>
                        {expandWebhook && (
                          <>
                            {links?.whatsapp ? (
                              <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                                <p className="text-sm font-medium">Webhook URL</p>
                                <div className="flex items-center mt-2">
                                  <p className="text-sm text-gray-600 truncate flex-1">{links.whatsapp}</p>
                                  <button
                                    onClick={() => {
                                      copyToClipboard(links.whatsapp)
                                      toast.success("Webhook URL copied to clipboard")
                                    }}
                                    className="ml-2 text-[#CD0ADD] hover:text-purple-700"
                                  >
                                    <svg
                                      className="w-5 h-5"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                                      ></path>
                                    </svg>
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <div className="mt-2">
                                <button
                                  onClick={() => {
                                    toast.loading("Generating webhook URL...")
                                    handleGenerateLink("whatsapp")
                                  }}
                                  className="flex items-center bg-[#CD0ADD] text-white px-4 py-2 rounded hover:bg-purple-700"
                                >
                                  <svg
                                    className="w-5 h-5 mr-2"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M13 10V3L4 14h7v7l9-11h-7z"
                                    ></path>
                                  </svg>
                                  Generate Webhook URL
                                </button>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabPanel>

          <TabPanel value="api-features" className="px-0">
            <div className="mb-6 font-manrope">
              <p className="text-gray-700 mb-6">Enable the features you need. Both require API credentials to function.</p>
              <div className="space-y-8">
                {/* Number Verification Feature Card */}
                <div className="border rounded-xl relative bg-white" >
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-1">
                      <div>
                        <span className="font-bold text-lg text-[#CD0ADD]">Number Verification</span>
                        <span
                          className={`ml-2 px-2 py-1 text-xs rounded font-semibold align-middle ${apiSaved.numberVerification && !apiEditing.numberVerification ? 'bg-[#D100F7] text-white' : 'bg-gray-400 text-white'}`}
                        >
                          {apiSaved.numberVerification && !apiEditing.numberVerification ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                    </div>
                    <div className="text-gray-600 mb-2 text-sm">Verify phone numbers through API integration</div>
                  </div>
                  <button
                    className={`w-full mb-4 text-left font-semibold text-black rounded-none px-6 flex items-center justify-between border-0 ${configuringFeature === 'numberVerification' ? 'bg-[rgba(205,10,221,0.26)]' : 'bg-transparent'}`}
                    onClick={() => setConfiguringFeature(configuringFeature === 'numberVerification' ? null : 'numberVerification')}
                  >
                    Configuration
                    <span className="text-xl">{configuringFeature === 'numberVerification' ? <GoChevronUp /> : <GoChevronDown />}</span>
                  </button>
                  {configuringFeature === 'numberVerification' && (
                    <div className="bg-white  px-4 py-6 rounded-b-xl">
                      <div className="flex flex-row items-center mb-4 space-x-2">
                        <div className="relative w-[120px]">
                          <select
                            className="appearance-none rounded-l-md px-2 py-2 pr-8 bg-[#D100F7] text-white font-semibold border-0 focus:outline-none w-full"
                            value={apiMethod.numberVerification}
                            onChange={e => setApiMethod(m => ({ ...m, numberVerification: e.target.value }))}
                            style={{ minWidth: '100px' }}>
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PATCH">PATCH</option>
                            <option value="DELETE">DELETE</option>
                          </select>
                          <span className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 text-white text-xl">
                            <GoChevronDown />
                          </span>
                        </div>
                        <input
                          className="flex-1 border border-gray-300 px-4 py-2 rounded-md focus:outline-none placeholder-black placeholder-opacity-80"
                          placeholder="Enter API"
                          value={apiInputs.numberVerification}
                          onChange={e => setApiInputs(i => ({ ...i, numberVerification: e.target.value }))}
                          disabled={apiSaved.numberVerification && !apiEditing.numberVerification}
                        />
                        <button
                          className={`ml-4 px-8 py-2 rounded-md text-white font-semibold ${apiSaved.numberVerification && !apiEditing.numberVerification ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#D100F7] hover:bg-[#b800d6]'}`}
                          onClick={() => handleFeatureButton('numberVerification')}
                          disabled={!apiEditing.numberVerification && !apiInputs.numberVerification}
                        >
                          {apiSaved.numberVerification && !apiEditing.numberVerification ? 'Disable' : 'Enable'}
                        </button>
                        {apiSaved.numberVerification && !apiEditing.numberVerification && (
                          <button
                            className="ml-2 px-4 py-2 rounded-md text-white font-semibold bg-[#D100F7] hover:bg-[#b800d6]"
                            onClick={() => handleFeatureDelete('numberVerification')}
                          >
                            Delete
                          </button>
                        )}
                      </div>
                      <div className="text-xs text-black mt-2">
                        *API <span className="text-[#D100F7] font-semibold">Payload</span> and <span className="text-[#D100F7] font-semibold">Response (JSON)</span> should be in this format
                      </div>
                    </div>
                  )}
                </div>
                {/* User Details Feature Card */}
                <div className="border rounded-xl relative bg-white" >
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-1">
                      <div>
                        <span className="font-bold text-lg text-[#CD0ADD]">User Details</span>
                        <span
                          className={`ml-2 px-2 py-1 text-xs rounded font-semibold align-middle ${apiSaved.userDetails && !apiEditing.userDetails ? 'bg-[#D100F7] text-white' : 'bg-gray-400 text-white'}`}
                        >
                          {apiSaved.userDetails && !apiEditing.userDetails ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                    </div>
                    <div className="text-gray-700 mb-2 text-sm">Fetch and manage user details through API</div>
                  </div>
                  <button
                    className={`w-full mb-4 text-left font-semibold px-6 text-black rounded-none flex items-center justify-between border-0 ${configuringFeature === 'userDetails' ? 'bg-[rgba(205,10,221,0.26)]' : 'bg-transparent'}`}
                    onClick={() => setConfiguringFeature(configuringFeature === 'userDetails' ? null : 'userDetails')}
                  >
                    Configuration
                    <span className="text-xl">{configuringFeature === 'userDetails' ? <GoChevronUp /> : <GoChevronDown />}</span>
                  </button>
                  {configuringFeature === 'userDetails' && (
                    <div className="bg-white px-4 py-6 rounded-b-xl">
                      <div className="flex flex-row items-center mb-4 space-x-2">
                        <div className="relative w-[120px]">
                          <select
                            className="appearance-none rounded-l-md px-2 py-2 pr-8 bg-[#D100F7] text-white font-semibold border-0 focus:outline-none w-full"
                            value={apiMethod.userDetails}
                            onChange={e => setApiMethod(m => ({ ...m, userDetails: e.target.value }))}
                            style={{ minWidth: '100px' }}>
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PATCH">PATCH</option>
                            <option value="DELETE">DELETE</option>
                          </select>
                          <span className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 text-white text-xl">
                            <GoChevronDown />
                          </span>
                        </div>
                        <input
                          className="flex-1 border border-gray-300 px-4 py-2 rounded-md focus:outline-none placeholder-black placeholder-opacity-80"
                          placeholder="Enter API"
                          value={apiInputs.userDetails}
                          onChange={e => setApiInputs(i => ({ ...i, userDetails: e.target.value }))}
                          disabled={apiSaved.userDetails && !apiEditing.userDetails}
                        />
                        <button
                          className={`ml-4 px-8 py-2 rounded-md text-white font-semibold ${apiSaved.userDetails && !apiEditing.userDetails ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#D100F7] hover:bg-[#b800d6]'}`}
                          onClick={() => handleFeatureButton('userDetails')}
                          disabled={!apiEditing.userDetails && !apiInputs.userDetails}
                        >
                          {apiSaved.userDetails && !apiEditing.userDetails ? 'Disable' : 'Enable'}
                        </button>
                        {apiSaved.userDetails && !apiEditing.userDetails && (
                          <button
                            className="ml-2 px-4 py-2 rounded-md text-white font-semibold bg-[#D100F7] hover:bg-[#b800d6]"
                            onClick={() => handleFeatureDelete('userDetails')}
                          >
                            Delete
                          </button>
                        )}
                      </div>
                      <div className="text-xs text-black mt-2">
                        *API <span className="text-[#D100F7] font-semibold">Payload</span> and <span className="text-[#D100F7] font-semibold">Response (JSON)</span> should be in this format
                      </div>
                    </div>
                  )}
                </div>
                {/* Authentication Token Section */}
                <div className="mt-10">
                  <h2 className="text-2xl font-bold text-black mb-1">Authentication</h2>
                  <p className="text-gray-700 mb-6">Enhance security for your API features.</p>
                  <div className="border rounded-xl relative bg-white" >
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-1">
                        <div>
                          <span className="font-bold text-lg text-[#CD0ADD]">Authentication Token</span>
                          <span
                            className={`ml-2 px-2 py-1 text-xs rounded font-semibold align-middle ${apiSaved.authToken && !apiEditing.authToken ? 'bg-[#D100F7] text-white' : 'bg-gray-400 text-white'}`}
                          >
                            {apiSaved.authToken && !apiEditing.authToken ? 'Enabled' : 'Disabled'}
                          </span>
                        </div>
                      </div>
                      <div className="text-gray-700 mb-2 text-sm">Enable token-based authentication for API features.</div>
                    </div>
                    {(apiSaved.numberVerification || apiSaved.userDetails) ? (
                      <button
                        className={`w-full px-6 mb-4 text-left font-semibold text-black rounded-none flex items-center justify-between border-0 ${configuringFeature === 'authToken' ? 'bg-[rgba(205,10,221,0.26)]' : 'bg-transparent'}`}
                        onClick={() => setConfiguringFeature(configuringFeature === 'authToken' ? null : 'authToken')}
                      >
                        Configuration
                        <span className="text-xl">{configuringFeature === 'authToken' ? <GoChevronUp /> : <GoChevronDown />}</span>
                      </button>
                    ) : (
                      <div className="w-full text-[#CD0ADD] px-6 mb-4 text-sm text-left font-semibold flex items-center justify-between border-0 bg-[rgba(205,10,221,0.26)]" style={{ minHeight: '40px' }}>
                        Requires at least one core feature to be enabled
                      </div>
                    )}
                    {configuringFeature === 'authToken' && (
                      <div className="bg-white  px-4 py-6 rounded-b-xl">
                        {/* get_auth_token_api field */}
                        <div className="mb-4">
                          <input
                            className="w-full border border-gray-300 px-3 py-2 rounded focus:outline-none placeholder-black placeholder-opacity-80"
                            placeholder="Enter get_auth_token_api link"
                            value={apiInputs.authToken}
                            onChange={e => setApiInputs(i => ({ ...i, authToken: e.target.value }))}
                            disabled={apiSaved.authToken && !apiEditing.authToken}
                          />
                          <div className="flex space-x-4 mt-2">
                            <button
                              className={`px-4 py-2 rounded-md text-white font-semibold ${apiSaved.authToken && !apiEditing.authToken ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#D100F7] hover:bg-[#b800d6]'}`}
                              onClick={() => handleFeatureButton('authToken')}
                              disabled={!apiEditing.authToken && !apiInputs.authToken}
                            >
                              {apiSaved.authToken && !apiEditing.authToken ? 'Disable' : 'Enable'}
                            </button>
                            {apiSaved.authToken && !apiEditing.authToken && (
                              <button
                                className="ml-2 px-4 py-2 rounded-md text-white font-semibold bg-[#D100F7] hover:bg-[#b800d6]"
                                onClick={() => handleFeatureDelete('authToken')}
                              >
                                Delete
                              </button>
                            )}
                          </div>
                        </div>
                        <div className="flex flex-row gap-4 mb-4">
                          <input
                            className="flex-1 border border-gray-300 px-3 py-2 rounded focus:outline-none placeholder-black placeholder-opacity-80"
                            placeholder="User name"
                            value={apiInputs.authTokenUsername}
                            onChange={e => setApiInputs(i => ({ ...i, authTokenUsername: e.target.value }))}
                            disabled={apiSaved.authTokenCredentials && !apiEditing.authTokenCredentials}
                          />
                          <div className="relative flex-1">
                            <input
                              className="w-full border border-gray-300 px-3 py-2 rounded focus:outline-none placeholder-black placeholder-opacity-80 pr-10"
                              placeholder="Password"
                              type={showPassword ? "text" : "password"}
                              value={apiInputs.authTokenPassword}
                              onChange={e => setApiInputs(i => ({ ...i, authTokenPassword: e.target.value }))}
                              disabled={apiSaved.authTokenCredentials && !apiEditing.authTokenCredentials}
                            />
                            <button
                              type="button"
                              className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400"
                              tabIndex={-1}
                              onClick={() => setShowPassword(v => !v)}
                            >
                              {showPassword ? (
                                // Eye open SVG
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                              ) : (
                                // Eye closed SVG
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.956 9.956 0 012.293-3.95m3.249-2.568A9.956 9.956 0 0112 5c4.478 0 8.268 2.943 9.542 7a9.973 9.973 0 01-4.293 5.03M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3l18 18" /></svg>
                              )}
                            </button>
                          </div>
                        </div>
                        {(apiEditing.authTokenCredentials || !apiSaved.authTokenCredentials) && (
                          <div className="mb-4 flex justify-end">
                            <div className="relative w-[25vw]">
                              <input
                                className="w-full border border-gray-300 px-3 py-2 rounded focus:outline-none placeholder-black placeholder-opacity-80 pr-10"
                                placeholder="Confirm Password"
                                type={showConfirmPassword ? "text" : "password"}
                                value={authFields.confirmPassword}
                                onChange={e => setAuthFields(f => ({ ...f, confirmPassword: e.target.value }))}
                              />
                              <button
                                type="button"
                                className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400"
                                tabIndex={-1}
                                onClick={() => setShowConfirmPassword(v => !v)}
                              >
                                {showConfirmPassword ? (
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                                ) : (
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.956 9.956 0 012.293-3.95m3.249-2.568A9.956 9.956 0 0112 5c4.478 0 8.268 2.943 9.542 7a9.973 9.973 0 01-4.293 5.03M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3l18 18" /></svg>
                                )}
                              </button>
                            </div>
                          </div>
                        )}
                        <div className="flex space-x-4 mb-4">
                          <button
                            className={`px-4 py-2 rounded-md text-white font-semibold ${apiSaved.authTokenCredentials && !apiEditing.authTokenCredentials ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#D100F7] hover:bg-[#b800d6]'}`}
                            onClick={handleAuthTokenCredentialsButton}
                            disabled={
                              !apiEditing.authTokenCredentials &&
                              (!apiInputs.authTokenUsername || !apiInputs.authTokenPassword)
                            }
                          >
                            {apiSaved.authTokenCredentials && !apiEditing.authTokenCredentials ? 'Disable' : 'Enable'}
                          </button>
                          {apiSaved.authTokenCredentials && !apiEditing.authTokenCredentials && (
                            <button
                              className="ml-2 px-4 py-2 rounded-md text-white font-semibold bg-[#D100F7] hover:bg-[#b800d6]"
                              onClick={() => handleFeatureDelete('authTokenCredentials')}
                            >
                              Delete
                            </button>
                          )}
                        </div>
                        <div className="text-xs text-black mt-2">
                          *API <span className="text-[#D100F7] font-semibold">Payload</span> and <span className="text-[#D100F7] font-semibold">Response (JSON)</span> should be in this format
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </TabPanel>
        </TabsBody>
      </Tabs>
    </div>
  )
}
