import React from 'react'
import {
  But<PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material'

interface LogoutDialogProps {
  isOpen: boolean
  onClose: () => void
  onLogout: () => void
  loading: boolean
}

export default function LogoutDialog({ isOpen, onClose, onLogout, loading }: LogoutDialogProps) {
  return (
    <div className="font-manrope">
      <Dialog
        open={isOpen}
        onClose={onClose}
        aria-labelledby="logout-dialog-title"
        sx={{
          '& .MuiPaper-root': {
            borderRadius: '16px',
            padding: '50px',
            minWidth: '400px',
            height: '250px',
          },
        }}
      >
        <DialogTitle
          id="logout-dialog-title"
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            fontFamily: 'Manrope, sans-serif',
            fontSize: '20px',
          }}
        >
          Want to log out?
        </DialogTitle>

        <DialogActions sx={{ justifyContent: 'center', gap: '10px', paddingBottom: '16px' }}>
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{
              borderColor: '#CD0ADD',
              color: '#CD0ADD',
              fontFamily: 'Manrope, sans-serif',
              textTransform: 'none',
              fontSize: '16px',
              '&:hover': { borderColor: '#cc00cc', backgroundColor: 'rgba(255, 0, 255, 0.1)' },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={onLogout}
            disabled={loading}
            variant="contained"
            sx={{
              backgroundColor: '#CD0ADD',
              color: 'white',
              fontFamily: 'Manrope, sans-serif',
              textTransform: 'none',
              fontSize: '16px',
              '&:hover': { backgroundColor: '#cc00cc' },
            }}
          >
            {loading ? 'Logging out...' : 'Log Out'}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  )
}
