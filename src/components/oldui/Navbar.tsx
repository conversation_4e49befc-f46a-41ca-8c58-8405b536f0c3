import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Menu, X } from 'lucide-react';
import userPool from '@/lib/userPoolConfig';
import { Button } from '@material-tailwind/react';
import { TbDeviceDesktopAnalytics } from 'react-icons/tb';
import { LuLogOut } from 'react-icons/lu';
import { BsCalendarCheck } from 'react-icons/bs';

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeLink, setActiveLink] = useState('/');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  useEffect(() => {
    const token = localStorage.getItem(process.env.NEXT_PUBLIC_COGNITO_LAST_AUTH_USER_KEY as string);
    setIsAuthenticated(!!token);
  }, []);

  const handleLinkClick = (link: string) => {
    setActiveLink(link);
    setIsMenuOpen(false);
    setIsDropdownOpen(false);
  };

  return (
    <nav className="fixed top-0 left-0 w-full z-50 bg-white lg:bg-white lg:top-0 font-manrope ">
      <div className="mx-auto flex items-center justify-between p-4 h-34">
        <div className={`space-x-2 flex text-2xl font-semibold lg:pl-8 transition-all duration-300 `}>
          <Link href="/">
            <img src="/QuickTalkLogonew.png" alt="Logo" className="h-12 md:h-[4rem] pt-1" />
          </Link>
        </div>

        <div className="text-black lg:text-black flex justify-end p-4 md:hidden">
          <button onClick={toggleMenu} className="z-50 focus:outline-none">
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        <div
          className={`font-manrope absolute left-0 pt-4 top-full z-40 w-full bg-white text-lg font-normal leading-10 md:static lg:text-white sm:text-black md:z-auto md:flex md:w-auto md:bg-transparent ${isMenuOpen ? 'block' : 'hidden'
            }`}
        >
          <div className="flex gap-2 flex-col items-center justify-center space-x-4 space-y-2 md:flex-row md:space-x-0 md:space-y-0 lg:pr-4">
            <Link href="/about">
              <button
                onClick={() => handleLinkClick('/about')}
                className={`w-full rounded px-4 text-center md:w-auto hover:bg-[#CD0ADD] hover:text-white ${activeLink === '/about' ? 'bg-[#BF00CD]' : 'text-black md:text-black'}`}
              >
                About
              </button>
            </Link>
            {/* Dropdown */}
            <div className="relative">
              <button
                onClick={toggleDropdown}
                className={`w-full rounded px-4 text-center md:w-auto hover:bg-[#CD0ADD] hover:text-white  ${isDropdownOpen ? 'bg-[#BF00CD] text-white' : 'text-black md:text-black'}`}
              >
                Solutions
              </button>
              {isDropdownOpen && (
                <div className="absolute left-0 mt-2 w-48 bg-white shadow-lg rounded-md">
                  <Link href="/Solutions?feature=Customer-Support">
                    <button
                      onClick={() => handleLinkClick('/Solutions?feature=Customer-Support')}
                      className={`block w-full px-4 py-2 text-left hover:bg-gray-100 ${activeLink === '/Solutions?feature=Customer-Support' ? 'bg-[#CD0ADD] text-white' : 'text-black'
                        }`}
                    >
                      Customer Support
                    </button>
                  </Link>
                  <Link href="/Solutions?feature=order-taking">
                    <button
                      onClick={() => handleLinkClick('/Solutions?feature=order-taking')}
                      className={`block w-full px-4 py-2 text-left hover:bg-gray-100 ${activeLink === '/Solutions?feature=order-taking' ? 'bg-[#CD0ADD] text-white' : 'text-black'
                        }`}
                    >
                      Order Taking
                    </button>
                  </Link>
                  <Link href="/Solutions?feature=lead-managment">
                    <button
                      onClick={() => handleLinkClick('/Solutions?feature=lead-managment')}
                      className={`block w-full px-4 py-2 text-left hover:bg-gray-100 ${activeLink === '/Solutions?feature=lead-managment' ? 'bg-[#CD0ADD] text-white' : 'text-black'
                        }`}
                    >
                      Lead Managment
                    </button>
                  </Link>
                  <Link href="/Solutions?feature=book-appointment">
                    <button
                      onClick={() => handleLinkClick('/Solutions?feature=book-appointment')}
                      className={`block w-full px-4 py-2 text-left hover:bg-gray-100 ${activeLink === '/Solutions?feature=book-appointment' ? 'bg-[#CD0ADD] text-white' : 'text-black'
                        }`}
                    >
                      Booking & Appointment
                    </button>
                  </Link>
                  <Link href="/Solutions?feature=sales-call">
                    <button
                      onClick={() => handleLinkClick('/Solutions?feature=sales-call')}
                      className={`block w-full px-4 py-2 text-left hover:bg-gray-100 ${activeLink === '/Solutions?feature=sales-call' ? 'bg-[#CD0ADD] text-white' : 'text-black'
                        }`}
                    >
                      Sales Call
                    </button>
                  </Link>
                </div>
              )}
            </div>

            <Link href="/blogs">
              <button
                onClick={() => handleLinkClick('/blogs')}
                className={`w-full rounded px-4 text-center md:w-auto hover:bg-[#CD0ADD] hover:text-white ${activeLink === '/blogs' ? 'bg-[#BF00CD]' : 'text-black md:text-black'}`}
              >
                Blogs
              </button>
            </Link>

            {/* <Link href="/Pricing">
              <button
                onClick={() => handleLinkClick('/Pricing')}
                className={`w-full rounded px-4 text-center md:w-auto hover:bg-[#CD0ADD] hover:text-white ${activeLink === '/Pricing' ? 'bg-[#BF00CD]' : 'text-black md:text-black'}`}
              >
                Pricing
              </button>
            </Link> */}
            <Link href="/contact">
              <button
                onClick={() => handleLinkClick('/contact')}
                className={`w-full rounded px-4 text-center md:w-auto hover:bg-[#CD0ADD] hover:text-white ${activeLink === '/contact' ? 'bg-[#BF00CD]' : 'text-black md:text-black'}`}
              >
                Contact
              </button>
            </Link>
            <div className="pb-8 lg:pb-0 flex space-x-4"> {/* Added flex container */}

              {/* Authenticated / Login button */}
              {isAuthenticated ? (
                <Link href="/Dashboard" passHref>
                  <Button
                    variant="text"
                    onClick={() => handleLinkClick('/Dashboard')}
                    size="lg"
                    className="md:font-manrope flex items-center text-black hover:text-white hover:bg-[#CD0ADD] py-3 hover:transition-all duration-300"
                  >
                    <TbDeviceDesktopAnalytics className="mr-2 text-xl" />
                    Dashboard
                  </Button>
                </Link>

              ) : (
                <Link href="/login">
                  <Button
                    variant="outlined"
                    onClick={() => handleLinkClick('/login')}
                    className="font-manrope min-w-[140px] border-[#CD0ADD] text-[#CD0ADD] hover:bg-[#CD0ADD] hover:text-white"
                  >
                    Login
                  </Button>
                </Link>
              )}
              <Link href="/demo">
                <Button className="font-manrope bg-[#CD0ADD] text-white hover:bg-[#CD0ADD]"
                  onClick={() => handleLinkClick('/demo')}
                >
                  Book A Free Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}

