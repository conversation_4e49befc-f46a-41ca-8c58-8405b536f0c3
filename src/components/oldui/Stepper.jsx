"use client"

import { useState, useEffect } from "react"
import { Card } from "@mui/material"
import toast from "react-hot-toast"
import { Play, ArrowLeft } from 'lucide-react'
import axios from "axios"
import userPool from "../../lib/userPoolConfig"
import { useNavigate } from "react-router-dom"
import { IoIosSend } from "react-icons/io"
import { FaMicrophone, FaSyncAlt } from "react-icons/fa"

const Stepper = ({ onComplete }) => {
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(1)
  const [settings, setSettings] = useState({
    logo: null,
    backgroundColor: "linear-gradient(135deg, #2A5A4B 0%, #A771BD 100%)",
    backgroundType: "gradient",
    fontColor: "white",
    wordDictionary: {},
    labelColors: {
      user: "#D3D3D3",
      assistant: "#E040FB",
    },
  })
  const [loading, setLoading] = useState(false)
  const [org_id, setOrg_id] = useState("")
  const [selectedOption, setSelectedOption] = useState("text")
  const [completionInProgress, setCompletionInProgress] = useState(false)
  const [isStep4Complete, setIsStep4Complete] = useState(false)
  const [isFirstSign, setIsFirstSign] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  
  // Add state for hex inputs
  const [hexInputs, setHexInputs] = useState({
    fontColor: "white",
    backgroundStart: "#2A5A4B",
    backgroundEnd: "#A771BD",
    userLabel: "#D3D3D3",
    assistantLabel: "#E040FB",
  })

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const user = userPool.getCurrentUser()

        if (!user) {
          console.log("No current user")
          navigate("/")
          return
        }

        const username = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY)

        user.getSession((err, session) => {
          if (err || !session.isValid()) {
            console.error("Session error or invalid session:", err)
            navigate("/")
            return
          }

          user.getUserAttributes(async (err, attributes) => {
            if (err) {
              console.error("Error fetching attributes:", err)
              return
            }

            if (!attributes) {
              console.error("User attributes are undefined")
              return
            }

            const customAttributes = attributes.filter((attr) => attr.Name.startsWith("custom:"))
            console.log("Custom attributes:", customAttributes)

            const organizationId = customAttributes.find((attr) => attr.Name === "custom:organizationId")?.Value

            if (organizationId) {
              setOrg_id(organizationId)
              console.log("organizationId:", organizationId)
            } else {
              console.error("Required custom attribute missing: organizationId")
            }
          })
        })
      } catch (error) {
        console.error("Unexpected error:", error)
      }
    }

    fetchUser()
  }, [navigate])

  // Initialize hex inputs from settings
  useEffect(() => {
    setHexInputs({
      fontColor: settings.fontColor,
      backgroundStart: settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B",
      backgroundEnd: settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD",
      userLabel: settings.labelColors.user,
      assistantLabel: settings.labelColors.assistant,
    })
  }, [])

  const handleLogoUpload = (e) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setSettings((prev) => ({ ...prev, logo: reader.result }))
      }
      reader.readAsDataURL(file)
    }
  }

  const handleColorChange = (e) => {
    const { id, value } = e.target
    setSettings((prev) => ({ ...prev, [id]: value }))
    
    // Update corresponding hex input
    if (id === "fontColor") {
      setHexInputs(prev => ({ ...prev, fontColor: value }))
    }
  }
  
  // New function to handle hex input changes
  const handleHexInputChange = (e) => {
    const { name, value } = e.target
    
    // Validate if it's a proper hex code
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    let hexValue = value
    
    // Add # prefix if missing
    if (value.length > 0 && !value.startsWith('#')) {
      hexValue = '#' + value
    }
    
    // Update the hex input state
    setHexInputs(prev => ({ ...prev, [name]: hexValue }))
    
    // Only update the actual color if it's a valid hex code
    if (hexRegex.test(hexValue)) {
      switch (name) {
        case "fontColor":
          setSettings(prev => ({ ...prev, fontColor: hexValue }))
          break
        case "backgroundStart":
          if (settings.backgroundType === "solid") {
            setSettings(prev => ({ ...prev, backgroundColor: hexValue }))
          } else {
            const endColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD"
            setSettings(prev => ({
              ...prev,
              backgroundColor: `linear-gradient(135deg, ${hexValue} 0%, ${endColor} 100%)`
            }))
          }
          break
        case "backgroundEnd":
          const startColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
          setSettings(prev => ({
            ...prev,
            backgroundColor: `linear-gradient(135deg, ${startColor} 0%, ${hexValue} 100%)`
          }))
          break
        case "userLabel":
          setSettings(prev => ({
            ...prev,
            labelColors: { ...prev.labelColors, user: hexValue }
          }))
          break
        case "assistantLabel":
          setSettings(prev => ({
            ...prev,
            labelColors: { ...prev.labelColors, assistant: hexValue }
          }))
          break
      }
    }
  }

  // Apply hex value when user presses Enter
  const handleHexKeyPress = (e) => {
    if (e.key === 'Enter') {
      const { name, value } = e.target
      // Ensure # is present
      const hexValue = value.startsWith('#') ? value : '#' + value
      
      // Apply the same logic as in handleHexInputChange
      switch (name) {
        case "fontColor":
          setSettings(prev => ({ ...prev, fontColor: hexValue }))
          break
        case "backgroundStart":
          if (settings.backgroundType === "solid") {
            setSettings(prev => ({ ...prev, backgroundColor: hexValue }))
          } else {
            const endColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD"
            setSettings(prev => ({
              ...prev,
              backgroundColor: `linear-gradient(135deg, ${hexValue} 0%, ${endColor} 100%)`
            }))
          }
          break
        case "backgroundEnd":
          const startColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
          setSettings(prev => ({
            ...prev,
            backgroundColor: `linear-gradient(135deg, ${startColor} 0%, ${hexValue} 100%)`
          }))
          break
        case "userLabel":
          setSettings(prev => ({
            ...prev,
            labelColors: { ...prev.labelColors, user: hexValue }
          }))
          break
        case "assistantLabel":
          setSettings(prev => ({
            ...prev,
            labelColors: { ...prev.labelColors, assistant: hexValue }
          }))
          break
      }
    }
  }

  const goBack = () => {
    if (showPreview) {
      setShowPreview(false)
      return
    }

    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1)
    }
  }

  const completeSetup = async () => {
    setLoading(true)
    try {
      const savedUsername = localStorage.getItem(
        import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY
      )

      if (!savedUsername) {
        toast.error("User session not found. Please login again.")
        navigate("/")
        return
      }

      const firstSignResponse = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/update_first_sign`, {
        username: savedUsername,
      })

      if (!firstSignResponse.data.success) {
        throw new Error("Failed to update first sign status")
      }

      setCompletionInProgress(true)
      toast.success("Setup completed successfully!")

      setTimeout(() => {
        if (onComplete) {
          onComplete()
        } else {
          navigate("/Dashboard")
        }
      }, 1500)
    } catch (error) {
      console.error("Error completing setup:", error)
      toast.error(error.message || "Error completing setup.")
    } finally {
      setLoading(false)
    }
  }

  const saveAndNext = async () => {
    setLoading(true)
    const savedUsername = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY)

    if (!savedUsername) {
      toast.error("User session not found. Please login again.")
      navigate("/")
      return
    }

    try {
      let specificData = {}
      switch (currentStep) {
        case 1:
          specificData = { logo: settings.logo }
          break
        case 2:
          specificData = { background_color: settings.backgroundColor, backgroundType: settings.backgroundType }
          break
        case 3:
          specificData = { font_color: settings.fontColor }
          break
        case 4:
          specificData = { labelColors: settings.labelColors }
          
          try {
            const response = await axios({
              method: "POST",
              url: `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/settings/set`,
              data: {
                organisation_id: org_id,
                label_colors: settings.labelColors,
              },
              headers: {
                "Content-Type": "application/json",
              },
            })

            if (response.status === 200) {
              toast.success("Label colors updated successfully")
              setCurrentStep(5)
              setLoading(false)
              return
            }
          } catch (error) {
            console.error("API Error:", error)
            throw new Error(error.response?.data?.message || "Failed to update label colors")
          }
          break
        case 5:
          if (!org_id) {
            throw new Error("Organization ID is required")
          }

          try {
            console.log("Sending request to update reply mode:", {
              organisationId: org_id,
              option: selectedOption,
            })
            const response = await axios({
              method: "POST",
              url: `${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/settings/reply_option/update`,
              data: {
                organisationId: org_id,
                service:"frontend_voice_setting",
                options: selectedOption,
              },
              headers: {
                "Content-Type": "application/json",
              },
            })

            console.log("API Response:", response)

            if (response.status >= 200 && response.status < 300) {
              toast.success("Reply mode updated successfully")
              setIsStep4Complete(true)
              
              const firstSignResponse = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/update_first_sign`, {
                username: savedUsername,
              })

              if (firstSignResponse.data.success) {
                setIsFirstSign(true)
                setShowPreview(true)
              } else {
                throw new Error("Failed to update first sign status")
              }

              setLoading(false)
              return
            } else {
              throw new Error(`Unexpected response status: ${response.status}`)
            }
          } catch (error) {
            console.error("API Error:", error)
            toast.error(error.response?.data?.message || "Failed to update reply mode")
            setLoading(false)
            throw error
          }
          break
      }

      if (Object.keys(specificData).length > 0) {
        const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/settings/set`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ organisation_id: org_id, ...specificData }),
        })

        if (!response.ok) throw new Error("Failed to save settings")
      }

      toast.success("Settings saved successfully!")
      setCurrentStep((prev) => prev + 1)
    } catch (error) {
      console.error("Error in saveAndNext:", error)
      toast.error(error.message || "Error saving settings.")
    } finally {
      setLoading(false)
    }
  }

  const skipStep = async () => {
    if (currentStep === 5) {
      try {
        const savedUsername = localStorage.getItem(
          import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY
        )

        if (!savedUsername) {
          toast.error("User session not found. Please login again.")
          navigate("/")
          return
        }

        // Update isFirstSign flag to true
        const firstSignResponse = await axios.post(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/update_first_sign`, {
          username: savedUsername,
        })

        if (!firstSignResponse.data.success) {
          throw new Error("Failed to update first sign status")
        }

        setCompletionInProgress(true)

        // Call the completion callback or redirect
        if (onComplete) {
          onComplete()
        } else {
          navigate("/Dashboard")
        }
      } catch (error) {
        console.error("Error in skipStep:", error)
        toast.error(error.message || "Error updating settings.")
      }
    } else {
      // For other steps, just increment the step
      setCurrentStep((prev) => prev + 1)
    }
  }

  const ChatPreview = () => {
    const renderReplyContent = () => {
      switch (selectedOption) {
        case "voice":
          return (
            <div className="flex flex-col gap-2 max-w-xs font-manrope">
              <div className="flex items-center gap-2 bg-purple-500 text-white p-2 rounded-full w-60">
                <Play className="w-5 h-5 cursor-pointer" />
                <div className="w-full h-1 bg-white rounded-full relative">
                  <div className="h-1 bg-gray-300 w-1/3 rounded-full"></div>
                </div>
                <span className="text-xs bg-purple-700 px-2 py-1 rounded-full">Yo</span>
              </div>
            </div>
          )
        case "both":
          return (
            <div className="flex flex-col gap-2 max-w-xs">
              <div className="flex items-center gap-2 bg-purple-500 text-white p-2 rounded-full w-60">
                <Play className="w-5 h-5 cursor-pointer" />
                <div className="w-full h-1 bg-white rounded-full relative">
                  <div className="h-1 bg-gray-300 w-1/3 rounded-full"></div>
                </div>
                <span className="text-xs bg-purple-700 px-2 py-1 rounded-full">Yo</span>
              </div>
              <div className="bg-purple-500 p-2 rounded-xl w-fit" style={{ color: settings.fontColor }}>
                Hi, how may I assist you?
              </div>
            </div>
          )
        case "text":
        default:
          return <p style={{ color: settings.fontColor }}>Hello! I&apos;m your AI assistant. How can I help you today?</p>;
      }
    }

    return (
      <div
        className="w-[420px] h-[440px] rounded-xl overflow-hidden font-manrope"
        style={{
          background: settings.backgroundColor,
        }}
      >
        <div className="p-4 flex items-center gap-3">
          {settings.logo ? (
            <img
              src={settings.logo || "/placeholder.svg"}
              alt="Company Logo"
              className="w-8 h-8 rounded-full object-contain border-2 border-black bg-white"
            />
          ) : (
            <div className="w-8 h-8 bg-[#E429F2] rounded-full border-2 border-black"></div>
          )}
          <div
            className="max-w-[80%] rounded-2xl rounded-tl-sm px-4 py-2"
            style={{
              backgroundColor: settings.labelColors.assistant,
              color: settings.fontColor,
            }}
          >
            {renderReplyContent()}
          </div>
        </div>

        <div className="p-4 space-y-4">
          <div className="flex justify-end items-center gap-2">
            <div
              className="max-w-[80%] rounded-lg rounded-tr-sm px-4 py-2"
              style={{
                backgroundColor: settings.labelColors.user,
                color: settings.fontColor,
              }}
            >
              <p>I have a question about my order</p>
            </div>
            <div className="w-8 h-8 bg-white rounded-full"></div>
          </div>
        </div>
      </div>
    )
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Upload Your Company Logo</h2>
            <div className="flex flex-col md:flex-row gap-8">
              <div className="space-y-4 min-w-[300px] ">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="border border-gray-300 p-2 rounded-lg w-80"
                />
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Choose Your Background</h2>
            <div className="flex flex-col md:flex-row gap-8">
              <div className="space-y-4 min-w-[300px]">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Background Type</label>
                  <div className="flex gap-4">
                    <label className="flex items-center space-x-3 p-3 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                      <input
                        type="radio"
                        name="backgroundType"
                        value="solid"
                        checked={settings.backgroundType === "solid"}
                        onChange={() => {
                          const solidColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
                          setSettings((prev) => ({
                            ...prev,
                            backgroundType: "solid",
                            backgroundColor: solidColor,
                          }))
                          setHexInputs(prev => ({
                            ...prev,
                            backgroundStart: solidColor
                          }))
                        }}
                        className="w-4 h-4 text-purple-600"
                      />
                      <span className="text-sm font-medium text-gray-900">Solid Color</span>
                    </label>

                    <label className="flex items-center space-x-3 p-3 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                      <input
                        type="radio"
                        name="backgroundType"
                        value="gradient"
                        checked={settings.backgroundType === "gradient"}
                        onChange={() => {
                          const startColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
                          const endColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD"
                          setSettings((prev) => ({
                            ...prev,
                            backgroundType: "gradient",
                            backgroundColor: `linear-gradient(135deg, ${startColor} 0%, ${endColor} 100%)`,
                          }))
                          setHexInputs(prev => ({
                            ...prev,
                            backgroundStart: startColor,
                            backgroundEnd: endColor
                          }))
                        }}
                        className="w-4 h-4 text-purple-600"
                      />
                      <span className="text-sm font-medium text-gray-900">Gradient</span>
                    </label>
                  </div>
                </div>

                {settings.backgroundType === "solid" ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Color</label>
                    <div className="flex items-center gap-3">
                      <input
                        type="color"
                        value={
                          settings.backgroundColor.startsWith("#")
                            ? settings.backgroundColor
                            : settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
                        }
                        onChange={(e) => {
                          setSettings((prev) => ({
                            ...prev,
                            backgroundColor: e.target.value,
                          }))
                          setHexInputs(prev => ({
                            ...prev,
                            backgroundStart: e.target.value
                          }))
                        }}
                        className="w-32 h-32"
                      />
                      <div className="flex flex-col gap-2">
                        <label className="block text-sm font-medium text-gray-700">Hex Code</label>
                        <input
                          type="text"
                          name="backgroundStart"
                          value={hexInputs.backgroundStart}
                          onChange={handleHexInputChange}
                          onKeyPress={handleHexKeyPress}
                          className="border border-gray-300 p-2 rounded-lg w-full"
                          placeholder="#2A5A4B"
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Start Color</label>
                      <div className="flex items-center gap-3">
                        <input
                          type="color"
                          value={settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"}
                          onChange={(e) => {
                            const endColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD"
                            setSettings((prev) => ({
                              ...prev,
                              backgroundColor: `linear-gradient(135deg, ${e.target.value} 0%, ${endColor} 100%)`,
                            }))
                            setHexInputs(prev => ({
                              ...prev,
                              backgroundStart: e.target.value
                            }))
                          }}
                          className="w-32 h-32"
                        />
                        <div className="flex flex-col gap-2">
                          <label className="block text-sm font-medium text-gray-700">Hex Code</label>
                          <input
                            type="text"
                            name="backgroundStart"
                            value={hexInputs.backgroundStart}
                            onChange={handleHexInputChange}
                            onKeyPress={handleHexKeyPress}
                            className="border border-gray-300 p-2 rounded-lg w-full"
                            placeholder="#2A5A4B"
                          />
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">End Color</label>
                      <div className="flex items-center gap-3">
                        <input
                          type="color"
                          value={settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[1] || "#A771BD"}
                          onChange={(e) => {
                            const startColor = settings.backgroundColor.match(/#[a-zA-Z0-9]{6}/g)?.[0] || "#2A5A4B"
                            setSettings((prev) => ({
                              ...prev,
                              backgroundColor: `linear-gradient(135deg, ${startColor} 0%, ${e.target.value} 100%)`,
                            }))
                            setHexInputs(prev => ({
                              ...prev,
                              backgroundEnd: e.target.value
                            }))
                          }}
                          className="w-32 h-32"
                        />
                        <div className="flex flex-col gap-2">
                          <label className="block text-sm font-medium text-gray-700">Hex Code</label>
                          <input
                            type="text"
                            name="backgroundEnd"
                            value={hexInputs.backgroundEnd}
                            onChange={handleHexInputChange}
                            onKeyPress={handleHexKeyPress}
                            className="border border-gray-300 p-2 rounded-lg w-full"
                            placeholder="#A771BD"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Choose Your Font Color</h2>
            <div className="flex flex-col md:flex-row gap-8">
              <div className="space-y-4 min-w-[300px]">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Select Font Color</label>
                  <div className="flex items-center gap-4">
                    <input
                      type="color"
                      id="fontColor"
                      value={settings.fontColor}
                      onChange={handleColorChange}
                      className="w-20 h-20 cursor-pointer"
                    />
                    <div className="flex flex-col gap-2">
                      <label className="block text-sm font-medium text-gray-700">Hex Code</label>
                      <input
                        type="text"
                        name="fontColor"
                        value={hexInputs.fontColor}
                        onChange={handleHexInputChange}
                        onKeyPress={handleHexKeyPress}
                        className="border border-gray-300 p-2 rounded-lg w-full"
                        placeholder="#FFFFFF"
                      />
                    </div>
                  </div>
                  <div className="mt-2">
                    <span className="text-sm font-medium" style={{ color: settings.fontColor }}>
                      Preview Text Color
                    </span>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Colors</h3>
                  <div className="grid grid-cols-5 gap-2">
                    {["#FFFFFF", "#000000", "#E429F2", "#4A90E2", "#F5A623"].map((color) => (
                      <div
                        key={color}
                        className="w-10 h-10 rounded-full cursor-pointer border border-gray-300 hover:scale-110 transition-transform"
                        style={{ backgroundColor: color }}
                        onClick={() => {
                          setSettings((prev) => ({ ...prev, fontColor: color }))
                          setHexInputs(prev => ({ ...prev, fontColor: color }))
                        }}
                      ></div>
                    ))}
                  </div>
                </div>

                <div className="mt-6">
                  <div className="p-4 rounded-lg border border-gray-200 bg-gray-50">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Font Color Tips</h3>
                    <ul className="text-xs text-gray-600 space-y-1">
                      <li>• Choose high contrast colors for better readability</li>
                      <li>• White or light colors work best on dark backgrounds</li>
                      <li>• Dark colors work best on light backgrounds</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Customize Label Colors</h2>
            <div className="space-y-6">
            <div className="space-y-4">
                <h3 className="text-lg font-medium">Response Label Color</h3>
                <div className="flex items-center gap-4">
                  <input
                    type="color"
                    value={settings.labelColors.assistant}
                    onChange={(e) => {
                      setSettings((prev) => ({
                        ...prev,
                        labelColors: {
                          ...prev.labelColors,
                          assistant: e.target.value,
                        }
                      }))
                      setHexInputs(prev => ({
                        ...prev,
                        assistantLabel: e.target.value
                      }))
                    }}
                    className="w-20 h-20 cursor-pointer"
                  />
                  <div className="flex flex-col gap-2">
                    <label className="block text-sm font-medium text-gray-700">Hex Code</label>
                    <input
                      type="text"
                      name="assistantLabel"
                      value={hexInputs.assistantLabel}
                      onChange={handleHexInputChange}
                      onKeyPress={handleHexKeyPress}
                      className="border border-gray-300 p-2 rounded-lg w-full"
                      placeholder="#E040FB"
                    />
                  </div>
                </div>
                <div 
                  className="px-4 py-2 rounded-lg max-w-fit"
                  style={{ backgroundColor: settings.labelColors.assistant, color: settings.fontColor }}
                >
                  Assistant message preview
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Question Label Color</h3>
                <div className="flex items-center gap-4">
                  <input
                    type="color"
                    value={settings.labelColors.user}
                    onChange={(e) => {
                      setSettings((prev) => ({
                        ...prev,
                        labelColors: {
                          ...prev.labelColors,
                          user: e.target.value,
                        }
                      }))
                      setHexInputs(prev => ({
                        ...prev,
                        userLabel: e.target.value
                      }))
                    }}
                    className="w-20 h-20 cursor-pointer"
                  />
                  <div className="flex flex-col gap-2">
                    <label className="block text-sm font-medium text-gray-700">Hex Code</label>
                    <input
                      type="text"
                      name="userLabel"
                      value={hexInputs.userLabel}
                      onChange={handleHexInputChange}
                      onKeyPress={handleHexKeyPress}
                      className="border border-gray-300 p-2 rounded-lg w-full"
                      placeholder="#D3D3D3"
                    />
                  </div>
                </div>
                <div 
                  className="px-4 py-2 rounded-lg max-w-fit"
                  style={{ backgroundColor: settings.labelColors.user, color: settings.fontColor }}
                >
                  User message preview
                </div>
              </div>
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6 font-manrope">
            <h2 className="text-2xl mb-8">Configure Reply Settings</h2>
            <div className="w-full max-w-md mx-auto">
              <h3 className="text-lg font-medium mb-4">Select preferred reply mode for your bot</h3>

              <div className="space-y-4">
                <label className="flex items-center space-x-3 p-4 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="replyMode"
                    value="text"
                    onChange={() => setSelectedOption("text")}
                    checked={selectedOption === "text"}
                    className="w-4 h-4 text-purple-600"
                  />
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-900">Text</span>
                  </div>
                </label>

                <label className="flex items-center space-x-3 p-4 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="replyMode"
                    value="voice"
                    onChange={() => setSelectedOption("voice")}
                    checked={selectedOption === "voice"}
                    className="w-4 h-4 text-purple-600"
                  />
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-900">Voice</span>
                  </div>
                </label>

                <label className="flex items-center space-x-3 p-4 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="replyMode"
                    value="both"
                    onChange={() => setSelectedOption("both")}
                    checked={selectedOption === "both"}
                    className="w-4 h-4 text-purple-600"
                  />
                  <div className="flex items-center space-x-3">
                    <div className="flex gap-1"></div>
                    <span className="text-sm font-medium text-gray-900">Both</span>
                  </div>
                </label>
              </div>
              
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="font-manrope">
      {(currentStep > 1 || showPreview) && (
        <button onClick={goBack} className="flex items-center gap-2 text-black hover:text-gray-900 mb-4 ml-4">
          <ArrowLeft className="w-10 h-10" />
        </button>
      )}

      <Card className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold text-center mb-12">
          {showPreview ? "Preview" : "A FEW MORE STEPS AND YOUR QUICKTALK IS READY"}
        </h1>
        <div className="flex flex-col lg:flex-row gap-16 items-start flex-1">
          <div className="flex-shrink-0 w-[420px] h-[570px]">
            <ChatPreview />
          </div>
          <div className="flex-grow max-w-sm w-full">
            {renderStep()}
          </div>
        </div>

        <div className="flex justify-end gap-4 mt-8 pt-6">
          {showPreview ? (
            <>
              <button
                onClick={goBack}
                className="px-6 py-2 text-[#CD0ADD] border border-[#CD0ADD] rounded-md hover:text-[#f20ea2]"
              >
                Go Back
              </button>
              <button
                onClick={completeSetup}
                disabled={loading}
                className="px-6 py-2 bg-[#E429F2] text-white rounded-md hover:bg-[#D11ADF] flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  "Done"
                )}
              </button>
            </>
          ) : (
            <>
              <button
                onClick={skipStep}
                className="px-12 py-2 text-[#CD0ADD] border border-[#CD0ADD] rounded-md hover:text-[#f20ea2]"
                disabled={loading}
              >
                Skip
              </button>
              <button
                onClick={saveAndNext}
                disabled={loading}
                className="px-6 py-2 bg-[#E429F2] text-white rounded-md hover:bg-[#D11ADF] flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  "Save & Next"
                )}
              </button>
            </>
          )}
        </div> 
      </Card>   
    </div>
  )  
}

export default Stepper
