import React, { useState } from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardBody,
    Typography,
    Button,
    IconButton,
    Collapse,
    Spinner,
    Dialog,
    DialogHeader,
    DialogBody,
    DialogFooter,
    Input,
    Textarea,
    Checkbox,
} from "@material-tailwind/react";
import { Chev<PERSON>Down, Pencil, Trash, Plus } from "lucide-react";

const AddAttributeDialog = ({ isOpen, onClose, onSubmit, functionId, loading }) => {
    const [attributeData, setAttributeData] = useState({
        name: "",
        type: "",
        description: "",
        required: false,
    });

    const handleChange = (e) => {
        const { id, value, type, checked } = e.target;
        setAttributeData(prev => ({
            ...prev,
            [id]: type === "checkbox" ? checked : value
        }));
    };

    const handleSubmit = () => {
        onSubmit(functionId, attributeData);
        setAttributeData({
            name: "",
            type: "",
            description: "",
            required: false,
        });
    };

    return (
        <Dialog open={isOpen} handler={onClose} size="md">
            <DialogHeader className="font-manrope">Add New Attribute</DialogHeader>
            <DialogBody divider>
                <div className="space-y-4">
                    <Input
                        type="text"
                        label="Attribute Name"
                        id="name"
                        value={attributeData.name}
                        onChange={handleChange}
                    />
                    <Input
                        type="text"
                        label="Type"
                        id="type"
                        value={attributeData.type}
                        onChange={handleChange}
                    />
                    <Textarea
                        label="Description"
                        id="description"
                        value={attributeData.description}
                        onChange={handleChange}
                    />
                    <Checkbox
                        label="Required"
                        id="required"
                        checked={attributeData.required}
                        onChange={handleChange}
                    />
                </div>
            </DialogBody>
            <DialogFooter>
                <Button variant="text" color="red" onClick={onClose} className="mr-2 bg-[#CD0ADD] text-white hover:bg-purple-700 font-manrope">
                    Cancel
                </Button>
                <Button 
                className="bg-[#CD0ADD] text-white hover:bg-purple-700 font-manrope"
                    variant="filled" 
                    color="blue" 
                    onClick={handleSubmit}
                    disabled={loading || !attributeData.name || !attributeData.type || !attributeData.description}
                >
                    {loading ? <Spinner className="h-4 w-4" /> : "Add Attribute"}
                </Button>
            </DialogFooter>
        </Dialog>
    );
};

export default AddAttributeDialog

