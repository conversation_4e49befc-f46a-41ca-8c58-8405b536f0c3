import React, { useState, useEffect, useRef } from "react";
import { PlusCircle, Globe, PhoneCall, LineChart, Share2, Settings, CreditCard } from "lucide-react";

const TechFeatureShowcase = ({ onScrollEnd }) => {
  const [activeFeature, setActiveFeature] = useState(0);
  const [isSticky, setIsSticky] = useState(false);
  const [shouldRelease, setShouldRelease] = useState(false);
  const showcaseRef = useRef(null);
  const containerRef = useRef(null);
  const isInitialized = useRef(false);
  const lastScrollTime = useRef(Date.now());
  const scrollTimeout = useRef(null);

  const features = [
    { title: "Effortless Setup", icon: <PlusCircle className="h-6 w-6" />, color: "bg-[#003344]" },
    { title: "Multilingual, Global Reach", icon: <Globe className="h-6 w-6" />, color: "bg-[#003344]" },
    { title: "Inbound & Outbound Calls", icon: <PhoneCall className="h-6 w-6" />, color: "bg-[#003344]" },
    { title: "Insightful Analytics", icon: <LineChart className="h-6 w-6" />, color: "bg-[#003344]" },
    { title: "Omnichannel Integration", icon: <Share2 className="h-6 w-6" />, color: "bg-[#003344]" },
    { title: "Customizable to Your Workflow", icon: <Settings className="h-6 w-6" />, color: "bg-[#003344]" },
    { title: "Pay-As-You-Go", icon: <CreditCard className="h-6 w-6" />, color: "bg-[#003344]" }
  ];

  useEffect(() => {
    if (!isInitialized.current) {
      window.scrollTo(0, 0);
      setActiveFeature(0);
      isInitialized.current = true;
    }
  }, []);

  useEffect(() => {
    let ticking = false;

    const updateActiveFeature = (scrollPosition, containerHeight) => {
      const segmentSize = containerHeight / (features.length - 1);
      const rawIndex = Math.round(scrollPosition / segmentSize);
      const boundedIndex = Math.max(0, Math.min(rawIndex, features.length - 1));
      
      if (boundedIndex !== activeFeature) {
        // Add debouncing for smooth transitions
        const currentTime = Date.now();
        const timeSinceLastScroll = currentTime - lastScrollTime.current;
        
        if (timeSinceLastScroll > 100) { // Minimum time between updates
          setActiveFeature(boundedIndex);
          lastScrollTime.current = currentTime;
        } else {
          // Clear existing timeout
          if (scrollTimeout.current) {
            clearTimeout(scrollTimeout.current);
          }
          
          // Set new timeout to ensure we don't miss the final position
          scrollTimeout.current = setTimeout(() => {
            setActiveFeature(boundedIndex);
            lastScrollTime.current = Date.now();
          }, 50);
        }
      }
    };

    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          if (containerRef.current && showcaseRef.current) {
            const containerRect = containerRef.current.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const containerHeight = containerRect.height - viewportHeight;
            const scrollPosition = Math.abs(containerRect.top);
            
            if (containerRect.top <= 0 && !shouldRelease) {
              setIsSticky(true);
              updateActiveFeature(scrollPosition, containerHeight);
            } else {
              setIsSticky(false);
            }

            if (scrollPosition >= containerHeight * 1) {
              setShouldRelease(true);
              setIsSticky(false);
              setActiveFeature(features.length - 1);
              onScrollEnd && onScrollEnd();
            } else {
              setShouldRelease(false);
            }
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => {
      window.removeEventListener("scroll", handleScroll);
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [activeFeature, features.length, shouldRelease, onScrollEnd]);

  // Rest of your component remains the same...
  const getFeaturePosition = (index) => {
    const totalFeatures = features.length;
    const rotationOffset = (activeFeature * (360 / totalFeatures));
    let angle = ((index * (360 / totalFeatures)) - 90 - rotationOffset) * (Math.PI / 180);
    
    const radius = 32;
    return {
      left: 50 + radius * Math.cos(angle),
      top: 50 + radius * Math.sin(angle),
      rotation: ((index * (360 / totalFeatures)) - rotationOffset)
    };
  };

  return (
    <div 
      ref={containerRef} 
      className="font-manrope relative overflow-hidden" 
      style={{ height: `${window.innerHeight * 4.5}px` }}
    >
      <div 
        ref={showcaseRef} 
        className={`flex flex-col items-center w-full max-w-7xl mx-auto py-16 px-4 transition-all duration-200
                   ${isSticky ? 'fixed top-[-140px] left-0 right-0' : ''} 
                   ${shouldRelease ? 'absolute bottom-0 left-0 right-0' : ''}`}
      >
        <h2 className="text-5xl font-semibold text-center mb-16">
          More Than Just A Regular Customer Support Agent
        </h2>

        <div className="relative w-full max-w-4xl aspect-square">
          <div className="absolute inset-0 flex items-center justify-center">
            <img src="/Circle.svg" alt="Technological ring" className="w-[600px] animate-move" />
          </div>

          {features.map((feature, index) => {
            const position = getFeaturePosition(index);
            const adjustedIndex = (index - activeFeature + features.length) % features.length;
            const isActive = adjustedIndex === 0;

            return (
              <div
                key={index}
                className="absolute transform -translate-x-1/2 -translate-y-1/2 transition-all duration-700 ease-in-out"
                style={{ 
                  left: `${position.left}%`, 
                  top: `${position.top}%`,
                  transform: `translate(-50%, -50%) scale(${isActive ? 1.1 : 1})`,
                  zIndex: isActive ? 2 : 1
                }}
              >
                <div 
                  className="relative cursor-pointer transition-transform duration-700"
                  style={{
                    transform: `rotate(${-position.rotation}deg)`
                  }}
                >
                  <div className={`w-36 h-36 flex flex-col items-center justify-center p-2 rounded-full 
                                  transition-all duration-300
                                  ${isActive ? "bg-white border-2 border-[#CD0ADD] shadow-lg" : "bg-transparent"}`}>
                    <div className={`p-3 rounded-full ${feature.color} text-white ${isActive ? 'mb-2' : ''}`}>
                      {feature.icon}
                    </div>
                    {isActive && (
                      <span className="text-center font-semibold text-purple-900 text-sm">
                        {feature.title}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}

          <div className="font-manrope absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
            <div className="bg-white bg-opacity-90 rounded-full w-2/5 h-2/5 flex items-center justify-center shadow-lg">
              <div className="text-center p-6">
                <div className={`p-3 rounded-full ${features[activeFeature].color} mx-auto mb-3 w-16 h-16 flex items-center justify-center`}>
                  {features[activeFeature].icon}
                </div>
                <h3 className="text-xl font-bold text-[#003344] mb-2">
                  {features[activeFeature].title}
                </h3>
                <p className="text-4xl text-[#CD0ADD]">
                  QuickTalk
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TechFeatureShowcase;