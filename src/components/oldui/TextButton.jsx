import { Button } from "@material-tailwind/react";
import Link from "next/link";
export default function TextButton() {
  return (
    <div className="font-manrope flex flex-col items-center justify-center text-center space-y-6 px-4 py-12 ">
      <h1 className="text-5xl md:text-4xl font-semibold">
        Unbeatable Value, Unmatched Prices
      </h1>
      <p className="text-lg">
        Get started with{" "}
        <span className="text-[#CD0ADD]">QuickTalk</span> today
      </p>
      <div className="flex gap-4">
        {/* Sign Up Button */}
        <Link href="/login">
        <Button
          variant="outlined"
          className="font-manrope border-[#CD0ADD] text-[#CD0ADD] hover:bg-[#CD0ADD] hover:text-white"
        >
          Sign Up
        </Button>
        </Link>
        {/* View Pricing Button */}
        <Link href="/Pricing">
        <Button
          variant="filled"
          className="font-manrope bg-[#CD0ADD] hover:bg-[#CD0ADD]"
        >
          View Pricing
        </Button>
        </Link>
      </div>
    </div>
  );
}
