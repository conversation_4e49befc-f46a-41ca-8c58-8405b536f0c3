import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material'
import { ClipLoader } from "react-spinners"

interface LogoutDialogProps {
  isOpen: boolean
  onClose: () => void
  onLogout: () => void
  loading: boolean
}

export default function LogoutDialog({ isOpen, onClose, onLogout, loading }: LogoutDialogProps) {
  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      aria-labelledby="logout-dialog-title"
      aria-describedby="logout-dialog-description"
    >
      <DialogTitle id="logout-dialog-title">
        Confirm Logout
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="logout-dialog-description">
          Are you sure you want to log out? This action cannot be undone.
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button 
          onClick={onLogout} 
          disabled={loading} 
          color="secondary" 
          variant="contained"
        >
          {loading ? <ClipLoader size={20} color={'#ffffff'} /> : 'Logout'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}