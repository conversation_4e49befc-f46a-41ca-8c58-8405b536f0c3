
import Header from '../Header';
import InputForm from './InputForm';
import TopicSelection from './TopicSelection';
import MessageList from './MessageList';
import WavEncoder from 'wav-encoder';
import { CognitoUserSession } from 'amazon-cognito-identity-js';
import userPool from '@/lib/userPoolConfig';
import LogoutDialog from '../LogoutDialog';
import AdminButton from '../AdminButton';
import { useRef, useState, useEffect } from 'react';
import axios from 'axios';
import { Message } from '@/types/chat';
import { Document } from 'langchain/document';
import { useRouter } from 'next/router';
import toast from 'react-hot-toast';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
export default function Home() {
  const [recorder, setRecorder] = useState<MediaRecorder | null>(null);
  const [recording, setRecording] = useState<boolean>(false);
  const [audioURL, setAudioURL] = useState<string | null>(null);
  const [detectedLanguage, setDetectedLanguage] = useState('');
  const [autoSubmit, setAutoSubmit] = useState(false);
  const [audioURL1, setAudioURL1] = useState<string | null>(null);
  const [query, setQuery] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [isMobile, setIsMobile] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileUrl, setFileUrl] = useState('');
  const [configData, setConfigData] = useState<any>(null);
  const [clientid, setClientid] = useState(uuidv4());
  const [username, setUsername] = useState<string | null>(null);
  const [isWhisperCalled, setIsWhisperCalled] = useState(true);
  const [inputType, setInputType] = useState('text');
  const [options, setOptions] = useState<Instance[]>([]);
  const [selectedOption, setSelectedOption] = useState('');
  const [logo, setLogo] = useState(''); // State to hold the logo URL
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [background, setBackground] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [fontcolor, setFontcolor] = useState('');
  const [ismicro, setMicro] = useState(false);
  const [isOtherResponseHandled, setIsOtherResponseHandled] = useState(false);
  const [useSecondApi, setUseSecondApi] = useState(false);
  const router = useRouter();
  interface Instance {
    instance_id: string;
    display_name: string;
  }
  const toggleChatHistory = () => {
    window.open('/history', '_blank');
  };

  process.env['GOOGLE_CLOUD_CREDENTIALS'] = path.join(
    __dirname,
    '/pages/config1.json',
  );

  const [messageState, setMessageState] = useState<{
    messages: Message[];
    pending?: string;
    history: [string, string][];
    pendingSourceDocs?: Document[];
  }>({
    messages: [
      {
        message: 'How can i assist you?',
        type: 'apiMessage',
      },
    ],
    history: [],
  });

  const { messages, history } = messageState;

  const messageListRef = useRef<HTMLDivElement>(null);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (autoSubmit) {
      // Create a fake Event object and pass it to handleSubmit
      const mockEvent = {
        preventDefault: () => { },
      } as React.FormEvent<HTMLFormElement>; // Explicitly typing as HTMLFormElement
      handleSubmit(mockEvent);

      // Reset autoSubmit flag
      setAutoSubmit(false);
    }
  }, [query]);


  useEffect(() => {
    // Fetch the config.json file
    axios.get('/config.json').then((response) => {
      setConfigData(response.data);
    });
  }, []);

  const fetchLogo = async (savedUsername: string) => {

    if (!savedUsername) {
      console.error('No username found in localStorage');
      return;
    }

    try {
      const response = await fetch('/api/Setting/settings-get', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username: savedUsername }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }

      const data = await response.json();
      // Assuming the logo_base64 is in the 'logo_base64' field of the response
      setLogo(data.logo_base64);
      setBackground(data.background_color);
      setFontcolor(data.font_color);
      console.log(data.logo_base64); // Log the base64 string for debugging
      console.log(data.background_color); // Log the base64 string for debugging
    } catch (error) {
      console.error('Error fetching logo:', error);
    }
  };

  const checkAdminStatus = async (username: string) => {
    if (!username) {
      setError('Username not found. Please login again.');
      return;
    }

    try {
      const response = await axios.post('/api/checkAdmin', { username });
      setIsAdmin(response.data.admin);
    } catch (err) {
      console.error('Error checking admin status:', err);
      toast.error('Failed to verify admin status. Please try again.');
    }
  };

  const saveCustomAttributes = async (username: string,
    organizationId: string,
    admin: string) => {
    try {
      const response = await axios.post('/api/saveCustomAttributes', {
        username,
        admin,
        organizationId,
      });
      console.log('Data saved:', response.data);
    } catch (error) {
      console.error('Error saving custom attributes:', error);
    }
  };

  useEffect(() => {
    const user = userPool.getCurrentUser();

    if (!user) {
      console.log('No current user');
      router.push('/'); // Redirect to login if no user is found
      return;
    }
    const username = localStorage.getItem(
      'CognitoIdentityServiceProvider.24ira6svu3j9t2qbls9s4cgr6t.LastAuthUser',
    );

    user.getSession((err: Error | null, session: CognitoUserSession | null) => {
      if (err) {
        if (err instanceof Error) { // Narrowing down the type of `err`
          console.error('Session error or invalid session:', err.message);
        } else {
          console.error('Unknown error:', err);
        }
        return;
      }

      if (!session?.isValid()) {
        console.error('Invalid session');
        return;
      }


      // Retrieve user attributes after validating session
      user.getUserAttributes((err: unknown, attributes) => { // Typing `err` as `unknown`
        if (err) {
          console.error('Error fetching attributes:', err);
          return;
        }
        if (!attributes) {
          console.error('User attributes are undefined');
          return;
        }

        // Filter out custom attributes
        const customAttributes = attributes.filter((attr) =>
          attr.Name.startsWith('custom:'),
        );
        console.log('Custom attributes:', customAttributes);

        // Extract organizationId and namespace from custom attributes
        const organizationId = customAttributes.find(
          (attr) => attr.Name === 'custom:organizationId',
        )?.Value;
        const admin = customAttributes.find(
          (attr) => attr.Name === 'custom:isAdmin',
        )?.Value || 'true'; // Always send 'true'  
        console.log('organizationId:', organizationId);
        console.log('admin:', admin);

        // Check if required custom attributes are available
        if (organizationId && admin) {
          // Get access token
          if (username) {
            saveCustomAttributes(username, organizationId, admin);
          } else {
            console.error('No saved username available');
          }
        } else {
          console.error('Required custom attributes missing');
        }
      });
    });

    // Retrieve and save username locally
    if (username) {
      localStorage.setItem('username', username);
      setUsername(username);
      setIsLoading(false);
      fetchOptions(username);
    } else {
      setIsLoading(true);
    }
  }, []);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const savedUsername = localStorage.getItem(
          'CognitoIdentityServiceProvider.24ira6svu3j9t2qbls9s4cgr6t.LastAuthUser'
        );
        if (!savedUsername) {
          console.error('No username found in localStorage');
          return;
        }

        // Fetch Logo first
        await fetchLogo(savedUsername);

        // Fetch Options and Admin Status after Logo is fetched
        await fetchOptions(savedUsername);
        await checkAdminStatus(savedUsername);

        setIsLoading(false); // Set loading false after all async actions complete
      } catch (error) {
        console.error('Error fetching data:', error);
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 600); // Adjust the threshold as needed
    };

    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  const fetchOptions = async (username: string) => {
    try {
      const response = await fetch('/api/pineconeinstance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Ins', data);
        setOptions(data.instances || []); // Assuming your API returns an array in 'instances'
        // Auto-select the first instance if available
        if (data.instances && data.instances.length > 0) {
          setSelectedOption(data.instances[0].instance_id); // Auto-select the first instance
        }
      } else {
        console.error('Failed to fetch options:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching options:', error);
    }
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedOption(event.target.value);
  };

  /*
  const logout = () => {
    localStorage.clear();
    router.push('/');
  };

  */

  const logout = async () => {
    setLoading(true);
    const user = userPool.getCurrentUser();

    if (user) {
      console.log('Signing out user:', user.getUsername());
      localStorage.removeItem('username');
      localStorage.removeItem('email');
      user.signOut();
      window.location.href = '/';
    } else {
      console.log('No user to sign out');
    }
  };
  if (isLoading) {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          fontSize: '20px',
          color: '#444',
          background: '#f2f2f2',
          textAlign: 'center',
          padding: '50px',
        }}
      >
        You are not allowed to visit Quicktalk without authentication...
        <button
          onClick={() => router.push('/')}
          style={{
            marginTop: '20px',
            fontSize: '18px',
            padding: '10px 20px',
            cursor: 'pointer',
          }}
        >
          Go to Login Page Please!
        </button>
      </div>
    );
  }

  // Rest of your component here...

  // Rest of your component here
  //handle form submission
  // const getUserInput = async (question: string) => {
  //   try {
  //     const response = await axios.post(
  //       "https://d717-124-109-40-174.ngrok-free.app/gets_user_input",
  //       { client_query: question },
  //       {
  //         headers: {
  //           'Content-Type': 'application/json'
  //         }
  //       }
  //     );
  //     console.log("Received Response:", response.data); // Log the actual response data
  //     return response.data;
  //   } catch (error) {
  //     throw new Error("Error fetching user input: " + error.message);
  //   }
  // };

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setError(null);
    setInputType("text");

    if (!query) {
      toast.error("Please input a question");
      return;
    }

    const question = query.trim();
    console.log("User Question:", question);

    setMessageState((state) => ({
      ...state,
      messages: [...state.messages, { type: "userMessage", message: question }],
    }));
    setLoading(true);
    setQuery(""); // Reset the input field

    try {
      // if (!useSecondApi) {
      //   const userInputResponse = await getUserInput(question);
      //   console.log("User Input Response:", userInputResponse.response);

      //   if (userInputResponse.response !== "other") {
      //     setMessageState((state) => ({
      //       ...state,
      //       messages: [
      //         ...state.messages,
      //         { type: "apiMessage", message: userInputResponse.response },
      //       ],
      //     }));
      //     toast.success("Response received successfully!");
      //     setLoading(false);
      //     return;
      //   } else {
      //     setUseSecondApi(true);
      //   }
      // }

      // Handle the second API call (previously the "other" flow)
      if (!selectedOption) {
        setError("Please select an option before ingesting.");
        toast.error("Please select an option before ingesting.");
        setLoading(false);
        return;
      }

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          question,
          history,
          username,
          clientid,
          selectedInstanceId: selectedOption,
          type: inputType
        }),
      });

      const data = await response.json();
      console.log("Additional API Response:", data);

      if (data.error) {
        setError(data.error);
        toast.error(`Error: ${data.error}`);
        return;
      }

      setMessageState((state) => ({
        ...state,
        messages: [
          ...state.messages,
          { type: "apiMessage", message: data.text, sourceDocs: data.sourceDocuments },
        ],
        history: [...state.history, [question, data.text]],
      }));

      toast.success("Processed response successfully!");
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message);
        toast.error("Error processing your request: " + error.message); // Safely access error.message
      }
      else {
        setError("An unknown error occurred");  // Handle other types of errors
      }
    } finally {
      setLoading(false);
      messageListRef.current?.scrollTo(0, messageListRef.current.scrollHeight);
    }
  }



  // Prevent empty submissions
  const handleEnter = (e: any) => {
    if (e.key === "Enter" && query) {
      handleSubmit(e);
    } else if (e.key == "Enter") {
      e.preventDefault();
    }
  };

  const refreshContext = async () => {
    setClientid(uuidv4());
    setAudioURL1(''); // Clear the audio URL
    setUseSecondApi(false);
    setIsOtherResponseHandled(false);
    setIsWhisperCalled(false);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: '', // You can set this to the desired question or leave it empty
          history: [],
          clientid: clientid,
        }),
      });

      const data = await response.json();
      console.log('data and client id is', data);

      if (data.error) {
        setError(data.error);
        toast.error("Error: " + data.error);
      } else {
        // Send refresh word to configData.whisper API
        const refreshResponse = await fetch(configData?.refresh, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ text: 'refresh' }),
        });

        const refreshData = await refreshResponse.json();
        console.log('Refresh API response from configData:', refreshData);
        toast.success("Refreshed context successfully!");

        // Send refresh word to external API at ngrok endpoint
        const externalResponse = await axios.post(
          'https://7b75-124-109-40-174.ngrok-free.app/gets_user_input',
          { client_query: 'refresh' },
          { headers: { 'Content-Type': 'application/json' } }
        );

        console.log('Refresh API response from ngrok:', externalResponse.data);
        toast.success("Sent refresh word to external API successfully!");
        setMessageState((prevState) => ({
          messages: [
            {
              type: 'apiMessage',
              message: data.text,
              sourceDocs: data.sourceDocuments,
            },
          ],
          history: [], // Keep the current history
        }));
      }

      setLoading(false);

      // Scroll to bottom
      messageListRef.current?.scrollTo(0, messageListRef.current.scrollHeight);
    } catch (error) {
      setLoading(false);
      setError(
        'An error occurred while refreshing the context. Please try again.',
      );
      toast.error("Error refreshing context: ");
      console.error('Error:', error);
    }
  };

  const handleMicClick = async () => {
    if (recording) {
      // Stop recording
      recorder?.stop();
      setRecorder(null);
      setRecording(false);
      setInputType('voice');
      setMicro(false);
    } else {
      // Check for microphone access
      if (!navigator.mediaDevices?.getUserMedia) {
        toast.error('Microphone access not supported');
        return;
      }

      setMicro(true);

      try {
        // Get microphone stream and start recording
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        const newRecorder = new MediaRecorder(stream);
        const chunks: Blob[] = []; // Explicitly define chunks as an array of Blob

        newRecorder.ondataavailable = (e: BlobEvent) => {
          chunks.push(e.data);
        };

        newRecorder.onstop = async () => {
          // Create a single blob from the recorded audio chunks
          const blob = new Blob(chunks, { type: 'audio/webm' });

          // Convert the blob to ArrayBuffer
          const arrayBuffer = await blob.arrayBuffer();

          // Create an AudioContext
          const audioContext = new (window.AudioContext ||
            window.webkitAudioContext)();

          // Resume audio context if it is suspended
          if (audioContext.state === 'suspended') {
            await audioContext.resume();
          }

          // Decode audio data from the WebM format
          try {
            const decodedData = await audioContext.decodeAudioData(arrayBuffer);

            // Prepare the data for WAV encoding
            const wavData = {
              sampleRate: decodedData.sampleRate,
              channelData: Array.from(
                { length: decodedData.numberOfChannels },
                (_, i) => decodedData.getChannelData(i),
              ),
            };

            // Encode to WAV format
            const wavBuffer: ArrayBuffer = await WavEncoder.encode(wavData); // This returns an ArrayBuffer
            const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' }); // Create Blob from ArrayBuffer

            const reader = new FileReader();

            reader.onloadend = async () => {
              const base64data = reader.result?.toString().split(',')[1]; // Ensure result is a string

              console.log('Base64 Audio Data:', base64data);

              // Show loading toast while fetching data
              toast.promise(
                (async () => {
                  const sentAudioURL = URL.createObjectURL(wavBlob);
                  setMessageState((state) => ({
                    ...state,
                    messages: [
                      ...state.messages,
                      {
                        type: 'userMessage',
                        message: 'Input Voice Audio',
                        audioURL: sentAudioURL, // Store the audio URL
                      },
                    ],
                  }));
                  const response = await fetch(configData?.whisper, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ audio: base64data, type: 'voice' }),
                  });
                  const data = await response.json();

                  // Handle the response containing the audio in WebM format
                  const responseAudio = data.voice_message_base64;
                  const audioBlob = new Blob(
                    [
                      new Uint8Array(
                        atob(responseAudio)
                          .split('')
                          .map((c) => c.charCodeAt(0)),
                      ),
                    ],
                    { type: 'audio/webm' },
                  );
                  const audioUrl = URL.createObjectURL(audioBlob);

                  setAudioURL(audioUrl);
                  console.log('Received audio URL:', audioUrl);

                  if (data.error) {
                    setError(data.error);
                    throw new Error(data.error);
                  } else {
                    // Update message state with received audio
                    setMessageState((state) => ({
                      ...state,
                      messages: [
                        ...state.messages,
                        {
                          type: 'apiMessage',
                          message: 'Fetched Voice Output',
                          audioURL: audioUrl,
                          sourceDocs: data.sourceDocuments,
                        },
                      ],
                    }));
                  }
                })(),
                {
                  loading: 'Fetching voice data...',
                  success: 'Voice fetched successfully!',
                  error: 'Failed to fetch voice data. Try again.',
                },
              );
            };

            reader.readAsDataURL(wavBlob); // This should now correctly read the WAV blob
          } catch (error) {
            console.error('Error decoding audio data:', error);
            toast.error('Failed to decode audio data.');
          }
        };

        newRecorder.start();
        setRecorder(newRecorder);
        setRecording(true);
      } catch (error) {
        console.error('Error accessing microphone:', error);
        toast.error('Failed to access microphone.');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Header Section */}
      {/* <Header
        isMobile={isMobile}
        toggleChatHistory={toggleChatHistory}
        setIsModalOpen={setIsModalOpen}
      /> */}


      {/* Logout Dialog */}
      <LogoutDialog
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onLogout={logout}
        loading={loading}
      />

      {/* Main Content Section */}
      <main className="sm:px-6 w-full pt-5 lg:max-w-4xl mx-auto px-4 px-8 lg:pt-20 lg:w-1/2">
        {/* Show Admin Button only for Admin and non-Mobile view */}
        {isAdmin && !isMobile && (
          <AdminButton />
        )}

        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          {/* Topic Selection (only visible for non-mobile) */}
          {!isMobile && (
            <TopicSelection
              options={options}
              selectedOption={selectedOption}
              handleChange={handleChange}
            />
          )}

          {/* Message List */}
          <MessageList
            messages={messages}
            messageListRef={messageListRef}
            background={background}
            fontcolor={fontcolor}
            isMobile={isMobile}
            logo={logo}
          />

          {/* Input Form Section */}
          <InputForm
            loading={loading}
            handleSubmit={handleSubmit}
            handleEnter={handleEnter}
            query={query}
            setQuery={setQuery}
            refreshContext={refreshContext}
            handleMicClick={handleMicClick}
            ismicro={ismicro}
          />

        </div>

        {/* Footer */}
        {/* <Footer /> */}
      </main>
    </div>
  );
};