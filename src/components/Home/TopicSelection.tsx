import React from 'react'
import { FaInfoCircle, FaCheckCircle } from 'react-icons/fa'

interface Option {
  instance_id: string
  display_name: string
}

interface TopicSelectionProps {
  options: Option[]
  selectedOption: string
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void
}

export default function TopicSelection({ options = [], selectedOption, handleChange }: TopicSelectionProps) {
  return (
    <div className="p-4 sm:p-6">
      <h3 className="text-lg font-semibold mb-4 flex items-center">
        Select your Topic
        <span className="ml-2 group relative">
          <FaInfoCircle className="text-blue-500 cursor-pointer" />
          <span className="absolute left-0 sm:left-full ml-2 w-48 p-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity">
            Choose an index from the available options to chat with the bot for your answers.
          </span>
        </span>
      </h3>
      {/* <div className="flex flex-wrap gap-2 sm:gap-4">
        {options.map((instance) => (
          <label key={instance.instance_id} className="flex items-center mb-2">
            <input
              type="radio"
              value={instance.instance_id}
              checked={selectedOption === instance.instance_id}
              onChange={handleChange}
              className="sr-only"
            />
            <span
              className={`px-3 py-2 text-sm sm:px-4 sm:py-2 rounded-full border-2 cursor-pointer transition-colors ${
                selectedOption === instance.instance_id
                  ? 'bg-blue-500 text-white border-blue-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
              }`}
            >
              <FaCheckCircle
                className={`inline-block mr-1 sm:mr-2 ${
                  selectedOption === instance.instance_id ? 'text-white' : 'text-blue-500'
                }`}
              />
              {instance.display_name}
            </span>
          </label>
        ))}
      </div> */}
    </div>
  )
}