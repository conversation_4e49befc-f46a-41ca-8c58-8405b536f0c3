import React, { useState, useEffect } from "react";
import { IoIosSend } from "react-icons/io";
import { FaMicrophone, FaSyncAlt } from "react-icons/fa";
import { MdClose } from "react-icons/md";
import LoadingDots from "@/components/ui/LoadingDots";
import { Phone } from "lucide-react";

interface InputFormProps {
  loading: boolean;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  handleEnter: (e: React.KeyboardEvent) => void;
  query: string;
  setQuery: (query: string) => void;
  refreshContext: () => void;
  handleMicClick: (action: 'start' | 'stop' | 'cancel') => void;
  ismicro: boolean;
  background?: string;
  textfield?: string;
  onCallClick?: () => void;
  showCallButton?: boolean; // Add this line
}

// Utility functions for background handling
const isGradient = (bg?: string): boolean => {
  return typeof bg === "string" && bg.includes("linear-gradient");
};

const getContrastColor = (background: string): string => {
  if (isGradient(background)) {
    // For gradients, extract the final color stop
    const matches = background.match(/#[a-fA-F0-9]{6}/g);
    if (matches && matches.length > 0) {
      const lastColor = matches[matches.length - 1];
      return getContrastingColor(lastColor);
    }
  }
  return getContrastingColor(background);
};

const getContrastingColor = (hexColor?: string): string => {
  if (!hexColor) return "#000000"; // Default fallback

  // Ensure hexColor is a valid string
  const hex = hexColor.replace("#", "");

  if (hex.length !== 6) return "#000000"; // Fallback for invalid colors

  // Convert to RGB
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  return luminance > 0.5 ? "#000000" : "#ffffff";
};

const getInputBackground = (background: string): string => {
  if (isGradient(background)) {
    return 'rgba(255, 255, 255, 0.9)';
  }
  return background === '#ffffff' ? '#ffffff' : 'rgba(255, 255, 255, 0.9)';
};

export default function InputForm({
  loading,
  handleSubmit,
  handleEnter,
  query,
  setQuery,
  refreshContext,
  handleMicClick,
  ismicro,
  background = "white",
  textfield,
  onCallClick,
  showCallButton = false, // Add this line
}: InputFormProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recordingInterval, setRecordingInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!ismicro) {
      setIsRecording(false);
      setRecordingTime(0);
      if (recordingInterval) {
        clearInterval(recordingInterval);
        setRecordingInterval(null);
      }
    }
  }, [ismicro, recordingInterval]);

  const startRecording = () => {
    setIsRecording(true);
    const interval = setInterval(() => {
      setRecordingTime((prev) => prev + 1);
    }, 1000);
    setRecordingInterval(interval);
    handleMicClick('start');
  };

  const stopRecording = (action: 'stop' | 'cancel') => {
    setIsRecording(false);
    if (recordingInterval) {
      clearInterval(recordingInterval);
      setRecordingInterval(null);
    }
    setRecordingTime(0);
    handleMicClick(action);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Compute styles based on background
  const parsedTextfield = textfield ? JSON.parse(textfield) : {};
const assistantColor = parsedTextfield.assistant || "#CD0ADD"; 
  const inputBg = getInputBackground(background);
  const textColor = "#000000"; 
  const buttonBgColor = assistantColor;
  const buttonHoverColor = "#B000A0";

  return (
    <div 
      className="p-4 pb-8 flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2"
      style={{ 
        background: background,
        transition: "background 0.3s ease"
      }}
    >
      {!isRecording ? (
        <>
          <form onSubmit={handleSubmit} className="relative w-full">
            <div className="relative w-full">
              <textarea
                disabled={loading}
                onKeyDown={handleEnter}
                rows={1}
                maxLength={500}
                id="userInput"
                name="userInput"
                placeholder={loading ? "Waiting for response..." : "Ask anything?"}
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="w-full resize-none p-3 pr-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                style={{
                  backgroundColor: inputBg,
                  color: textColor||'#000000',
                  borderColor: 'rgba(0, 0, 0, 0.1)',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                }}
              />
              <button
                type="submit"
                disabled={loading}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors duration-200"
                style={{
                  color: buttonBgColor,
                }}
              >
                {loading ? <LoadingDots color={textColor} /> : <IoIosSend size={30} />}
              </button>
            </div>
          </form>

          <div className="flex space-x-2">
            <button
              type="button"
              onClick={startRecording}
              disabled={loading}
              className="w-12 h-12 flex items-center justify-center rounded-full shadow-lg text-white transition-all disabled:opacity-50"
              style={{
                backgroundColor: buttonBgColor,
                transition: "background-color 0.2s ease",
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = buttonHoverColor;
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = buttonBgColor;
              }}
            >
              <FaMicrophone size={20} />
            </button>
            {/* Call button right after mic button */}
            {showCallButton && onCallClick && (
              <button
                type="button"
                onClick={onCallClick}
                disabled={loading}
                className="w-12 h-12 flex items-center justify-center rounded-full shadow-lg text-white transition-all disabled:opacity-50 ml-2"
                style={{
                  backgroundColor: buttonBgColor,
                  transition: "background-color 0.2s ease",
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = buttonHoverColor;
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = buttonBgColor;
                }}
                title="Start Call"
              >
                <Phone size={20} />
              </button>
            )}
            <button
              type="button"
              onClick={refreshContext}
              className="w-12 h-12 flex items-center justify-center rounded-full shadow-lg text-white transition-all"
              style={{
                backgroundColor: buttonBgColor,
                transition: "background-color 0.2s ease",
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = buttonHoverColor;
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = buttonBgColor;
              }}
            >
              <FaSyncAlt size={20} />
            </button>
          </div>
        </>
      ) : (
        <div 
          className="w-full flex items-center justify-between p-4 rounded-lg"
          style={{
            backgroundColor: 'rgba(243, 244, 246, 0.9)',
            backdropFilter: 'blur(4px)'
          }}
        >
          <div className="flex items-center space-x-4">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
            <span className="font-medium" style={{ color: textColor }}>
              {formatTime(recordingTime)}
            </span>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => stopRecording('cancel')}
              className="p-2 hover:opacity-80 transition-opacity"
              style={{ color: textColor }}
            >
              <MdClose size={24} />
            </button>
            <button
              onClick={() => stopRecording('stop')}
              className="p-2 hover:opacity-80 transition-opacity"
              style={{ color: buttonBgColor }}
            >
              <IoIosSend size={24} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}