"use client"

import type React from "react"
import { User } from "lucide-react"
import ReactMarkdown from "react-markdown"
import ReactAudioPlayer from "react-audio-player"
import Image from "next/image"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

interface Message {
  type: "apiMessage" | "userMessage"
  message: string
  audioURL?: string
  sourceDocs?: Array<{
    pageContent: string
    metadata: {
      fileUrl?: string
      source?: string
    } & Record<string, any>
  }>
  button_values?: string[]
}

interface MessageListProps {
  messages: Message[]
  messageListRef: React.RefObject<HTMLDivElement>
  background?: string
  fontcolor?: string
  logo?: string
  isMobile: boolean
  onButtonClick?: (value: string) => void
  textfield?: string // Add textfield prop to receive color JSON
}

// Utility to check if a string is a valid gradient
const isGradient = (value: string): boolean => {
  return value.includes("linear-gradient")
}

// Utility to get contrasting text color based on background
const getContrastingColor = (bgColor: string): string => {
  // For gradients, default to white text
  if (isGradient(bgColor)) return "#ffffff"

  // For hex colors, calculate contrast
  const hex = bgColor.replace("#", "")
  const r = Number.parseInt(hex.substring(0, 2), 16)
  const g = Number.parseInt(hex.substring(2, 4), 16)
  const b = Number.parseInt(hex.substring(4, 6), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000

  return brightness > 128 ? "#000000" : "#ffffff"
}

export default function MessageList({
  fontcolor = "black",
  background = "black",
  messages,
  messageListRef,
  logo,
  isMobile,
  onButtonClick,
  textfield,
}: MessageListProps) {
  // Process background value
  const processedBackground = background || "white"
  const isGradientBg = isGradient(processedBackground)
  // Parse the textfield JSON to get color values
  let userBubbleColor = "#D3D3D3"
  let apiBubbleColor = "#e040fb"

  try {
    if (textfield) {
      const colorConfig = JSON.parse(textfield)
      if (colorConfig.user) {
        userBubbleColor = colorConfig.user
      }
      if (colorConfig.assistant) {
        apiBubbleColor = colorConfig.assistant
      }
    }
  } catch (error) {
    console.error("Error parsing textfield JSON:", error)
    // Fallback to default colors if parsing fails
  }

  // Set text colors as requested
  const apiTextColor = fontcolor || getContrastingColor(apiBubbleColor)
  const userTextColor = fontcolor || getContrastingColor(userBubbleColor)

  return (
    <div
      ref={messageListRef}
      className="flex-grow overflow-y-auto p-4 pt-12 lg:h-[calc(100vh-200px)]"
      style={{
        background: processedBackground,
        transition: "background-color 0.3s ease",
      }}
    >
      {messages.map((message, index) => (
        <div
          key={`chatMessage-${index}`}
          className={`flex items-start gap-3 pb-3 ${message.type === "apiMessage" ? "justify-start" : "justify-end"}`}
        >
          {message.type === "apiMessage" && (
            <div className="relative flex-shrink-0 w-12 h-12 rounded-full border-2 overflow-hidden flex items-center justify-center">
              {logo ? (
                <Image
                  src={logo || "/placeholder.svg"}
                  alt="Bot Avatar"
                  width={40}
                  height={40}
                  className="object-fit w-full"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement
                    target.src = "/placeholder.svg"
                  }}
                />
              ) : (
                <div className="w-full h-full rounded-full bg-gray-200" />
              )}
            </div>
          )}

          <div
            className={`flex flex-col space-y-2 max-w-[80%] p-3 shadow-lg rounded-lg break-words`}
            style={{
              backgroundColor: message.type === "apiMessage" ? apiBubbleColor : userBubbleColor,
              color: message.type === "apiMessage" ? apiTextColor : userTextColor,
              overflowWrap: "break-word",
              wordWrap: "break-word",
              wordBreak: "break-word",
            }}
          >
            <div className="break-words flex flex-col space-y-2 min-w-[200px]">
              {message.message && <ReactMarkdown className="text-sm leading-relaxed" components={{
                p: ({ node, ...props }) => <p style={{ overflowWrap: "break-word", wordWrap: "break-word" }} {...props} />
              }}>{message.message}</ReactMarkdown>}

              {message.audioURL && (
                <div className="w-full min-w-[200px]">
                  <ReactAudioPlayer src={message.audioURL} controls className="w-full" style={{ minWidth: "200px" }} />
                </div>
              )}

              {/* Button values rendering */}
              {message.button_values && message.button_values.length > 0 && (
                <div className="grid grid-cols-3 sm:grid-cols-3 gap-2 mt-3 w-full">
                  {message.button_values.map((buttonValue, buttonIndex) => (
                    <button
                      key={`button-${buttonIndex}`}
                      onClick={() => onButtonClick && onButtonClick(buttonValue)}
                      className="px-4 py-2 text-sm font-medium rounded-lg transition-colors"
                      style={{
                        backgroundColor: "#ffffff",
                        color: apiBubbleColor,
                        border: `1px solid ${apiBubbleColor}`,
                        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                      }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.backgroundColor = apiBubbleColor
                        e.currentTarget.style.color = "#ffffff"
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.backgroundColor = "#ffffff"
                        e.currentTarget.style.color = apiBubbleColor
                      }}
                    >
                      {buttonValue}
                    </button>
                  ))}
                </div>
              )}

              {!isMobile && message.sourceDocs && (
                <Accordion type="single" collapsible className="w-full">
                  {message.sourceDocs.map((doc, docIndex) => (
                    <AccordionItem key={`messageSourceDocs-${docIndex}`} value={`item-${docIndex}`}>
                      <AccordionTrigger
                        className="text-xs font-medium"
                        style={{ color: message.type === "apiMessage" ? apiTextColor : userTextColor }}
                      >
                        Source {docIndex + 1}
                      </AccordionTrigger>
                      <AccordionContent
                        className="text-xs"
                        style={{ color: message.type === "apiMessage" ? apiTextColor : userTextColor }}
                      >
                        <ReactMarkdown >{doc.pageContent}</ReactMarkdown>
                        <p className="mt-2">
                          <b>Source: </b>
                          <a
                            href={doc.metadata.fileUrl}
                            className="text-blue-300 hover:underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {doc.metadata?.source?.split("\\").pop()?.split("/").pop() ?? "Unknown source"}
                          </a>
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              )}
            </div>
          </div>

          {message.type === "userMessage" && (
            <div className="flex-shrink-0 w-11 h-11 rounded-full border-2 border-[#D3D3D3] flex items-center justify-center bg-white">
              <User className="w-8 h-8 text-gray-500" />
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

