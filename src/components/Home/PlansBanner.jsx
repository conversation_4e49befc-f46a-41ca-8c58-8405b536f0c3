import React, { useState } from 'react';

export default function PlansBanner({ onBillingChange }) {
    const [billing, setBilling] = useState('monthly'); // Track the selected billing option

    const handleBillingChange = (option) => {
        setBilling(option); // Update the selected billing option
        onBillingChange(option); // Call the parent function to handle external state (if necessary)
    };

    return (
        <div className="font-manrope pt-24 relative w-full h-[70vh] lg:h-[80vh] bg-gradient-to-b from-[#033035] via-[#0C3330] to-[#183C1E] flex items-center justify-center px-4">
            <div className="text-center flex flex-col items-center">
                <h1 className="text-3xl md:text-6xl text-white font-bold mb-6">
                    Pay As You Go
                </h1>

                <p className="max-w-3xl text-white text-xl md:text-2xl mb-12">
                    Whether you&apos;re an individual or business enterprise, QuickTalk&apos;s flexible pricing model allows you to only pay for what you need.
                </p>

                <div className="flex gap-x-1">
                    <button
                        className={`px-9 py-3 rounded-l-lg font-semibold text-lg transition-all duration-300 
              ${billing === 'monthly' ? 'bg-[#BF00CD] text-white' : 'bg-white text-black hover:bg-gray-100'}`}
                        onClick={() => handleBillingChange('monthly')}
                    >
                        Monthly Billing
                    </button>
                    <button
                        className={`px-9 py-3 rounded-r-lg font-semibold text-lg transition-all duration-300 
              ${billing === 'yearly' ? 'bg-[#BF00CD] text-white' : 'bg-white text-black hover:bg-gray-100'}`}
                        onClick={() => handleBillingChange('yearly')}
                    >
                        Yearly Billing
                    </button>
                </div>
            </div>
        </div>
    );
}
