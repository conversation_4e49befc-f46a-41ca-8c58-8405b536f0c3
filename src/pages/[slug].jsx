"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom"
import axios from "axios"
import { ArrowLeft, Calendar, User, Share2, Facebook, Twitter, Linkedin, Mail, Link as LinkIcon, Check } from "lucide-react"
import Squareload from '../components/oldui/Squareload';
import BlogBody from "../components/oldui/BlogBody"
import Header from '@/components/sections/Header';
import Footer from '@/components/sections/Footer';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

function BlogPost() {
  const { slug } = useParams()
  const [blogData, setBlogData] = useState(null)
  const [authorData, setAuthorData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showCopied, setShowCopied] = useState(false)

  useEffect(() => {
    if (slug) {
      fetchBlogData()
    }
  }, [slug])

  const fetchBlogData = async () => {
    try {
      const response = await axios.get(`${import.meta.env.VITE_PUBLIC_BLOG_URL || 'https://your-blog-api-url.com/api/blogs'}/${slug}`)
      const blog = response.data.data
      const formattedDate = new Date(blog.createdAt).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })

      setBlogData({
        title: blog.title,
        body: blog.body,
        introduction: blog.introduction,
        conclusion: blog.conclusion,
        tag: blog.blog_tag,
        mainImg: blog.blog_main_img.url,
        author: blog.author_name.name,
        blogDate: formattedDate,
      })

      await getAuthor(blog.author_name.slug)
      setLoading(false)
    } catch (error) {
      console.error("Error fetching blog data:", error)
      setLoading(false)
    }
  }

  const getAuthor = async (name) => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_PUBLIC_BLOG_AUTHOR || 'https://your-blog-api-url.com/api/authors'}?filters[slug][$eq]=${name}&populate[picture][fields][0]=alternativeText&populate[picture][fields][1]=url`,
      )
      setAuthorData(response.data.data[0])
    } catch (error) {
      console.error("Error fetching author data:", error)
    }
  }

  const handleShare = (platform) => {
    const url = window.location.href
    const title = blogData?.title || "Check out this blog post"

    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
      email: `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(url)}`,
    }

    if (platform === "copy") {
      navigator.clipboard.writeText(url).then(() => {
        setShowCopied(true)
        setTimeout(() => setShowCopied(false), 2000)
      })
    } else {
      window.open(shareUrls[platform], "_blank", "width=600,height=400")
    }
  }

  const ShareButton = ({ platform, icon: Icon, label, onClick }) => (
    <button
      onClick={onClick}
      className="flex items-center space-x-2 p-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors text-white"
    >
      <Icon className="w-5 h-5" />
      <span className="text-sm">{label}</span>
    </button>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400"></div>
              <span className="text-white text-lg">Loading article...</span>
            </div>
          </div>
        ) : (
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <Link to="/blogs" className="inline-flex items-center space-x-2 text-purple-200 hover:text-purple-100 transition-colors mb-8">
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Blog</span>
            </Link>

            {/* Article Header */}
            <article className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden mb-8">
              {/* Hero Image */}
              <div className="h-64 md:h-96 w-full overflow-hidden">
                <img
                  src={blogData?.mainImg || "/placeholder.svg"}
                  alt={blogData?.title}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Article Info */}
              <div className="p-6 md:p-8">
                <Badge className="mb-4 bg-purple-500/20 text-purple-200 border-purple-400">
                  {blogData?.tag}
                </Badge>
                
                <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  {blogData?.title}
                </h1>
                
                <div className="flex items-center space-x-6 text-gray-300 text-sm mb-6">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4" />
                    <span>{blogData?.author}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4" />
                    <span>{blogData?.blogDate}</span>
                  </div>
                </div>

                {/* Share Buttons */}
                <div className="flex flex-wrap gap-3">
                  <ShareButton
                    platform="twitter"
                    icon={Twitter}
                    label="Twitter"
                    onClick={() => handleShare("twitter")}
                  />
                  <ShareButton
                    platform="facebook"
                    icon={Facebook}
                    label="Facebook"
                    onClick={() => handleShare("facebook")}
                  />
                  <ShareButton
                    platform="linkedin"
                    icon={Linkedin}
                    label="LinkedIn"
                    onClick={() => handleShare("linkedin")}
                  />
                  <ShareButton
                    platform="email"
                    icon={Mail}
                    label="Email"
                    onClick={() => handleShare("email")}
                  />
                  <ShareButton
                    platform="copy"
                    icon={showCopied ? Check : LinkIcon}
                    label={showCopied ? "Copied!" : "Copy Link"}
                    onClick={() => handleShare("copy")}
                  />
                </div>
              </div>
            </article>

            {/* Article Content */}
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 md:p-8 mb-8">
              <div className="prose prose-purple max-w-none text-white [&>*]:text-white [&_p]:text-white [&_h1]:text-white [&_h2]:text-white [&_h3]:text-white [&_h4]:text-white [&_h5]:text-white [&_h6]:text-white [&_li]:text-white [&_strong]:text-white [&_em]:text-white [&_blockquote]:text-white [&_a]:text-purple-300 [&_a:hover]:text-purple-200">
                <BlogBody
                  body={blogData?.body}
                  introduction={blogData?.introduction}
                  conclusion={blogData?.conclusion}
                />
              </div>
            </div>

            {/* Author Section */}
            {authorData && authorData?.length !== 0 && blogData?.author !== 'Najoomi Press' && (
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                <div className="flex items-center space-x-4">
                  <img
                    src={authorData?.picture?.url || "/placeholder.svg"}
                    alt={authorData?.picture?.alternativeText || authorData?.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  <div>
                    <h3 className="text-xl font-bold text-white">{authorData?.name}</h3>
                    <p className="text-gray-300">{authorData?.introduction}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  )
}

export default BlogPost