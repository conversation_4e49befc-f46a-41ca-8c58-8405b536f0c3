import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { Search, Calendar, User, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Link } from 'react-router-dom';
import Header from '@/components/sections/Header';
import Footer from '@/components/sections/Footer';

function Blogs() {
  const [loading, setLoading] = useState(true);
  const [searchBlog, setSearchBlog] = useState('');
  const [blogs, setBlogs] = useState([]);
  const [filteredBlogs, setFilteredBlogs] = useState([]);

  const API_URL = import.meta.env.VITE_PUBLIC_BLOG_URL || 'https://your-blog-api-url.com/api/blogs';

  useEffect(() => {
    fetchBlogs();
  }, []);

  useEffect(() => {
    const filtered = searchBlog
      ? blogs.filter(blog =>
        blog.title.toLowerCase().includes(searchBlog.toLowerCase()) ||
        blog.blog_tag?.toLowerCase().includes(searchBlog.toLowerCase())
      )
      : blogs;
    setFilteredBlogs(filtered);
  }, [searchBlog, blogs]);

  const fetchBlogs = async () => {
    try {
      const response = await axios.get(API_URL);
      const blogs = response.data.data.filter(blog => blog.blog_for !== 'xperia');
      setBlogs(blogs);
      setFilteredBlogs(blogs);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching blogs:', error);
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <Header />
      
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <Badge className="mb-6 bg-purple-500/20 text-purple-200 border-purple-400 font-medium">
            📚 Latest Insights & Updates
          </Badge>
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
            QuickTalk
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Blog</span>
          </h1>
          <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto font-medium">
            Discover the latest trends in AI-powered customer service, automation insights, and industry best practices.
          </p>
          
          {/* Search Bar */}
          <div className="relative max-w-md mx-auto mb-12">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              value={searchBlog}
              onChange={(e) => setSearchBlog(e.target.value)}
              placeholder="Search articles..."
              className="pl-12 pr-4 py-3 bg-white/10 border-white/20 text-white placeholder-gray-300 focus:border-purple-400 focus:ring-purple-400 rounded-full backdrop-blur-sm"
            />
          </div>
        </div>
      </section>

      {/* Blog Content */}
      <section className="container mx-auto px-4 pb-20">
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400"></div>
              <span className="text-white text-lg">Loading articles...</span>
            </div>
          </div>
        ) : (
          <div className="max-w-7xl mx-auto">
            {filteredBlogs.length === 0 ? (
              <div className="text-center py-20">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-2xl font-bold text-white mb-2">No articles found</h3>
                <p className="text-gray-300">Try adjusting your search terms</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredBlogs.map((blog) => (
                  <Link to={`/blogs/${blog.slug}`} key={blog.slug} className="group">
                    <article className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden hover:bg-white/15 transition-all duration-300 transform hover:scale-105 h-full flex flex-col">
                      {/* Image */}
                      <div className="h-48 w-full overflow-hidden">
                        <img
                          src={blog.blog_main_img?.url || '/placeholder.svg'}
                          alt={blog.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                        />
                      </div>
                      
                      {/* Content */}
                      <div className="p-6 flex flex-col flex-grow">
                        {/* Tag */}
                        <Badge className="mb-4 self-start bg-purple-500/20 text-purple-200 border-purple-400">
                          {blog.blog_tag || 'Uncategorized'}
                        </Badge>
                        
                        {/* Title */}
                        <h3 className="text-xl font-bold text-white mb-3 group-hover:text-purple-200 transition-colors line-clamp-2">
                          {blog.title}
                        </h3>
                        
                        {/* Summary */}
                        <p className="text-gray-300 text-sm line-clamp-3 flex-grow mb-4">
                          {blog.blog_summary}
                        </p>
                        
                        {/* Meta Info */}
                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4" />
                            <span>{formatDate(blog.createdAt)}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <User className="w-4 h-4" />
                            <span>{blog.author_name?.name || 'QuickTalk Team'}</span>
                          </div>
                        </div>
                        
                        {/* Read More */}
                        <div className="mt-4 flex items-center text-purple-300 group-hover:text-purple-200 transition-colors">
                          <span className="text-sm font-medium">Read more</span>
                          <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </article>
                  </Link>
                ))}
              </div>
            )}
          </div>
        )}
      </section>
      
      <Footer />
    </div>
  );
}

export default Blogs;