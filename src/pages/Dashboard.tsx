
import { useState } from "react";
import { Send, Mic, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import DashboardSidebar from "@/components/Dashboard/index";

const Dashboard = () => {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "How can I assist you?",
      isBot: true,
      timestamp: new Date()
    }
  ]);

  const handleSendMessage = () => {
    if (message.trim()) {
      setMessages([...messages, {
        id: messages.length + 1,
        text: message,
        isBot: false,
        timestamp: new Date()
      }]);
      setMessage("");
    }
  };

  return (
    <div className="flex h-screen bg-gray-900">
      <DashboardSidebar />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-gray-800 border-b border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <h1 className="text-white text-xl font-semibold">QuickTalk</h1>
            <div className="text-gray-400 text-sm">
              Najoomi Technologies
            </div>
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col bg-gray-100">
          {/* Messages */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="max-w-4xl mx-auto">
              {/* Welcome Message */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center bg-purple-600 text-white px-6 py-3 rounded-full mb-4">
                  <span className="text-sm font-medium">How can I assist you?</span>
                </div>
              </div>

              {/* Chat Messages */}
              <div className="space-y-4">
                {messages.map((msg) => (
                  <div key={msg.id} className={`flex ${msg.isBot ? 'justify-start' : 'justify-end'}`}>
                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      msg.isBot 
                        ? 'bg-purple-600 text-white' 
                        : 'bg-gray-300 text-gray-800'
                    }`}>
                      {msg.text}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Input Area */}
          <div className="bg-white border-t border-gray-200 p-4">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center space-x-3">
                <div className="flex-1 relative">
                  <Input
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Ask anything?"
                    className="w-full pr-12 py-3 rounded-full border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  />
                </div>
                <Button
                  onClick={handleSendMessage}
                  className="bg-purple-600 hover:bg-purple-700 text-white rounded-full p-3"
                  size="icon"
                >
                  <Send size={20} />
                </Button>
                <Button
                  className="bg-purple-600 hover:bg-purple-700 text-white rounded-full p-3"
                  size="icon"
                >
                  <Mic size={20} />
                </Button>
                <Button
                  className="bg-purple-600 hover:bg-purple-700 text-white rounded-full p-3"
                  size="icon"
                >
                  <RefreshCw size={20} />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
