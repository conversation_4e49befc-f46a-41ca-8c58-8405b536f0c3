import { useState, useEffect } from "react";
import { Eye, EyeOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Link, useNavigate } from "react-router-dom";
import Header from "@/components/sections/Header";
import { CognitoUser, CognitoUserAttribute, AuthenticationDetails } from "amazon-cognito-identity-js";
import { toast } from "react-hot-toast";
import userPool from "@/lib/userPoolConfig";
import { jwtDecode } from "jwt-decode";
import Cookies from "js-cookie";

interface CustomJwtPayload {
  email?: string;
  aud?: string | string[];
  exp?: number;
  iat?: number;
  iss?: string;
  jti?: string;
  nbf?: number;
  scope?: string;
  sub?: string;
  [key: string]: any;
}

const Signup = () => {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isConfirming, setIsConfirming] = useState(false);
  const [confirmationCode, setConfirmationCode] = useState("");
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [fields, setFields] = useState({
    username: "",
    email: "",
    phone_number: "",
    organizationId: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: false
  });

  // Handle countdown for resend button
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else {
      setResendDisabled(false);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFields((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const checkIfOrganizationExists = async (organizationId: string) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/check-organization`, {
        method: "POST",
        body: JSON.stringify({ organizationId }),
        headers: {
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();
      if (data.exists === false) {
        toast.error("Organization already registered. Please choose a different one.");
        return false;
      }
      return true;
    } catch (error) {
      console.error("Error checking organization:", error);
      return false;
    }
  };

  const handleSignUp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    // Username validation: must be all lowercase
    if (fields.username !== fields.username.toLowerCase()) {
      setError('Username must be all lowercase (no capital letters).');
      toast.error('Username must be all lowercase (no capital letters).');
      setIsLoading(false);
      return;
    }
    if (fields.password !== fields.confirmPassword) {
      setError('Passwords do not match');
      toast.error('Passwords do not match');
      setIsLoading(false);
      return;
    }
    if (!fields.agreeToTerms) {
      setError("Please agree to the Terms of Service and Privacy Policy");
      toast.error("Please agree to the Terms of Service and Privacy Policy");
      setIsLoading(false);
      return;
    }
    const { username, password, email, phone_number, organizationId } = fields;
    // Check if organization exists
    const isOrganizationValid = await checkIfOrganizationExists(organizationId);
    if (!isOrganizationValid) {
      setError("Organization already registered. Please choose a different one.");
      setIsLoading(false);
      return;
    }
    // Create CognitoUserAttribute instances
    const emailAttribute = new CognitoUserAttribute({
      Name: 'email',
      Value: email,
    });

    const phoneAttribute = new CognitoUserAttribute({
      Name: 'phone_number',
      Value: phone_number,
    });

    const organizationAttribute = new CognitoUserAttribute({
      Name: 'custom:organizationId',
      Value: organizationId,
    });

    userPool.signUp(
      username,
      password,
      [
        emailAttribute,
        phoneAttribute,
        organizationAttribute,
      ],
      [],
      (err, data) => {
        if (err) {
          setError(err.message);
          toast.error(err.message);
          setIsLoading(false);
          return;
        }
        toast.success('Sign up successful! Please check your email for the confirmation code.');
        setIsLoading(false);
        setIsConfirming(true);
        setResendDisabled(true);
        setCountdown(60);
      },
    );
  };

  const handleResendCode = () => {
    setIsLoading(true);
    const user = new CognitoUser({
      Username: fields.username,
      Pool: userPool,
    });

    user.resendConfirmationCode((err) => {
      if (err) {
        console.error('Resend code error:', err);
        setError(err.message);
        toast.error(err.message);
      } else {
        toast.success('New confirmation code sent!');
        setResendDisabled(true);
        setCountdown(60);
      }
      setIsLoading(false);
    });
  };

  const handleSignUpConfirmation = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    const user = new CognitoUser({
      Username: fields.username,
      Pool: userPool,
    });

    user.confirmRegistration(confirmationCode, true, async (err) => {
      if (err) {
        toast.error(err.message); // Show error toast
        setError(err.message);
        setIsLoading(false);
        return;
      }

      toast.success('Account confirmed! Logging you in...');
      // Send org_id and username to /create-user-group
      try {
        await fetch(`${import.meta.env.VITE_PUBLIC_DPWRAPPER_API}/create-user-group`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            org_id: fields.organizationId,
            username: fields.username,
          }),
        });
      } catch (apiError) {
        console.error('Error calling /create-user-group:', apiError);
      }
      handleSignInAfterConfirmation();
    });
  };

  const handleSignInAfterConfirmation = () => {
    const { username, password } = fields;
    const authenticationDetails = new AuthenticationDetails({
      Username: username,
      Password: password,
    });

    const user = new CognitoUser({
      Username: username,
      Pool: userPool,
    });

    user.authenticateUser(authenticationDetails, {
      onSuccess: async (session) => {
        const idToken = session.getIdToken().getJwtToken();
        const decodedToken = jwtDecode<CustomJwtPayload>(idToken);

        if (decodedToken.email) {
          localStorage.setItem('userEmail', decodedToken.email);
        }
        

        toast.success('Logged in successfully!');
        navigate('/dashboard');
      },
      onFailure: (err) => {
        console.error('Login error:', err);
        setError(err.message);
        toast.error(err.message);
        setIsLoading(false);
      },
    });
  };

  if (isConfirming) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <Header />
        <div className="flex items-center justify-center p-4 pt-20">
          <div className="w-full max-w-md">
            <div className="bg-white rounded-2xl shadow-2xl p-8">
              <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">Confirm Your Account</h2>
              <p className="text-center text-gray-600 mb-6">
                We've sent a confirmation code to {fields.email}
              </p>
              <form onSubmit={handleSignUpConfirmation} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="confirmationCode" className="text-gray-700">
                    Confirmation Code <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="confirmationCode"
                    type="text"
                    value={confirmationCode}
                    onChange={(e) => setConfirmationCode(e.target.value)}
                    className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                    placeholder="Enter the 6-digit code"
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-500 hover:from-purple-700 hover:to-pink-600 text-white font-semibold rounded-lg transition-all duration-200"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center">
                      <span className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></span>
                      Confirming...
                    </span>
                  ) : (
                    "Confirm Account"
                  )}
                </Button>
                <div className="text-center">
                  <button
                    type="button"
                    onClick={handleResendCode}
                    disabled={resendDisabled}
                    className={`text-purple-600 hover:text-purple-700 font-medium ${resendDisabled ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                  >
                    {resendDisabled
                      ? `Resend code in ${countdown}s`
                      : "Didn't receive the code? Resend"}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <Header />
      <div className="flex items-center justify-center p-4 pt-20">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-2xl shadow-2xl p-8">
            {/* Logo */}
            <div className="flex justify-center mb-8">
              <Link to="/" className="flex items-center space-x-3">
                {/* Glasses Logo */}
                <div className="relative flex flex-col items-start">
                  <div className="h-12 flex items-center">
                    <img
                      src="/headerlogo.png"
                      alt="QuickTalk Logo"
                      className="h-[100px] object-contain"
                      style={{ display: 'block' }}
                    />
                  </div>
                  {/* <span className="text-xs text-white  mt-1 ml-[130px]">Powered by Najoomi Technologies</span> */}
                </div>
              </Link>
            </div>

            <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">Create Account</h2>

            <form onSubmit={handleSignUp} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="username" className="text-gray-700">
                  Username <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="username"
                  name="username"
                  type="text"
                  value={fields.username}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="organizationId" className="text-gray-700">
                  Organization Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="organizationId"
                  name="organizationId"
                  type="text"
                  value={fields.organizationId}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-700">
                  Email <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={fields.email}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone_number" className="text-gray-700">
                  Phone Number <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="phone_number"
                  name="phone_number"
                  type="tel"
                  value={fields.phone_number}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-700">
                  Password <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    value={fields.password}
                    onChange={handleInputChange}
                    className="w-full h-12 px-4 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-gray-700">
                  Confirm Password <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={fields.confirmPassword}
                    onChange={handleInputChange}
                    className="w-full h-12 px-4 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="terms"
                  name="agreeToTerms"
                  checked={fields.agreeToTerms}
                  onCheckedChange={(checked) => setFields({ ...fields, agreeToTerms: checked as boolean })}
                />
                <Label htmlFor="terms" className="text-gray-700 cursor-pointer text-sm">
                  I agree to the Terms of Service and Privacy Policy
                </Label>
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-500 hover:from-purple-700 hover:to-pink-600 text-white font-semibold rounded-lg transition-all duration-200"
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <span className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></span>
                    Creating Account...
                  </span>
                ) : (
                  "CREATE ACCOUNT"
                )}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-gray-600">
                Already have an account?{" "}
                <Link to="/login" className="text-purple-600 hover:text-purple-700 font-medium">
                  Sign in
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;
