
import { Phone, PhoneCall, Globe, Mic, HeadphonesIcon, Calendar, Target, Users, Clock, MessageSquare, CheckCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import Header from "@/components/sections/Header";
import Footer from "@/components/sections/Footer";

const CallSolutions = () => {
  const navigate = useNavigate();

  const handleDemoClick = () => {
    navigate('/contact');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <Header />
      
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-white mb-6">Call Solutions</h1>
          <p className="text-xl text-gray-200 max-w-4xl mx-auto mb-8">
            Transform your customer communication with AI-powered call handling and automated campaigns. 
            Support 50+ languages and connect through voice calls and WhatsApp messaging.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4"
              onClick={handleDemoClick}
            >
              Book Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Features Overview */}
      <section className="container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Inbound Solutions */}
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
            <CardHeader>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                  <Phone className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-white text-2xl">Inbound Call Management</CardTitle>
              </div>
              <p className="text-gray-300">Handle customer calls automatically with intelligent AI agents</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-blue-400" />
                  <span>24/7 availability - never miss a call</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-blue-400" />
                  <span>Natural conversation flow with context awareness</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-blue-400" />
                  <span>Smart escalation to live agents when needed</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-blue-400" />
                  <span>Real-time sentiment analysis and adjustment</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-blue-400" />
                  <span>Call recording and transcription</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Outbound Solutions */}
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
            <CardHeader>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
                  <PhoneCall className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-white text-2xl">Outbound Campaigns</CardTitle>
              </div>
              <p className="text-gray-300">Run automated campaigns via voice calls and WhatsApp</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Dual-channel campaigns (Voice + WhatsApp)</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Mission-driven conversations (demos, follow-ups)</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Smart callback scheduling and management</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>A/B testing for optimal messaging</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-200">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Campaign analytics and performance tracking</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Multilingual Support */}
      <section className="container mx-auto px-4 py-16">
        <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Globe className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-white text-3xl mb-4">Multilingual Support</CardTitle>
            <p className="text-gray-300 text-lg">Communicate with customers in their preferred language</p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <h4 className="text-white font-semibold mb-2">50+ Languages</h4>
                <p className="text-gray-300 text-sm">Support for major world languages including English, Spanish, French, German, Chinese, Japanese, and more</p>
              </div>
              <div className="text-center">
                <h4 className="text-white font-semibold mb-2">Auto-Detection</h4>
                <p className="text-gray-300 text-sm">Automatically detect customer language and switch conversation flow accordingly</p>
              </div>
              <div className="text-center">
                <h4 className="text-white font-semibold mb-2">Cultural Context</h4>
                <p className="text-gray-300 text-sm">AI understands cultural nuances and adjusts communication style appropriately</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Use Cases */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">Popular Use Cases</h2>
          <p className="text-xl text-gray-200">See how businesses use our call solutions</p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
            <CardHeader>
              <CardTitle className="text-white">Customer Support</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm">Handle support tickets, troubleshooting, and account inquiries 24/7</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
            <CardHeader>
              <CardTitle className="text-white">Lead Qualification</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm">Qualify prospects and schedule demos with sales teams automatically</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
            <CardHeader>
              <CardTitle className="text-white">Appointment Booking</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm">Schedule appointments, send reminders, and handle rescheduling requests</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
            <CardHeader>
              <CardTitle className="text-white">Follow-up Campaigns</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm">Nurture leads with personalized follow-up calls and messages</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
            <CardHeader>
              <CardTitle className="text-white">Survey & Feedback</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm">Collect customer feedback and conduct satisfaction surveys</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-lg">
            <CardHeader>
              <CardTitle className="text-white">Order Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm">Handle order status inquiries, modifications, and delivery updates</p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20">
        <Card className="bg-white/10 backdrop-blur-sm border-white/20">
          <CardContent className="p-12 text-center">
            <h2 className="text-4xl font-bold text-white mb-4">Ready to Transform Your Call Operations?</h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              Start handling calls smarter with AI that speaks your customers' language.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4"
                onClick={handleDemoClick}
              >
                Book Demo
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>

      <Footer />
    </div>
  );
};

export default CallSolutions;
