import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Button, Menu, MenuItem } from '@mui/material';
import { ChevronDown, MessageCircle } from 'lucide-react';
import config from '../../src/config.json';
import axios from 'axios';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

interface QuestionAnswer {
  question: string;
  answer: string;
  type: string;
  timestamp: { $date: string };
}

interface Topic {
  _id: string;
  topic: string;
  timestamp: { $date: string };
  history?: QuestionAnswer[];
}

const ChatHistory: React.FC<{ organization_id: string }> = ({ organization_id }) => {
  const navigate = useNavigate();
  const [username, setUsername] = useState<string | null>(null);
  const [fetchedTopics, setFetchedTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'recent' | 'oldest'>('recent');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [filterApplied, setFilterApplied] = useState<boolean>(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [showConversation, setShowConversation] = useState(false);

  useEffect(() => {
    const storedUsername = localStorage.getItem(import.meta.env.VITE_COGNITO_LAST_AUTH_USER_KEY as string);
    if (storedUsername && organization_id) {
      setUsername(storedUsername);
      fetchTopics(storedUsername);
    } else if (!storedUsername) {
      navigate('/');
    }
  }, [navigate, organization_id]);

  const fetchTopics = async (username: string) => {
    try {
      setLoading(true);
      const response = await axios.get(`${config.flaskstore}/${organization_id}`);
      setFetchedTopics(response.data.topics || []);
    } catch (error) {
      console.error('Failed to fetch topics:', error);
      setFetchedTopics([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterClick = () => {
    setShowFilterMenu(!showFilterMenu);
  };

  const handleFilterOption = (option: string) => {
    setSortOrder(option as 'recent' | 'oldest');
    setShowFilterMenu(false);
  };

  const handleClearFilter = () => {
    setSelectedDate(null);
    setFilterApplied(false);
  };

  const handleTopicClick = (topic: Topic) => {
    setSelectedTopic(topic);
    setShowConversation(true);
  };

  const handleBackToTopics = () => {
    setShowConversation(false);
    setSelectedTopic(null);
  };

  const filteredTopics = fetchedTopics
    .filter(topic =>
      topic.topic.toLowerCase().includes(searchQuery.toLowerCase()) &&
      (!selectedDate || new Date(topic.timestamp.$date).toDateString() === selectedDate.toDateString())
    )
    .sort((a, b) => {
      return sortOrder === 'recent'
        ? new Date(b.timestamp.$date).getTime() - new Date(a.timestamp.$date).getTime()
        : new Date(a.timestamp.$date).getTime() - new Date(b.timestamp.$date).getTime();
    });

  // Show loading state
  if (loading) {
    return (
      <div className="p-4 max-w-6xl mx-auto font-manrope">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading chat history...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 max-w-6xl mx-auto font-manrope">
      {/* Search Bar and Filter Section - Only show when not viewing a conversation */}
      {!showConversation && (
        <>
          <div className="flex justify-center items-center mb-6 relative">
            <div className="relative flex items-center w-full max-w-2xl">
              {/* Search Bar */}
              <div className="relative flex-1 shadow-md rounded-full">
                <input
                  type="text"
                  placeholder="Search"
                  className="w-full h-10 pl-10 pr-12 rounded-full border border-gray-200 focus:outline-none focus:border-gray-300"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <span className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-2">
                  <div className="border-l h- border-gray-300"></div>
                 <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </span>
              </div>

              {/* Filter Button */}
              <button
                onClick={handleFilterClick}
                className="ml-2 p-2 hover:bg-gray-100 rounded-md relative"
              >
                <svg className="w-6 h-6 text-gray-600" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>

              {/* Filter Dropdown Menu */}
              {showFilterMenu && (
                <div className="absolute right-0 top-12 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                  <div className="py-1">
                    <button
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => handleFilterOption('recent')}
                    >
                      Recent to oldest
                    </button>
                    <button
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => handleFilterOption('oldest')}
                    >
                      Oldest to recent
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="mb-4 flex justify-center">
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Filter By Date"
                value={selectedDate}
                onChange={(newDate) => {
                  setSelectedDate(newDate);
                  setShowDatePicker(false);
                  setFilterApplied(true);
                }}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </LocalizationProvider>
          </div>

          {filterApplied && (
            <Button onClick={handleClearFilter} className="mb-4" variant="outlined">
              Clear Filter
            </Button>
          )}
        </>
      )}

      {/* Back button when viewing conversation */}
      {showConversation && (
        <div className="mb-4">
          <Button
            onClick={handleBackToTopics}
            className="font-manrope min-w-[140px] !border-2 !border-[#CD0ADD] !text-[#CD0ADD] hover:!bg-[#CD0ADD] hover:!text-white"
            startIcon={<ChevronDown className="rotate-90" />}
          >
            Back to Topics
          </Button>

          <h2 className="text-xl font-bold mt-4">{selectedTopic?.topic}</h2>
        </div>
      )}

      {/* Conversation View */}
      {showConversation && selectedTopic && (
        <Card className="overflow-hidden mb-4">
          <div className="p-4 bg-[#F5F5F5]">
            <div className="space-y-4">
              {selectedTopic.history?.map((item, index) => (
                <div key={index}>
                  <div className="bg-white p-3 rounded-lg shadow-sm">
                    <div className="font-semibold text-gray-800">Question:</div>
                    <div className="mt-1">{item.question}</div>
                  </div>
                  <div className="bg-white p-3 mt-3 rounded-lg shadow-sm">
                    <div className="font-semibold text-gray-800">Answer:</div>
                    <div className="mt-1">{item.answer}</div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {new Date(item.timestamp.$date).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}

      {/* Chat History Table - Only show when not viewing a conversation */}
      {!showConversation && (
        <Card className="overflow-hidden">
          <div className="w-full">
            <div className="grid grid-cols-3 text-center bg-[#CD0ADD] text-white font-medium">
              <div className="p-4 px-14 text-left font-extrabold">Chat</div>
              <div className="p-4 font-extrabold">Date</div>
              <div className="p-4 px-20 text-right font-extrabold">Time</div>
            </div>
            <div className="divide-y divide-gray-200">
              {filteredTopics.map((topic) => (
                <div
                  key={topic._id}
                  className="grid grid-cols-3 text-center hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleTopicClick(topic)}
                >
                  <div className="p-4 px-10 flex items-center font-bold">
                    <img src="/history.png" alt="History Icon" className="w-5 h-5 mr-2" />
                    {topic.topic}
                  </div>

                  <div className="p-4 text-center">{new Date(topic.timestamp.$date).toLocaleDateString()}</div>
                  <div className="p-4 px-14 text-right">{new Date(topic.timestamp.$date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default ChatHistory;