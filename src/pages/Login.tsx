import { useState, useEffect } from "react";
import { <PERSON>, EyeOff } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Link, useNavigate } from "react-router-dom";
import { CognitoUser, AuthenticationDetails } from "amazon-cognito-identity-js";
import { toast } from "react-hot-toast";
import userPool from "@/lib/userPoolConfig";
import { jwtDecode } from "jwt-decode";
import Cookies from "js-cookie";

interface CustomJwtPayload {
  email?: string;
  aud?: string | string[];
  exp?: number;
  iat?: number;
  iss?: string;
  jti?: string;
  nbf?: number;
  scope?: string;
  sub?: string;
  [key: string]: any; // Allow for additional custom claims
}

const Login = () => {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isForceChangePassword, setIsForceChangePassword] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [cognitoUser, setCognitoUser] = useState<CognitoUser | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user is already logged in
    const user = userPool.getCurrentUser();
    if (user) {
      user.getSession((err, session) => {
        if (!err && session && session.isValid()) {
          navigate('/dashboard');
        }
      });
    }
    const savedUsername = Cookies.get('username');
    if (savedUsername) {
      setUsername(savedUsername);
      setRememberMe(true);
    }
    
    // Check for temporary new password and pre-fill it
    const tempNewPassword = localStorage.getItem('tempNewPassword');
    if (tempNewPassword) {
      setPassword(tempNewPassword);
      localStorage.removeItem('tempNewPassword'); // Clear it after use
    }
  }, [navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    const authenticationDetails = new AuthenticationDetails({
      Username: username,
      Password: password,
    });

    const user = new CognitoUser({
      Username: username,
      Pool: userPool,
    });

    user.authenticateUser(authenticationDetails, {
      onSuccess: async (session) => {
        const idToken = session.getIdToken().getJwtToken();
        const decodedToken = jwtDecode<CustomJwtPayload>(idToken);

        if (decodedToken.email) {
          localStorage.setItem('userEmail', decodedToken.email as string);
        }
        if (decodedToken['cognito:groups'] && Array.isArray(decodedToken['cognito:groups'])) {
          localStorage.setItem('groupName', decodedToken['cognito:groups'][0]);
        }

        if (rememberMe) {
          Cookies.set('username', username, { expires: 5 });
        }

        toast.success('Logged in successfully!');
        navigate('/dashboard');
      },
      onFailure: (err) => {
        toast.error(err.message || 'Authentication failed');
        setError(err.message);
        setIsLoading(false);
      },
      newPasswordRequired: (userAttributes, requiredAttributes) => {
        setCognitoUser(user);
        setIsForceChangePassword(true);
        setIsLoading(false);
      },
    });
  };

  const handleForceChangePassword = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!cognitoUser) return;
    setIsLoading(true);
    cognitoUser.completeNewPasswordChallenge(newPassword, {}, {
      onSuccess: async (session) => {
        // Store the new password temporarily for auto-fill
        localStorage.setItem('tempNewPassword', newPassword);
        toast.success('Password changed successfully! Please login again.');
        setIsForceChangePassword(false);
        setNewPassword('');
        setCognitoUser(null);
        setIsLoading(false);
      },
      onFailure: (err) => {
        toast.error(err.message);
        setError(err.message);
        setIsLoading(false);
      },
    });
  };

  if (isForceChangePassword) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-2xl shadow-2xl p-8">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">Set New Password</h2>
            {error && <p className="text-red-500 mb-4">{error}</p>}
            <form onSubmit={handleForceChangePassword} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="newPassword" className="text-gray-700">
                  New Password <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                <Input
                  id="newPassword"
                    type={showNewPassword ? "text" : "password"}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                    className="w-full h-12 px-4 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  required
                />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showNewPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>
              <Button
                type="submit"
                className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-500 hover:from-purple-700 hover:to-pink-600 text-white font-semibold rounded-lg transition-all duration-200 flex items-center justify-center"
                disabled={isLoading}
              >
                {isLoading ? 'Changing Password...' : 'Set New Password'}
              </Button>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-2xl p-8">
          {/* Logo */}
          <div className="flex justify-center mb-8">
            <Link to="/" className="flex items-center space-x-3">
              {/* Glasses Logo */}
              <div className="relative flex flex-col items-start">
                <div className="h-12 flex items-center">
                  <img
                    src="/headerlogo.png"
                    alt="QuickTalk Logo"
                    className="h-[100px] object-contain"
                    style={{ display: 'block' }}
                  />
                </div>
                {/* <span className="text-xs text-white  mt-1 ml-[130px]">Powered by Najoomi Technologies</span> */}
              </div>
            </Link>
          </div>

          {/* Login Title */}
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">Login</h2>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Username Field */}
            <div className="space-y-2">
              <Label htmlFor="username" className="text-gray-700">
                Username <span className="text-red-500">*</span>
              </Label>
              <Input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                required
              />
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-gray-700">
                Password <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full h-12 px-4 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>

            {/* Remember Me */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember"
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked as boolean)}
              />
              <Label htmlFor="remember" className="text-gray-700 cursor-pointer">
                Remember me
              </Label>
            </div>

            {/* Login Button */}
            <Button
              type="submit"
              className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-500 hover:from-purple-700 hover:to-pink-600 text-white font-semibold rounded-lg transition-all duration-200 flex items-center justify-center"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <span className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></span>
                  Logging in...
                </span>
              ) : (
                'LOGIN'
              )}
            </Button>
          </form>

          {/* Footer Links */}
          <div className="mt-6 text-center">
            <p className="text-gray-600">
              Don't have an account?{" "}
              <Link to="/signup" className="text-purple-600 hover:text-purple-700 font-medium">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
