
import { 
  <PERSON>Text, 
  <PERSON>lette, 
  Plug, 
  BarChart3, 
  Users, 
  MessageSquare, 
  Phone,
  PhoneCall,
  Upload,
  Zap,
  Target,
  Globe,
  UserPlus
} from "lucide-react";

export const features = [
  {
    id: "documents",
    title: "Document Processing",
    description: "Upload and process any document type",
    details: "Advanced AI processes PDFs, Word docs, spreadsheets, and more. Extract insights, answer questions, and generate summaries from your documents instantly.",
    icon: FileText
  },
  {
    id: "customization",
    title: "Brand Customization",
    description: "Make it uniquely yours",
    details: "Customize colors, fonts, logos, and conversation flows to match your brand perfectly. Create a seamless experience that feels native to your business.",
    icon: Palette
  },
  {
    id: "integration",
    title: "Easy Integration",
    description: "Connect with your existing tools",
    details: "Seamlessly integrate with CRM systems, help desk software, and other business tools. API-first design makes implementation quick and flexible.",
    icon: Plug
  },
  {
    id: "multilingual",
    title: "Global Language Support",
    description: "Communicate in 50+ languages",
    details: "Native support for over 50 languages with automatic language detection. Your AI agents can seamlessly switch between languages within the same conversation.",
    icon: Globe
  },
  {
    id: "lead-generation",
    title: "Smart Lead Generation",
    description: "Capture leads automatically during conversations",
    details: "AI naturally collects contact information during conversations without being pushy. Automatically generates qualified leads and sends notifications to your team.",
    icon: UserPlus
  },
  {
    id: "analytics",
    title: "Smart Analytics",
    description: "Understand your customers better",
    details: "Track conversation metrics, customer satisfaction, resolution times, and more. Get actionable insights to improve your customer service strategy.",
    icon: BarChart3
  },
  {
    id: "agents",
    title: "Live Agent Handoff",
    description: "Seamless human support when needed",
    details: "Smart escalation to live agents when conversations require human touch. Maintain context and conversation history for smooth transitions.",
    icon: Users
  }
];

export const howItWorksSteps = [
  {
    title: "Upload & Configure",
    description: "Set up your AI assistant in minutes",
    icon: Upload,
    features: [
      "Upload documents, websites, or API data",
      "Customize brand colors and messaging",
      "Configure conversation flows"
    ]
  },
  {
    title: "Train & Deploy",
    description: "AI learns your business automatically",
    icon: Zap,
    features: [
      "AI processes your content instantly",
      "Smart routing and escalation rules",
      "Multi-channel deployment"
    ]
  },
  {
    title: "Engage & Optimize",
    description: "Deliver exceptional customer experiences",
    icon: Target,
    features: [
      "24/7 automated support",
      "Real-time analytics and insights",
      "Continuous improvement suggestions"
    ]
  }
];

export const integrationPlatforms = [
  "WhatsApp",
  "LinkedIn", 
  "Website",
  "Mobile App",
  "Messenger",
  "Telegram"
];

export const pricingPlans = [
  {
    name: "Starter",
    price: 0,
    period: "Forever free",
    description: "Perfect for trying out QuickTalk",
    features: [
      "Up to 100 conversations/month",
      "Basic chat widget",
      "Email support",
      "1 knowledge base"
    ],
    cta: "Get Started Free",
    popular: false,
    comingSoon: false
  },
  {
    name: "Professional",
    price: 99,
    originalPrice: 99,
    period: "per month",
    description: "Best for growing businesses",
    features: [
      "Up to 2,000 conversations/month",
      "Advanced customization",
      "Live agent handoff",
      "Analytics dashboard",
      "Priority support",
      "Multiple knowledge bases"
    ],
    cta: "Start 14-day Trial",
    popular: true,
    comingSoon: false
  },
  {
    name: "Enterprise",
    price: "Custom",
    period: "Contact us for pricing",
    description: "For large organizations",
    features: [
      "Unlimited conversations",
      "Custom integrations",
      "Dedicated support",
      "SLA guarantees",
      "Advanced analytics",
      "White-label options"
    ],
    cta: "Contact Sales",
    popular: false,
    comingSoon: false
  }
];

export const integrations = [
  {
    name: "Slack",
    description: "Team collaboration",
    logo: "/lovable-uploads/2f863830-eae7-4772-9513-3c1959487e36.png"
  },
  {
    name: "Shopify",
    description: "E-commerce platform",
    logo: "/lovable-uploads/d5609cee-b9b6-4445-bf7c-3f26f650142b.png"
  },
  {
    name: "Zapier",
    description: "Workflow automation",
    logo: "/lovable-uploads/e6804bcd-8be7-4018-a277-2da12acb1140.png"
  }
];

export const useCases = [
  {
    title: "E-commerce Support",
    description: "Handle order inquiries, product questions, and returns automatically",
    icon: "🛍️"
  },
  {
    title: "SaaS Help Desk",
    description: "Provide instant technical support and onboarding assistance",
    icon: "💻"
  },
  {
    title: "Healthcare",
    description: "Schedule appointments and answer common patient questions",
    icon: "🏥"
  },
  {
    title: "Education",
    description: "Support students with course information and enrollment",
    icon: "🎓"
  },
  {
    title: "Real Estate",
    description: "Qualify leads and schedule property viewings",
    icon: "🏠"
  },
  {
    title: "Financial Services",
    description: "Answer account questions and guide through applications",
    icon: "💰"
  }
];
