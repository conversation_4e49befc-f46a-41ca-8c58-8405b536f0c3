import { Toaster } from "react-hot-toast";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import CallSolutions from "./pages/CallSolutions";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";
import Dashboard from "../src/components/Dashboard/index";
import { ThemeProvider } from "@material-tailwind/react";
import ChatbotPublicHome from "./pages/Chatbotpublic";
import Blogs from "./pages/Blogs";
import BlogPost from "./pages/[slug]";
import { LoaderProvider } from "./context/LoaderContext";

const queryClient = new QueryClient();

const App = () => (
  <ThemeProvider>
  <QueryClientProvider client={queryClient}>
    <LoaderProvider>
      <TooltipProvider>
        <Toaster
          position="bottom-left"
          reverseOrder={false}
          toastOptions={{
            success: {
              icon: (
                <div className="w-6 h-6 flex items-center justify-center rounded-full bg-[#CD0ADD]">
                  <svg
                    className="w-4 h-4"
                    fill="white"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 00-1.414 0L8 12.586l-3.293-3.293a1 1 0 00-1.414 1.414l4 4a1 1 0 001.414 0l8-8a1 1 0 000-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              ),
            },
          }}
        />
        <BrowserRouter>
          <ScrollToTop />
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/call-solutions" element={<CallSolutions />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/chatbot/:username" element={<ChatbotPublicHome />} />
            <Route path="/blogs" element={<Blogs />} />
            <Route path="/blogs/:slug" element={<BlogPost />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </LoaderProvider>
  </QueryClientProvider>
  </ThemeProvider>
);

export default App;
