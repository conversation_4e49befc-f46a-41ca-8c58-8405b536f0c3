

import React, { createContext, useState, useContext } from 'react'

const LoaderContext = createContext({
  loading: false,
  setLoading: () => {},
  handleLoaderCompletion: () => {},
})

export const LoaderProvider = ({ children }) => {
  const [loading, setLoading] = useState(false)
  const [completed, setCompleted] = useState(false)
  const [message, setMessage] = useState('') 
  console.log("message",message)
  const handleLoaderCompletion = () => {
    setCompleted(true)
    setTimeout(() => {
      setCompleted(false)
      setLoading(false)
    }, 2000)
  }

  return (
    <LoaderContext.Provider value={{ loading, setLoading, handleLoaderCompletion,setMessage }}>
      <div className="relative">
        {children}
        {loading && (
          <div className="absolute top-20 left-0 right-0 z-50 flex items-center justify-center p-4">
            <div className="bg-white shadow-lg rounded-lg p-4 flex items-center space-x-4 transition-all duration-300 ease-in-out">
            <div className="w-4 h-4 rounded-full border-t-2 border-[#CD0ADD] border-solid animate-spin mr-2"></div>
              <span className="text-sm font-medium text-gray-700">{message || 'Ingestion is currently in progress....'}</span>
            </div>
          </div>
        )}
      </div>
    </LoaderContext.Provider>
  )
}

export const useLoader = () => useContext(LoaderContext)
