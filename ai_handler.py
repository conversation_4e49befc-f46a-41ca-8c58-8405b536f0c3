import asyncio
import json
import logging
from fastapi import FastAPI, Request, Response, Depends
import httpx
import asyncpg
from aiortc import RTCPeerConnection, RTCSessionDescription, RTCDataChannel
from aiortc.rtcdtlstransport import RTCCertificate
from aiortc.contrib.media import MediaPlayer, MediaRecorder
from aiortc.rtcrtpsender import RTCRtpSender
from typing import Dict, Any, Optional

import os
import json
import asyncio
import asyncpg
import logging
import requests
from typing import Optional, Dict, List, Any
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
import httpx
from pydantic import BaseModel
from dotenv import load_dotenv

from prompts import get_voice_instruct, get_org_instruct
from app import fetch_session_config_from_db

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration - Load from environment variables
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY environment variable not set.")
    raise EnvironmentError("OPENAI_API_KEY environment variable not set.")

OPENAI_SESSION_URL = os.environ.get('OPENAI_SESSION_URL')
OPENAI_API_URL = os.environ.get('OPENAI_API_URL')
MODEL_ID = os.environ.get('MODEL')
VOICE = os.environ.get('VOICE')
# DEFAULT_INSTRUCTIONS = os.environ.get('DEFAULT_INSTRUCTIONS', 'You are a helpful AI assistant and have some tools installed. Converse in the Urdu language, use simple vocabulary and speak clearly. Talk quickly, but remain friendly and stay on topic at all times.')
PORT = int(os.environ.get('PORT', 5055))

# Database configuration
DB_CONFIG = {
    'user': os.getenv('user', 'chatbot_user'),
    'password': os.getenv('password', 'ChatBot@123'),
    'database': os.getenv('dbname', 'call_centre_analytics'),
    'host': os.getenv('host', 'najoomi.clw2kwwaavrk.us-east-1.rds.amazonaws.com'),
    'port': os.getenv('port', '5432'),
    'min_size': 1,
    'max_size': 10
}

# Global database pool
db_pool = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan context manager for database connection pool management."""
    global db_pool
    logger.info("Application startup: Initializing database connection pool...")
    try:
        db_pool = await asyncpg.create_pool(**DB_CONFIG)
        logger.info("Database connection pool created successfully")
        yield
    except Exception as e:
        logger.error(f"Failed to create database pool: {e}")
        raise
    finally:
        logger.info("Application shutdown: Closing database connection pool...")
        if db_pool:
            await db_pool.close()

# Log configuration (without sensitive data)
logger.info("Configuration loaded: AI HANDLER")
logger.info(f"  OPENAI_SESSION_URL: {OPENAI_SESSION_URL}")
logger.info(f"  OPENAI_API_URL: {OPENAI_API_URL}")
logger.info(f"  MODEL: {MODEL_ID}")
logger.info(f"  VOICE: {VOICE}")
logger.info(f"  PORT: {PORT}")
logger.info(f"  OPENAI_API_KEY: {'*' * (len(OPENAI_API_KEY) - 8) + OPENAI_API_KEY[-8:] if OPENAI_API_KEY else 'Not set'}")

# FastAPI app instance
app = FastAPI(
    title="Integrated WebRTC Backend",
    description="WebRTC backend with session configuration from database",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class SessionConfig(BaseModel):
    tools: List[Dict[str, Any]]
    instructions: str
    api_links: Dict[str, List[str]]
    modalities: List[str] = ["text", "audio"]

class FunctionTool(BaseModel):
    type: str = "function"
    name: str
    description: str
    parameters: Dict[str, Any]

# Dependency to get database pool
async def get_pool() -> asyncpg.Pool:
    """Get the database connection pool."""
    if db_pool is None:
        raise HTTPException(
            status_code=503,
            detail="Database service is currently unavailable"
        )
    return db_pool

import asyncio
import json
import logging
import uuid
import time
from typing import Dict, Any, Optional
import httpx
from fastapi import Request, Depends
from fastapi.responses import Response
import asyncpg

# aiortc imports
from aiortc import RTCPeerConnection, RTCSessionDescription, RTCDataChannel
from aiortc.rtcrtpsender import RTCRtpSender
from aiortc.rtcrtpreceiver import RTCRtpReceiver
from aiortc.mediastreams import MediaStreamTrack

logger = logging.getLogger(__name__)

# Store active connections
active_connections: Dict[str, Dict[str, Any]] = {}

class AudioRelayTrack(MediaStreamTrack):
    """
    Custom audio track that relays audio between peer connections
    """
    kind = "audio"

    def __init__(self, source_track, connection_name):
        super().__init__()
        self.source_track = source_track
        self.connection_name = connection_name
        self.frame_count = 0
        self.last_log_time = 0

    async def recv(self):
        """Receive and relay audio frames"""
        try:
            frame = await self.source_track.recv()
            self.frame_count += 1
            
            # Log audio flow every 5 seconds
            current_time = time.time()
            if current_time - self.last_log_time > 5:
                logger.info(f"Audio relay {self.connection_name}: {self.frame_count} frames processed")
                self.last_log_time = current_time
                
            return frame
        except Exception as e:
            logger.error(f"Error in audio relay {self.connection_name}: {e}")
            raise

class OpenAIRealtimeSession:
    """Handles OpenAI Realtime API integration via data channel"""
    
    def __init__(self, peer_connection: RTCPeerConnection, organisation_id: str, config):
        self.pc = peer_connection
        self.organisation_id = organisation_id
        self.config = config
        self.data_channel: Optional[RTCDataChannel] = None
        self.session_id = str(uuid.uuid4())
        
    async def setup_data_channel(self):
        """Create data channel for OpenAI communication"""
        try:
            # Create data channel for sending events to OpenAI
            self.data_channel = self.pc.createDataChannel("openai-events", ordered=True)
            
            @self.data_channel.on("open")
            async def on_open():
                logger.info(f"Data channel opened for session {self.session_id}")
                # Send initial session configuration
                await self.configure_session()
            
            @self.data_channel.on("message")
            async def on_message(message):
                logger.info(f"Received message from OpenAI: {message}")
                try:
                    event = json.loads(message)
                    await self.handle_openai_event(event)
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON from OpenAI: {message}")
                    
        except Exception as e:
            logger.error(f"Error setting up data channel: {e}")
    
    async def configure_session(self):
        """Send session configuration to OpenAI"""
        if not self.data_channel or self.data_channel.readyState != "open":
            logger.warning("Data channel not ready for configuration")
            return
            
        session_config = {
            "type": "session.update",
            "session": {
                "modalities": ["text", "audio"],
                "instructions": self.config.instructions if self.config else "You are a helpful assistant",
                "voice": "ash",
                "input_audio_format": "pcm16",
                "output_audio_format": "pcm16",
                "tools": self.config.tools if self.config and self.config.tools else [],
                "tool_choice": "auto"
            }
        }
        
        try:
            self.data_channel.send(json.dumps(session_config))
            logger.info("Session configuration sent to OpenAI")
        except Exception as e:
            logger.error(f"Error sending session config: {e}")
    
    async def handle_openai_event(self, event: Dict[str, Any]):
        """Handle events from OpenAI Realtime API"""
        event_type = event.get("type")
        logger.info(f"OpenAI event: {event_type}")
        
        if event_type == "response.function_call_arguments.done":
            await self.handle_function_call(event)
        elif event_type == "session.created":
            logger.info("OpenAI session created successfully")
        elif event_type == "error":
            logger.error(f"OpenAI error: {event.get('error')}")
    
    async def handle_function_call(self, event: Dict[str, Any]):
        """Execute function call and send result back"""
        try:
            call_id = event.get("call_id")
            function_name = event.get("name")
            arguments_str = event.get("arguments", "{}")
            
            logger.info(f"Function call: {function_name} (call_id: {call_id})")
            
            # Parse arguments
            try:
                arguments = json.loads(arguments_str)
            except json.JSONDecodeError:
                arguments = {}
            
            # Execute the function
            result = await self.execute_function(function_name, arguments)
            
            # Send result back to OpenAI
            function_result = {
                "type": "conversation.item.create",
                "item": {
                    "type": "function_call_output",
                    "call_id": call_id,
                    "output": json.dumps(result)
                }
            }
            
            if self.data_channel and self.data_channel.readyState == "open":
                self.data_channel.send(json.dumps(function_result))
                
                # Trigger response generation
                response_create = {"type": "response.create"}
                self.data_channel.send(json.dumps(response_create))
                
                logger.info(f"Function result sent for {function_name}")
            
        except Exception as e:
            logger.error(f"Error handling function call: {e}")
    
    async def execute_function(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the actual function call"""
        try:
            if not self.config or function_name not in self.config.api_links:
                return {"error": f"Function {function_name} not configured"}
            
            api_url, method = self.config.api_links[function_name]
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                if method.upper() == "GET":
                    response = await client.get(api_url, params=arguments)
                else:
                    response = await client.post(api_url, json=arguments)
                
                response.raise_for_status()
                
                try:
                    return response.json()
                except:
                    return {"data": response.text}
                    
        except Exception as e:
            logger.error(f"Function execution error: {e}")
            return {"error": f"Function failed: {str(e)}"}

# Add this enhanced logging to your WebRTC backend

@app.post("/api/rtc-connect")
async def connect_rtc(
    request: Request,
    organisation_id: str,
    pool: asyncpg.Pool = Depends(get_pool)
):
    try:
        client_sdp_bytes = await request.body()
        if not client_sdp_bytes:
            return Response("No SDP provided in the request body.", status_code=400)
        client_sdp = client_sdp_bytes.decode('utf-8')

        logger.info(f"=== RTC Connect Debug for org: {organisation_id} ===")
        logger.info(f"Incoming SDP length: {len(client_sdp)}")
        
        # Check SDP content
        has_audio = 'm=audio' in client_sdp
        has_connection = 'c=' in client_sdp
        logger.info(f"SDP has audio: {has_audio}")
        logger.info(f"SDP has connection: {has_connection}")
        
        if not has_audio:
            logger.error("❌ No audio media line in incoming SDP!")
            return Response("Invalid SDP: No audio media", status_code=400)

        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info("Fetching session config and ephemeral token...")
            
            # Fetch config
            try:
                config = await fetch_session_config_from_db(pool, organisation_id)
                if not config:
                    logger.error(f"❌ No config found for org: {organisation_id}")
                    return Response("No configuration found", status_code=404)
                    
                logger.info(f"✅ Config loaded: {len(config.tools)} tools, instructions length: {len(config.instructions)}")
            except Exception as e:
                logger.error(f"❌ Config fetch error: {e}")
                raise

            # Get token
            try:
                token_data = await get_ephemeral_token(client, config)
                ephemeral_token = token_data['client_secret']['value']
                logger.info(f"✅ Token obtained, length: {len(ephemeral_token)}")
            except Exception as e:
                logger.error(f"❌ Token fetch error: {e}")
                raise

            # SDP exchange
            sdp_headers = {
                "Authorization": f"Bearer {ephemeral_token}",
                "Content-Type": "application/sdp"
            }
            
            sdp_params = {
                "model": MODEL_ID,
                "voice": VOICE
            }
           
            logger.info(f"Sending SDP to OpenAI: {OPENAI_API_URL}")
            logger.info(f"Model: {MODEL_ID}, Voice: {VOICE}")
            
            try:
                sdp_response = await client.post(
                    OPENAI_API_URL, 
                    headers=sdp_headers, 
                    params=sdp_params, 
                    content=client_sdp
                )
                
                logger.info(f"OpenAI SDP Response Status: {sdp_response.status_code}")
                logger.info(f"OpenAI SDP Response Headers: {dict(sdp_response.headers)}")
                
                sdp_response.raise_for_status()
                
                # Analyze response SDP
                response_sdp = sdp_response.text
                has_response_audio = 'm=audio' in response_sdp
                logger.info(f"Response SDP length: {len(response_sdp)}")
                logger.info(f"Response SDP has audio: {has_response_audio}")
                
                if not has_response_audio:
                    logger.error("❌ OpenAI returned SDP without audio!")
                    
                logger.info("✅ SDP exchange completed successfully")
                
            except Exception as e:
                logger.error(f"❌ SDP exchange error: {e}")
                if hasattr(e, 'response'):
                    logger.error(f"Response text: {e.response.text}")
                raise

        return Response(
            content=sdp_response.content,
            status_code=200,
            media_type='application/sdp'
        )

    except httpx.HTTPStatusError as e:
        logger.error(f"❌ HTTP error: {e.response.status_code} - {e.response.text}")
        return Response(f"Upstream API error: {e.response.text}", status_code=e.response.status_code)
    except Exception as e:
        logger.error(f"❌ Internal error: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response(f"Internal error: {str(e)}", status_code=500)

async def get_ephemeral_token(client: httpx.AsyncClient, config: SessionConfig) -> dict:
    """Enhanced token fetching with better error handling"""
    token_headers = {"Authorization": f"Bearer {OPENAI_API_KEY}"}
    token_payload = {
        "model": MODEL_ID, 
        "voice": VOICE, 
        "instructions": config.instructions,
        "tools": config.tools,
        "tool_choice": "auto",
        "temperature": 0.8,
        "modalities": ["text", "audio"]
    }
    
    logger.info("Requesting ephemeral token from OpenAI...")
    logger.info(f"Token payload tools count: {len(config.tools)}")
    logger.info(f"Token payload instructions length: {len(config.instructions)}")
    
    try:
        token_response = await client.post(
            OPENAI_SESSION_URL, 
            headers=token_headers, 
            json=token_payload,
            timeout=20.0
        )
        
        logger.info(f"Token response status: {token_response.status_code}")
        
        if token_response.status_code != 200:
            logger.error(f"Token request failed: {token_response.text}")
            token_response.raise_for_status()
        
        token_data = token_response.json()
        
        if not token_data.get('client_secret', {}).get('value'):
            logger.error("Empty token in response")
            raise ValueError("Ephemeral token is empty or not found in the response.")
            
        logger.info("✅ Ephemeral token obtained successfully")
        return token_data
        
    except Exception as e:
        logger.error(f"Token fetch exception: {e}")
        raise
    
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)