import asyncio
import json
import logging
from fastapi import FastAPI, Request, Response, Depends
import httpx
import asyncpg
from aiortc import RTCPeerConnection, RTCSessionDescription, RTCDataChannel
from aiortc.rtcdtlstransport import RTCCertificate
from aiortc.contrib.media import MediaPlayer, MediaRecorder
from aiortc.rtcrtpsender import RTCRtpSender
from typing import Dict, Any, Optional

import os
import json
import asyncio
import asyncpg
import logging
import requests
from typing import Optional, Dict, List, Any
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
import httpx
from pydantic import BaseModel
from dotenv import load_dotenv

from prompts import get_voice_instruct, get_org_instruct
from app import fetch_session_config_from_db

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration - Load from environment variables
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY environment variable not set.")
    raise EnvironmentError("OPENAI_API_KEY environment variable not set.")

OPENAI_SESSION_URL = os.environ.get('OPENAI_SESSION_URL')
OPENAI_API_URL = os.environ.get('OPENAI_API_URL')
MODEL_ID = os.environ.get('MODEL')
VOICE = os.environ.get('VOICE')
# DEFAULT_INSTRUCTIONS = os.environ.get('DEFAULT_INSTRUCTIONS', 'You are a helpful AI assistant and have some tools installed. Converse in the Urdu language, use simple vocabulary and speak clearly. Talk quickly, but remain friendly and stay on topic at all times.')
PORT = int(os.environ.get('PORT', 5055))

# Database configuration
DB_CONFIG = {
    'user': os.getenv('user', 'chatbot_user'),
    'password': os.getenv('password', 'ChatBot@123'),
    'database': os.getenv('dbname', 'call_centre_analytics'),
    'host': os.getenv('host', 'najoomi.clw2kwwaavrk.us-east-1.rds.amazonaws.com'),
    'port': os.getenv('port', '5432'),
    'min_size': 1,
    'max_size': 10
}

# Global database pool
db_pool = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan context manager for database connection pool management."""
    global db_pool
    logger.info("Application startup: Initializing database connection pool...")
    try:
        db_pool = await asyncpg.create_pool(**DB_CONFIG)
        logger.info("Database connection pool created successfully")
        yield
    except Exception as e:
        logger.error(f"Failed to create database pool: {e}")
        raise
    finally:
        logger.info("Application shutdown: Closing database connection pool...")
        if db_pool:
            await db_pool.close()

# Log configuration (without sensitive data)
logger.info("Configuration loaded: AI HANDLER")
logger.info(f"  OPENAI_SESSION_URL: {OPENAI_SESSION_URL}")
logger.info(f"  OPENAI_API_URL: {OPENAI_API_URL}")
logger.info(f"  MODEL: {MODEL_ID}")
logger.info(f"  VOICE: {VOICE}")
logger.info(f"  PORT: {PORT}")
logger.info(f"  OPENAI_API_KEY: {'*' * (len(OPENAI_API_KEY) - 8) + OPENAI_API_KEY[-8:] if OPENAI_API_KEY else 'Not set'}")

# FastAPI app instance
app = FastAPI(
    title="Integrated WebRTC Backend",
    description="WebRTC backend with session configuration from database",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class SessionConfig(BaseModel):
    tools: List[Dict[str, Any]]
    instructions: str
    api_links: Dict[str, List[str]]
    modalities: List[str] = ["text", "audio"]

class FunctionTool(BaseModel):
    type: str = "function"
    name: str
    description: str
    parameters: Dict[str, Any]

# Dependency to get database pool
async def get_pool() -> asyncpg.Pool:
    """Get the database connection pool."""
    if db_pool is None:
        raise HTTPException(
            status_code=503,
            detail="Database service is currently unavailable"
        )
    return db_pool

import asyncio
import json
import logging
import uuid
import time
from typing import Dict, Any, Optional
import httpx
from fastapi import Request, Depends
from fastapi.responses import Response
import asyncpg

# aiortc imports
from aiortc import RTCPeerConnection, RTCSessionDescription, RTCDataChannel
from aiortc.rtcrtpsender import RTCRtpSender
from aiortc.rtcrtpreceiver import RTCRtpReceiver
from aiortc.mediastreams import MediaStreamTrack

logger = logging.getLogger(__name__)

# Store active connections
active_connections: Dict[str, Dict[str, Any]] = {}

class AudioRelayTrack(MediaStreamTrack):
    """
    Custom audio track that relays audio between peer connections
    """
    kind = "audio"

    def __init__(self, source_track, connection_name):
        super().__init__()
        self.source_track = source_track
        self.connection_name = connection_name
        self.frame_count = 0
        self.last_log_time = 0

    async def recv(self):
        """Receive and relay audio frames"""
        try:
            frame = await self.source_track.recv()
            self.frame_count += 1
            
            # Log audio flow every 5 seconds
            current_time = time.time()
            if current_time - self.last_log_time > 5:
                logger.info(f"Audio relay {self.connection_name}: {self.frame_count} frames processed")
                self.last_log_time = current_time
                
            return frame
        except Exception as e:
            logger.error(f"Error in audio relay {self.connection_name}: {e}")
            raise

class OpenAIRealtimeSession:
    """Handles OpenAI Realtime API integration via data channel"""
    
    def __init__(self, peer_connection: RTCPeerConnection, organisation_id: str, config):
        self.pc = peer_connection
        self.organisation_id = organisation_id
        self.config = config
        self.data_channel: Optional[RTCDataChannel] = None
        self.session_id = str(uuid.uuid4())
        
    async def setup_data_channel(self):
        """Create data channel for OpenAI communication"""
        try:
            # Create data channel for sending events to OpenAI
            self.data_channel = self.pc.createDataChannel("openai-events", ordered=True)
            
            @self.data_channel.on("open")
            async def on_open():
                logger.info(f"Data channel opened for session {self.session_id}")
                # Send initial session configuration
                await self.configure_session()
            
            @self.data_channel.on("message")
            async def on_message(message):
                logger.info(f"Received message from OpenAI: {message}")
                try:
                    event = json.loads(message)
                    await self.handle_openai_event(event)
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON from OpenAI: {message}")
                    
        except Exception as e:
            logger.error(f"Error setting up data channel: {e}")
    
    async def configure_session(self):
        """Send session configuration to OpenAI"""
        if not self.data_channel or self.data_channel.readyState != "open":
            logger.warning("Data channel not ready for configuration")
            return
            
        session_config = {
            "type": "session.update",
            "session": {
                "modalities": ["text", "audio"],
                "instructions": self.config.instructions if self.config else "You are a helpful assistant",
                "voice": "ash",
                "input_audio_format": "pcm16",
                "output_audio_format": "pcm16",
                "tools": self.config.tools if self.config and self.config.tools else [],
                "tool_choice": "auto"
            }
        }
        
        try:
            self.data_channel.send(json.dumps(session_config))
            logger.info("Session configuration sent to OpenAI")
        except Exception as e:
            logger.error(f"Error sending session config: {e}")
    
    async def handle_openai_event(self, event: Dict[str, Any]):
        """Handle events from OpenAI Realtime API"""
        event_type = event.get("type")
        logger.info(f"OpenAI event: {event_type}")
        
        if event_type == "response.function_call_arguments.done":
            await self.handle_function_call(event)
        elif event_type == "session.created":
            logger.info("OpenAI session created successfully")
        elif event_type == "error":
            logger.error(f"OpenAI error: {event.get('error')}")
    
    async def handle_function_call(self, event: Dict[str, Any]):
        """Execute function call and send result back"""
        try:
            call_id = event.get("call_id")
            function_name = event.get("name")
            arguments_str = event.get("arguments", "{}")
            
            logger.info(f"Function call: {function_name} (call_id: {call_id})")
            
            # Parse arguments
            try:
                arguments = json.loads(arguments_str)
            except json.JSONDecodeError:
                arguments = {}
            
            # Execute the function
            result = await self.execute_function(function_name, arguments)
            
            # Send result back to OpenAI
            function_result = {
                "type": "conversation.item.create",
                "item": {
                    "type": "function_call_output",
                    "call_id": call_id,
                    "output": json.dumps(result)
                }
            }
            
            if self.data_channel and self.data_channel.readyState == "open":
                self.data_channel.send(json.dumps(function_result))
                
                # Trigger response generation
                response_create = {"type": "response.create"}
                self.data_channel.send(json.dumps(response_create))
                
                logger.info(f"Function result sent for {function_name}")
            
        except Exception as e:
            logger.error(f"Error handling function call: {e}")
    
    async def execute_function(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the actual function call"""
        try:
            if not self.config or function_name not in self.config.api_links:
                return {"error": f"Function {function_name} not configured"}
            
            api_url, method = self.config.api_links[function_name]
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                if method.upper() == "GET":
                    response = await client.get(api_url, params=arguments)
                else:
                    response = await client.post(api_url, json=arguments)
                
                response.raise_for_status()
                
                try:
                    return response.json()
                except:
                    return {"data": response.text}
                    
        except Exception as e:
            logger.error(f"Function execution error: {e}")
            return {"error": f"Function failed: {str(e)}"}

@app.post("/api/rtc-connect")
async def connect_rtc(
    request: Request,
    organisation_id: str,
    pool: asyncpg.Pool = Depends(get_pool)
):
    """
    Handles the full B2BUA logic with enhanced audio debugging
    """
    try:
        # 1. Receive the initial offer from WhatsApp
        offer_from_whatsapp_sdp = (await request.body()).decode('utf-8')
        logger.info(f"Received SDP offer from WhatsApp: {len(offer_from_whatsapp_sdp)} characters")
        
        offer_from_whatsapp = RTCSessionDescription(sdp=offer_from_whatsapp_sdp, type="offer")

        # 2. Create two separate peer connections
        pc_whatsapp = RTCPeerConnection()
        pc_openai = RTCPeerConnection()

        # Store connections to keep them alive
        connection_id = f"{organisation_id}_{int(time.time())}"
        active_connections[connection_id] = {
            "whatsapp": pc_whatsapp, 
            "openai": pc_openai,
            "session_id": connection_id,
            "audio_stats": {
                "whatsapp_tracks_received": 0,
                "openai_tracks_received": 0,
                "whatsapp_tracks_added": 0,
                "openai_tracks_added": 0
            }
        }
        
        # 3. Setup session manager on the OpenAI connection
        config = await fetch_session_config_from_db(pool, organisation_id)
        openai_session = OpenAIRealtimeSession(pc_openai, organisation_id, config)
        
        # Store the session for cleanup
        active_connections[connection_id]["openai_session"] = openai_session
        
        # Setup data channel for OpenAI communication
        await openai_session.setup_data_channel()

        # 4. Enhanced media track bridging with debugging
        @pc_whatsapp.on("track")
        async def on_whatsapp_track(track):
            logger.info(f"🎵 Audio track from WhatsApp: kind={track.kind}, id={track.id}")
            active_connections[connection_id]["audio_stats"]["whatsapp_tracks_received"] += 1
            
            if track.kind == "audio":
                # Create a relay track that will forward audio to OpenAI
                relay_track = AudioRelayTrack(track, f"WhatsApp->OpenAI ({connection_id})")
                pc_openai.addTrack(relay_track)
                active_connections[connection_id]["audio_stats"]["openai_tracks_added"] += 1
                logger.info(f"✅ Audio track from WhatsApp forwarded to OpenAI (relay created)")
            else:
                logger.warning(f"⚠️  Non-audio track received from WhatsApp: {track.kind}")

        @pc_openai.on("track")
        async def on_openai_track(track):
            logger.info(f"🎵 Audio track from OpenAI: kind={track.kind}, id={track.id}")
            active_connections[connection_id]["audio_stats"]["openai_tracks_received"] += 1
            
            if track.kind == "audio":
                # Create a relay track that will forward audio to WhatsApp
                relay_track = AudioRelayTrack(track, f"OpenAI->WhatsApp ({connection_id})")
                pc_whatsapp.addTrack(relay_track)
                active_connections[connection_id]["audio_stats"]["whatsapp_tracks_added"] += 1
                logger.info(f"✅ Audio track from OpenAI forwarded to WhatsApp (relay created)")
            else:
                logger.warning(f"⚠️  Non-audio track received from OpenAI: {track.kind}")

        # 5. Enhanced connection state monitoring
        @pc_whatsapp.on("connectionstatechange")
        async def on_whatsapp_connection_state():
            state = pc_whatsapp.connectionState
            logger.info(f"📱 WhatsApp connection state: {state}")
            if state == "failed":
                logger.error("❌ WhatsApp connection failed!")
                await cleanup_connection(connection_id)
            elif state == "connected":
                logger.info("✅ WhatsApp connection established successfully")

        @pc_openai.on("connectionstatechange")
        async def on_openai_connection_state():
            state = pc_openai.connectionState
            logger.info(f"🤖 OpenAI connection state: {state}")
            if state == "failed":
                logger.error("❌ OpenAI connection failed!")
                await cleanup_connection(connection_id)
            elif state == "connected":
                logger.info("✅ OpenAI connection established successfully")

        # 6. Additional debugging event handlers
        @pc_whatsapp.on("datachannel")
        async def on_whatsapp_datachannel(channel):
            logger.info(f"📡 WhatsApp data channel created: {channel.label}")

        @pc_openai.on("datachannel")
        async def on_openai_datachannel(channel):
            logger.info(f"📡 OpenAI data channel created: {channel.label}")

        # 7. Set WhatsApp's offer on the appropriate peer connection
        await pc_whatsapp.setRemoteDescription(offer_from_whatsapp)
        logger.info("✅ WhatsApp offer set as remote description")
        
        # 8. Perform the OpenAI exchange
        async with httpx.AsyncClient(timeout=60.0) as client:
            # Get the token
            token_data = await get_ephemeral_token(client, config)
            ephemeral_token = token_data['client_secret']['value']
            logger.info("🔑 Ephemeral token obtained from OpenAI")

            # Create offer for OpenAI
            offer_for_openai = await pc_openai.createOffer()
            await pc_openai.setLocalDescription(offer_for_openai)
            logger.info("📤 Created offer for OpenAI")

            # Send offer to OpenAI
            sdp_headers = { 
                "Authorization": f"Bearer {ephemeral_token}", 
                "Content-Type": "application/sdp" 
            }
            sdp_params = { 
                "model": MODEL_ID, 
                "voice": "ash"
            }
            
            logger.info("📡 Sending SDP offer to OpenAI Realtime API")
            sdp_response = await client.post(
                OPENAI_API_URL, 
                headers=sdp_headers, 
                params=sdp_params, 
                content=pc_openai.localDescription.sdp,
                timeout=30.0
            )
            sdp_response.raise_for_status()
            
            # Process OpenAI's answer
            answer_from_openai_sdp = sdp_response.text
            logger.info(f"📥 Received SDP answer from OpenAI: {len(answer_from_openai_sdp)} characters")
            
            answer_from_openai = RTCSessionDescription(sdp=answer_from_openai_sdp, type="answer")
            await pc_openai.setRemoteDescription(answer_from_openai)
            logger.info("✅ OpenAI answer set as remote description")

            # 9. Create answer for WhatsApp
            answer_for_whatsapp = await pc_whatsapp.createAnswer()
            await pc_whatsapp.setLocalDescription(answer_for_whatsapp)
            logger.info("📤 Created answer for WhatsApp")

            # 10. Return answer to WhatsApp
            logger.info(f"📱 Returning SDP answer to WhatsApp: {len(pc_whatsapp.localDescription.sdp)} characters")
            
            # Log audio stats
            stats = active_connections[connection_id]["audio_stats"]
            logger.info(f"🎵 Audio setup stats: WA_tracks_rx={stats['whatsapp_tracks_received']}, "
                       f"AI_tracks_rx={stats['openai_tracks_received']}, "
                       f"WA_tracks_added={stats['whatsapp_tracks_added']}, "
                       f"AI_tracks_added={stats['openai_tracks_added']}")
            
            return Response(
                content=pc_whatsapp.localDescription.sdp,
                status_code=200,
                media_type='application/sdp'
            )
        
    except httpx.HTTPStatusError as e:
        logger.error(f"❌ HTTP error during OpenAI SDP exchange: {e.response.status_code} - {e.response.text}")
        await cleanup_connection_if_exists(connection_id)
        return Response(f"OpenAI API error: {e.response.text}", status_code=e.response.status_code)
    except Exception as e:
        logger.exception("❌ An error occurred during the RTC connection process")
        await cleanup_connection_if_exists(connection_id)
        return Response(f"An internal error occurred: {str(e)}", status_code=500)

async def cleanup_connection(connection_id: str):
    """Clean up peer connections and associated resources"""
    if connection_id in active_connections:
        logger.info(f"🧹 Cleaning up connection: {connection_id}")
        
        connection_data = active_connections[connection_id]
        
        # Log final stats
        if "audio_stats" in connection_data:
            stats = connection_data["audio_stats"]
            logger.info(f"📊 Final audio stats for {connection_id}: {stats}")
        
        # Close peer connections
        try:
            if "whatsapp" in connection_data:
                await connection_data["whatsapp"].close()
            if "openai" in connection_data:
                await connection_data["openai"].close()
        except Exception as e:
            logger.warning(f"⚠️  Error closing peer connections: {e}")
        
        # Remove from active connections
        del active_connections[connection_id]
        logger.info(f"✅ Connection {connection_id} cleaned up")

async def cleanup_connection_if_exists(connection_id: str):
    """Safely cleanup connection if it exists"""
    try:
        if connection_id in active_connections:
            await cleanup_connection(connection_id)
    except Exception as e:
        logger.warning(f"⚠️  Error during connection cleanup: {e}")

# Enhanced monitoring endpoint
@app.get("/api/connections")
async def get_active_connections():
    """Get detailed information about active connections"""
    connections_info = {}
    
    for conn_id, data in active_connections.items():
        connections_info[conn_id] = {
            "whatsapp_state": data["whatsapp"].connectionState if "whatsapp" in data else "unknown",
            "openai_state": data["openai"].connectionState if "openai" in data else "unknown",
            "session_id": data.get("session_id", "unknown"),
            "audio_stats": data.get("audio_stats", {}),
            "uptime": time.time() - int(conn_id.split('_')[-1]) if '_' in conn_id else 0
        }
    
    return {
        "active_connections": len(active_connections),
        "connections": connections_info
    }

# Enhanced get_ephemeral_token function
async def get_ephemeral_token(client: httpx.AsyncClient, config):
    """Get ephemeral token from OpenAI"""
    token_headers = {
        "Authorization": f"Bearer {OPENAI_API_KEY}",
        "Content-Type": "application/json"
    }
    
    token_payload = {
        "model": MODEL_ID,
        "voice": "ash"
    }
    
    # Include tools and instructions in session creation
    if config:
        if config.tools:
            token_payload["tools"] = config.tools
            logger.info(f"🔧 Including {len(config.tools)} tools in session creation")
        if config.instructions:
            token_payload["instructions"] = config.instructions
            logger.info("📝 Including custom instructions in session creation")
    
    logger.info("🔑 Requesting ephemeral token from OpenAI")
    response = await client.post(
        "https://api.openai.com/v1/realtime/sessions",
        headers=token_headers,
        json=token_payload,
        timeout=30.0
    )
    response.raise_for_status()
    
    logger.info("✅ Ephemeral token obtained successfully")
    return response.json()
    
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)