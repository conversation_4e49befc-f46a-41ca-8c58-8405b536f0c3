def fix_openai_sdp(sdp_str: str) -> str:
    """
    Parses an SDP string to find a valid IP from ICE candidates and uses it
    to replace an invalid 0.0.0.0 connection IP.
    """
    lines = sdp_str.splitlines()
    first_candidate_ip = None

    # First, find a valid IP address from the first available ICE candidate
    for line in lines:
        if line.startswith("a=candidate:"):
            parts = line.split()
            if len(parts) > 4:
                # The IP address is the 5th element in the candidate line
                ip = parts[4]
                # A simple check to ensure it's a plausible IP and not a hostname
                if '.' in ip and not ip.endswith('.local'):
                    first_candidate_ip = ip
                    logger.info(f"🔧 Found valid candidate IP to use: {first_candidate_ip}")
                    break
    
    # If we found a valid IP, rebuild the SDP with the corrected c= line
    if first_candidate_ip:
        corrected_lines = []
        for line in lines:
            if line.strip() == "c=IN IP4 0.0.0.0":
                corrected_line = f"c=IN IP4 {first_candidate_ip}"
                corrected_lines.append(corrected_line)
                logger.info(f"✅ Corrected invalid SDP line to: {corrected_line}")
            else:
                corrected_lines.append(line)
        # Join with \r\n for proper SDP formatting
        return "\r\n".join(corrected_lines)
    else:
        # If no candidate IP was found, return the original SDP to avoid further errors
        logger.warning("⚠️  Could not find a candidate IP in OpenAI's SDP. Returning original.")
        return sdp_str