# Use official Node.js v22.11.0 image
FROM node:22.11.0

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Build the app
RUN npm run build

# Set environment variable for Vite preview port
ENV PORT=3000

# Expose port 3000
EXPOSE 3000

# Run the Vite preview server
CMD ["npm", "run", "preview", "--", "--port", "3000", "--host"] 