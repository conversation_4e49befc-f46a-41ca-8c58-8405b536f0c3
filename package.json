{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.4", "@langchain/pinecone": "^0.1.0", "@material-tailwind/react": "^2.0.5", "@mui/material": "^7.1.1", "@mui/x-date-pickers": "^8.5.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.20.5", "amazon-cognito-identity-js": "^6.3.15", "axios": "^1.6.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "date-fns": "^3.3.1", "embla-carousel-react": "^8.0.0", "input-otp": "^1.1.0", "jwt-decode": "^4.0.0", "langchain": "^0.1.17", "lottie-react": "^2.4.1", "lovable-tagger": "^1.1.8", "lucide-react": "^0.331.0", "next-themes": "^0.2.1", "react": "^18.3.1", "react-audio-player": "^0.17.0", "react-day-picker": "^8.10.0", "react-dom": "^18.3.1", "react-hook-form": "^7.50.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.0.9", "react-router-dom": "^6.22.1", "react-spinners": "^0.17.0", "recharts": "^2.12.0", "sonner": "^1.4.0", "tailwind-merge": "^1.1.1", "vaul": "^0.9.0", "wav-encoder": "^0.3.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.17", "buffer": "^6.0.3", "next-tick": "^1.1.0", "postcss": "^8.4.35", "postcss-import": "^16.0.1", "postcss-nesting": "^12.0.2", "process": "^0.11.10", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "vite": "^5.1.3"}}