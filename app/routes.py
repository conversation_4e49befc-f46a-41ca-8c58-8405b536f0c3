# API endpoints
from flask import Blueprint, current_app, jsonify, request
from app.latency_analysis import calculate_latency_for_conversations
from app.query_resolution_time import calculate_query_resolution_time_per_conversation
from app.extract_event_types import extract_event_types
from app.conversation_time import calculate_conversation_time
from app.llm_service import evaluate_conversation_unified
from app.db import insert_event_data_into_postgres 
from app.function_calling import extract_function_calls
from app.db import store_function_calls_pg

api_blueprint = Blueprint('api', __name__)

@api_blueprint.route('/endpoint', methods=['POST'])
def session_summary_combined():
    try:
        data = request.get_json()
        session_id = data.get("session_id")

        
        if not session_id:
            return jsonify({"error": "Missing session_id"}), 400
        print(f'Received data with Session ID: {session_id}')

        # MongoDB connection
        mongo_collection = current_app.db["history"]
        documents = list(mongo_collection.find({"session_id": session_id}))
        if not documents:
            return jsonify({"error": "Session not found"}), 500

        # -------------------- FUNCTION CALL EXTRACTION --------------------
        all_function_calls = []
        for doc in documents:
            doc_id = str(doc.get("_id"))
            doc["_id"] = doc_id
            calls = extract_function_calls(doc)
            for call in calls:
                call["document_id"] = doc_id
            all_function_calls.extend(calls)
        if all_function_calls:
            try:
                store_function_calls_pg(all_function_calls)
            except Exception as e:
                current_app.logger.error(f"PostgreSQL insert error (function calls): {str(e)}")
                return jsonify({"error": f"Failed to store function calls: {str(e)}"}), 500

        # -------------------- EVENT TYPE + METRICS EXTRACTION --------------------
        base_doc = documents[0]
        events = extract_event_types(base_doc)  # now returns (doc_id, event_type, start_time, end_time, bot_name)
        conversation_time = calculate_conversation_time(base_doc)
        latency = calculate_latency_for_conversations(session_id)
        llm_results = evaluate_conversation_unified(session_id)
        # print("....................................................")
        # print("llm_results:", llm_results)
        # print(".....................................................")
        query_resolution_time_results = calculate_query_resolution_time_per_conversation(mongo_collection, session_id)

        combined_json = {
            "event_types": {
                "data": events
            },
            "evaluate_results": llm_results,
            "latency_results": latency,
            "query_resolution_time": query_resolution_time_results
        }
        print("combined JSON:", combined_json)
        try:
            insert_event_data_into_postgres(combined_json)
        except Exception as e:
            current_app.logger.error(f"PostgreSQL insert error (event types): {str(e)}")
            return jsonify({"error": f"Failed to store event data: {str(e)}"}), 500

        print("stored into postgres")

        response = {
            "session_id": session_id,
            "function_calls": all_function_calls,
            "event_types": events,
            "conversation_time_summary": conversation_time,
            "latency_summary": latency,
            "llm_evaluation": llm_results,
            "query_resolution_time": query_resolution_time_results
        }
        return jsonify(response), 200

    except Exception as e:
        current_app.logger.error(f"Error in /session-summary: {str(e)}")
        return jsonify({"error": str(e)}), 404
