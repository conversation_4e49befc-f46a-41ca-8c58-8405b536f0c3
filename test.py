#!/usr/bin/env python3
"""
Quick script to test if OpenAI Realtime API is returning valid SDP
"""
import asyncio
import httpx
import os
from dotenv import load_dotenv

load_dotenv()

# Your config
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
OPENAI_SESSION_URL = os.environ.get('OPENAI_SESSION_URL')  
OPENAI_API_URL = os.environ.get('OPENAI_API_URL')
MODEL_ID = os.environ.get('MODEL')
VOICE = os.environ.get('VOICE')

# Test SDP from your WhatsApp logs
WHATSAPP_SDP = """v=0
o=- 1753783956576 2 IN IP4 127.0.0.1
s=-
t=0 0
a=group:BUNDLE audio
a=msid-semantic: WMS fe9cc65a-d637-42e0-8c48-36e40ccc3ce2
a=ice-lite
m=audio 3480 UDP/TLS/RTP/SAVPF 111 126
c=IN IP4 **************
a=rtcp:9 IN IP4 0.0.0.0
a=candidate:1071287312 1 udp 2122260223 ************** 3480 typ host generation 0 network-cost 50
a=candidate:3369381842 1 udp 2122262783 2a03:2880:f267:c4:face:b00c:0:699c 3480 typ host generation 0 network-cost 50
a=ice-ufrag:oKvWNHdRIrxSKCtN
a=ice-pwd:Tpqqk0/kgQdxi8c/qeZoTA==
a=fingerprint:sha-256 AE:E4:C7:15:9C:48:E0:BC:07:D8:AA:26:62:0E:7F:36:3C:07:E8:68:13:0B:9F:AB:E8:14:A3:E0:1F:C1:55:B7
a=setup:actpass
a=mid:audio
a=sendrecv
a=msid:fe9cc65a-d637-42e0-8c48-36e40ccc3ce2 WhatsAppTrack1
a=rtcp-mux
a=rtpmap:111 opus/48000/2
a=rtcp-fb:111 transport-cc
a=fmtp:111 maxaveragebitrate=20000;maxplaybackrate=16000;minptime=20;sprop-maxcapturerate=16000;useinbandfec=1
a=rtpmap:126 telephone-event/8000
a=maxptime:20
a=ptime:20
a=ssrc:1904628923 cname:WhatsAppAudioStream1
"""

async def test_openai_sdp():
    """Test OpenAI SDP exchange with actual WhatsApp SDP"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🔍 Testing OpenAI Realtime API SDP Exchange...")
            print("=" * 60)
            
            # Step 1: Get ephemeral token
            print("1️⃣ Getting ephemeral token...")
            token_headers = {"Authorization": f"Bearer {OPENAI_API_KEY}"}
            token_payload = {
                "model": MODEL_ID,
                "voice": VOICE,
                "instructions": "You are a helpful assistant.",
                "modalities": ["text", "audio"],
                "temperature": 0.8
            }
            
            token_response = await client.post(
                OPENAI_SESSION_URL, 
                headers=token_headers, 
                json=token_payload
            )
            
            print(f"   Status: {token_response.status_code}")
            if token_response.status_code != 200:
                print(f"   ❌ Token Error: {token_response.text}")
                return False
                
            token_data = token_response.json()
            ephemeral_token = token_data['client_secret']['value']
            print(f"   ✅ Token obtained (length: {len(ephemeral_token)})")
            
            # Step 2: Send WhatsApp SDP to OpenAI
            print("\n2️⃣ Sending WhatsApp SDP to OpenAI...")
            sdp_headers = {
                "Authorization": f"Bearer {ephemeral_token}",
                "Content-Type": "application/sdp"
            }
            
            sdp_params = {
                "model": MODEL_ID,
                "voice": VOICE
            }
            
            sdp_response = await client.post(
                OPENAI_API_URL,
                headers=sdp_headers,
                params=sdp_params,
                content=WHATSAPP_SDP
            )
            
            print(f"   SDP Status: {sdp_response.status_code}")
            
            if sdp_response.status_code != 200:
                print(f"   ❌ SDP Error: {sdp_response.text}")
                # return False
                
            # Step 3: Analyze response SDP
            print("\n3️⃣ Analyzing OpenAI SDP Response...")
            response_sdp = sdp_response.text
            
            print(f"   Response length: {len(response_sdp)}")
            
            # Check for the problematic connection line
            connection_lines = [line for line in response_sdp.split('\n') if line.startswith('c=')]
            print(f"   Connection lines found: {len(connection_lines)}")
            
            has_invalid_ip = False
            for line in connection_lines:
                print(f"   📡 {line}")
                if '0.0.0.0' in line:
                    has_invalid_ip = True
                    print("   ⚠️  WARNING: Invalid 0.0.0.0 IP detected!")
            
            # Check for audio
            audio_lines = [line for line in response_sdp.split('\n') if line.startswith('m=audio')]
            print(f"   Audio lines found: {len(audio_lines)}")
            for line in audio_lines:
                print(f"   🔊 {line}")
            
            print("\n" + "=" * 60)
            
            if has_invalid_ip:
                print("❌ ISSUE CONFIRMED: OpenAI is returning invalid SDP with 0.0.0.0")
                print("   This is why audio doesn't work despite 200 responses.")
                print("   This appears to be an OpenAI service issue.")
                return False
            else:
                print("✅ SDP looks valid - the issue might be elsewhere")
                return True
                
    except Exception as e:
        print(f"❌ Exception during test: {e}")
        return False

if __name__ == "__main__":
    print("OpenAI Realtime API SDP Validation Test")
    print("Testing with actual WhatsApp SDP from your logs...")
    print()
    
    result = asyncio.run(test_openai_sdp())
    
    print("\n" + "="*60)
    if not result:
        print("🚨 DIAGNOSIS: OpenAI Realtime API is returning invalid SDP")
        print("   • All API calls succeed (200 responses)")  
        print("   • But connection IP is 0.0.0.0 (invalid)")
        print("   • This prevents actual audio media flow")
        print("   • Likely an OpenAI service-side issue")
        print("\n🔧 RECOMMENDED ACTIONS:")
        print("   1. Check OpenAI status page")
        print("   2. Try again in a few hours")
        print("   3. Contact OpenAI support if persists")
        print("   4. Monitor OpenAI developer community for similar reports")
    else:
        print("✅ OpenAI SDP looks valid - investigate other causes")