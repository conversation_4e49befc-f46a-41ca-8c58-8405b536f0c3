#!/usr/bin/env python3
"""
Integrated WebRTC Backend with Session Configuration API
Combines WebRTC functionality with database-driven session configuration
"""
import os
import json
import asyncio
import asyncpg
import logging
import requests
from typing import Optional, Dict, List, Any
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
import httpx
from pydantic import BaseModel
from dotenv import load_dotenv

from prompts import get_voice_instruct, get_org_instruct

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration - Load from environment variables
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY environment variable not set.")
    raise EnvironmentError("OPENAI_API_KEY environment variable not set.")

OPENAI_SESSION_URL = os.environ.get('OPENAI_SESSION_URL')
OPENAI_API_URL = os.environ.get('OPENAI_API_URL')
MODEL_ID = os.environ.get('MODEL')
VOICE = os.environ.get('VOICE')
# DEFAULT_INSTRUCTIONS = os.environ.get('DEFAULT_INSTRUCTIONS', 'You are a helpful AI assistant and have some tools installed. Converse in the Urdu language, use simple vocabulary and speak clearly. Talk quickly, but remain friendly and stay on topic at all times.')
PORT = int(os.environ.get('PORT', 5055))

# Database configuration
DB_CONFIG = {
    'user': os.getenv('user', 'chatbot_user'),
    'password': os.getenv('password', 'ChatBot@123'),
    'database': os.getenv('dbname', 'call_centre_analytics'),
    'host': os.getenv('host', 'najoomi.clw2kwwaavrk.us-east-1.rds.amazonaws.com'),
    'port': os.getenv('port', '5432'),
    'min_size': 1,
    'max_size': 10
}

# Global database pool
db_pool = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan context manager for database connection pool management."""
    global db_pool
    logger.info("Application startup: Initializing database connection pool...")
    try:
        db_pool = await asyncpg.create_pool(**DB_CONFIG)
        logger.info("Database connection pool created successfully")
        yield
    except Exception as e:
        logger.error(f"Failed to create database pool: {e}")
        raise
    finally:
        logger.info("Application shutdown: Closing database connection pool...")
        if db_pool:
            await db_pool.close()

# Log configuration (without sensitive data)
logger.info("Configuration loaded:")
logger.info(f"  OPENAI_SESSION_URL: {OPENAI_SESSION_URL}")
logger.info(f"  OPENAI_API_URL: {OPENAI_API_URL}")
logger.info(f"  MODEL: {MODEL_ID}")
logger.info(f"  VOICE: {VOICE}")
logger.info(f"  PORT: {PORT}")
logger.info(f"  OPENAI_API_KEY: {'*' * (len(OPENAI_API_KEY) - 8) + OPENAI_API_KEY[-8:] if OPENAI_API_KEY else 'Not set'}")

# FastAPI app instance
app = FastAPI(
    title="Integrated WebRTC Backend",
    description="WebRTC backend with session configuration from database",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class SessionConfig(BaseModel):
    tools: List[Dict[str, Any]]
    instructions: str
    api_links: Dict[str, List[str]]
    modalities: List[str] = ["text", "audio"]

class FunctionTool(BaseModel):
    type: str = "function"
    name: str
    description: str
    parameters: Dict[str, Any]

# Dependency to get database pool
async def get_pool() -> asyncpg.Pool:
    """Get the database connection pool."""
    if db_pool is None:
        raise HTTPException(
            status_code=503,
            detail="Database service is currently unavailable"
        )
    return db_pool

@app.get("/")
async def home():
    return {"message": "WebRTC Backend Running!", "status": "healthy"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "integrated-webrtc-backend"}

import asyncio
import httpx
from fastapi import Request, Response, Depends, FastAPI
# ... (other imports) ...

# ... (your app, logger, and config variables) ...

async def get_ephemeral_token(client: httpx.AsyncClient) -> dict:
    """Fetches the ephemeral token from OpenAI."""
    token_headers = {"Authorization": f"Bearer {OPENAI_API_KEY}"}
    token_payload = {"model": MODEL_ID, "voice": VOICE}
    
    logger.info("Requesting ephemeral token from OpenAI.")
    token_response = await client.post(OPENAI_SESSION_URL, headers=token_headers, json=token_payload)
    token_response.raise_for_status()
    
    token_data = token_response.json()
    if not token_data.get('client_secret', {}).get('value'):
        raise ValueError("Ephemeral token is empty or not found in the response.")
        
    logger.info("Ephemeral token obtained successfully.")
    return token_data

@app.post("/api/rtc-connect")
async def connect_rtc(
    request: Request,
    organisation_id: str,
    pool: asyncpg.Pool = Depends(get_pool)
):
    try:
        client_sdp_bytes = await request.body()
        if not client_sdp_bytes:
            return Response("No SDP provided in the request body.", status_code=400)
        client_sdp = client_sdp_bytes.decode('utf-8')

        async with httpx.AsyncClient() as client:

            logger.info("Starting concurrent fetch for session config and ephemeral token.")
            config = await fetch_session_config_from_db(pool, organisation_id)
            token_data = await get_ephemeral_token(client, config)

            ephemeral_token = token_data['client_secret']['value']

            sdp_headers = {
                "Authorization": f"Bearer {ephemeral_token}",
                "Content-Type": "application/sdp"
            }
            logger.info(f"Config tools: {len(config.tools)}")
            sdp_params = {
                "model": MODEL_ID,
                "voice": "ash"
            }
           

            logger.info("Sending SDP to OpenAI Realtime API.")
            sdp_response = await client.post(
                OPENAI_API_URL, 
                headers=sdp_headers, 
                params=sdp_params, 
                content=client_sdp
            )
            sdp_response.raise_for_status()
            logger.info("SDP exchange with OpenAI completed successfully.")

        # Return OpenAI's SDP response
        return Response(
            content=sdp_response.content,
            status_code=200,
            media_type='application/sdp'
        )

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
        return Response(f"An upstream API error occurred: {e.response.text}", status_code=e.response.status_code)
    except Exception as e:
        logger.exception("An error occurred during the RTC connection process.")
        return Response(f"An internal error occurred: {str(e)}", status_code=500)
    
async def get_ephemeral_token(client: httpx.AsyncClient, config: SessionConfig) -> dict:
    """Fetches the ephemeral token from OpenAI."""
    token_headers = {"Authorization": f"Bearer {OPENAI_API_KEY}"}
    token_payload = {"model": MODEL_ID, 
                     "voice": VOICE, 
                     "instructions": config.instructions,
                     "tools": config.tools,
                     "tool_choice": "auto",
                     "temperature": 0.8,
                     "modalities": ["text", "audio"]}
    
    logger.info("Requesting ephemeral token from OpenAI.")
    token_response = await client.post(OPENAI_SESSION_URL, headers=token_headers, json=token_payload)
    token_response.raise_for_status()
    
    token_data = token_response.json()
    if not token_data.get('client_secret', {}).get('value'):
        raise ValueError("Ephemeral token is empty or not found in the response.")
        
    logger.info("Ephemeral token obtained successfully.")
    return token_data

async def fetch_session_config_from_db(pool: asyncpg.Pool, organisation_id: str) -> Optional[SessionConfig]:
    """Fetch session configuration from database for given organisation_id."""
    try:
        async with pool.acquire() as conn:
            # Query to get function definitions and system prompts for the organisation
            query = """
                SELECT function_name, function_schema, system_prompt, api_link, method_type
                FROM chatbot.function_definitions
                WHERE organisation_id = $1
            """
            result = await conn.fetch(query, organisation_id)

            await conn.close()
            if not result:
                return None
            logger.info('Data received')
            all_base_urls, all_function_names, all_methods = [], [], []
            function_tools = result[0][1]
            prompt = result[0][2]
            for row in result:
                api_link = row['api_link']
                function_name = row['function_name']
                method = row['method_type']
                all_base_urls.append(api_link)
                all_function_names.append(function_name)
                all_methods.append(method)

            function_tools = json.loads(function_tools)

            # Convert to the realtime api's required format
            converted_tools = []

            for tool in function_tools:
                function_data = tool.get("function", {})
                converted_tools.append({
                    "type": "function",
                    "name": function_data.get("name"),
                    "description": function_data.get("description"),
                    "parameters": function_data.get("parameters")
                })

            api_links = {i:[j,k] for i, j, k in zip(all_function_names, all_base_urls, all_methods)}

            logger.info('Data processed')

            additional_prompt = await get_voice_instruct('urdu')
            org_prompt = await get_org_instruct(client_id=organisation_id)
            system_message = org_prompt + '\n' + prompt + '\n' + additional_prompt

            return SessionConfig(
                tools=converted_tools,
                instructions=system_message,
                api_links=api_links,
                modalities=["text", "audio"]
            )

    except Exception as e:
        logger.error(f"Database error while fetching session config: {e}")
        raise HTTPException(status_code=500, detail="Database error")

@app.get("/api/session-config/{organisation_id}", response_model=SessionConfig)
async def get_session_config(
    organisation_id: str,
    pool: asyncpg.Pool = Depends(get_pool)
) -> SessionConfig:
    """
    Get session configuration for a specific organisation.

    Args:
        organisation_id: The organisation identifier

    Returns:
        SessionConfig: Configuration including tools, instructions, and API links
    """
    logger.info(f"Fetching session config for organisation: {organisation_id}")

    config = await fetch_session_config_from_db(pool, organisation_id)

    if config is None:
        raise HTTPException(
            status_code=404,
            detail=f"No configuration found for organisation_id: {organisation_id}"
        )

    logger.info(f"Successfully retrieved {len(config.tools)} tools for organisation: {organisation_id}")
    return config

@app.post("/api/function-call/{organisation_id}/{function_name}")
async def proxy_function_call(
    organisation_id: str,
    function_name: str,
    request: Request,
    pool: asyncpg.Pool = Depends(get_pool)
):
    """
    Proxy function calls to external APIs to avoid CORS issues.

    Args:
        organisation_id: The organisation identifier
        function_name: The name of the function to call
        request: The request containing function parameters

    Returns:
        The response from the external API
    """
    logger.info(f"Proxying function call: {function_name} for organisation: {organisation_id}")

    try:
        # Get the session config to retrieve API links
        config = await fetch_session_config_from_db(pool, organisation_id)

        if config is None:
            raise HTTPException(
                status_code=404,
                detail=f"No configuration found for organisation_id: {organisation_id}"
            )

        # Check if the function has an API endpoint
        if function_name not in config.api_links:
            raise HTTPException(
                status_code=404,
                detail=f"No API endpoint found for function: {function_name}"
            )

        # Get the API endpoint details
        api_url, method = config.api_links[function_name]
        logger.info(f"Making {method} request to: {api_url}")

        # Get the request body (function parameters)
        try:
            parameters = await request.json()
        except Exception:
            parameters = {}

        # Make the external API call
        import aiohttp
        async with aiohttp.ClientSession() as session:
            if method.upper() == 'GET':
                # For GET requests, add parameters as query string
                params = {k: v for k, v in parameters.items() if v is not None}
                async with session.get(api_url, params=params) as response:
                    if response.content_type == 'application/json':
                        result = await response.json()
                    else:
                        result = await response.text()
            else:
                # For POST/PUT requests, send parameters in body
                async with session.request(
                    method.upper(),
                    api_url,
                    json=parameters,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    if response.content_type == 'application/json':
                        result = await response.json()
                    else:
                        result = await response.text()

        logger.info(f"External API call successful for function: {function_name}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error proxying function call {function_name}: {e}")
        raise HTTPException(

            
            status_code=500,
            detail=f"Failed to proxy function call: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)